#!/usr/bin/env python3
"""
测试LM Studio工具调用功能
"""
import asyncio
import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from autogen_agentchat.agents import AssistantAgent
from autogen_ext.tools.mcp import McpWorkbench
from autogen_agentchat.ui import Console
from mcp_registry import get_mcp_params
from LM_Studio import get_lmstudio_client

async def test_tool_calling():
    """测试工具调用功能"""
    try:
        print("🔧 测试LM Studio工具调用功能...")

        # 创建LM Studio客户端
        lm_studio_client = get_lmstudio_client("qwen3-32b")
        print(f"✅ LM Studio客户端创建成功: {lm_studio_client.model}")

        # 获取MCP参数
        params = get_mcp_params("mongo_chip")
        print(f"✅ MCP参数获取成功: {params.command}")

        # 创建MCP工作台和助手
        async with McpWorkbench(server_params=params) as workbench:
            print("✅ MCP工作台创建成功")

            # 注意：McpWorkbench没有get_tools方法，工具会自动注册到助手
            print("📋 MCP工具将自动注册到助手")

            assistant = AssistantAgent(
                name="Assistant",
                model_client=lm_studio_client,
                system_message="你是数据库助手，拥有数据库操作工具。请使用工具来回答用户问题。",
                workbench=workbench,
                reflect_on_tool_use=True,
            )
            print("✅ 助手创建成功")

            # 测试工具调用
            print("\n🚀 测试工具调用...")
            print("=" * 50)

            # 运行对话
            await Console(assistant.run_stream(task="请列出数据库中的所有集合"))

    except Exception as e:
        print(f"❌ 错误: {e}")
        import traceback
        traceback.print_exc()

async def test_direct_api_call():
    """直接测试API调用以验证工具格式"""
    try:
        print("\n🔍 直接测试API调用...")

        from autogen_core.models._types import UserMessage
        from autogen_core.tools._base import ToolSchema

        client = get_lmstudio_client("qwen3-32b")

        # 创建一个简单的测试工具
        test_tool = ToolSchema(
            name="get_weather",
            description="获取天气信息",
            parameters={
                "type": "object",
                "properties": {
                    "city": {
                        "type": "string",
                        "description": "城市名称"
                    }
                },
                "required": ["city"]
            }
        )

        messages = [UserMessage(content="北京今天天气怎么样？", source="user")]

        print("📤 发送带工具的请求...")
        result = await client.create(messages, tools=[test_tool])

        print(f"📥 响应类型: {type(result.content)}")
        print(f"📝 响应内容: {result.content}")
        print(f"🏁 结束原因: {result.finish_reason}")

        if result.finish_reason == "function_calls":
            print("🎉 工具调用成功!")
        else:
            print("⚠️  没有触发工具调用")

    except Exception as e:
        print(f"❌ 直接API测试失败: {e}")
        import traceback
        traceback.print_exc()

async def main():
    """主函数"""
    print("🚀 开始LM Studio工具调用测试\n")

    # 先测试直接API调用
    await test_direct_api_call()

    print("\n" + "="*60 + "\n")

    # 再测试完整的工具调用流程
    await test_tool_calling()

if __name__ == "__main__":
    asyncio.run(main())
