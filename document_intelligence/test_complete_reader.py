#!/usr/bin/env python3
"""
测试完整文件阅读智能体
"""
import asyncio
import sys
import os

sys.path.append(os.path.dirname(os.path.abspath(__file__)))
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from config.models_config import get_agent_model_client
from config.agents_config import AGENT_SYSTEM_PROMPTS, AGENT_CAPABILITIES
from agents.complete_file_reader import CompleteFileReader

async def test_complete_reading():
    """测试完整文件阅读"""
    print("📖 完整文件阅读智能体测试")
    print("=" * 50)
    
    # 初始化智能体
    print("🔧 初始化完整文件阅读智能体...")
    try:
        model_client = get_agent_model_client("DocumentReader")
        system_message = AGENT_SYSTEM_PROMPTS["DocumentReader"]
        capabilities = AGENT_CAPABILITIES["DocumentReader"]
        
        complete_reader = CompleteFileReader(model_client, system_message, capabilities)
        print("✅ 初始化完成")
        
    except Exception as e:
        print(f"❌ 初始化失败: {e}")
        return
    
    # 测试用例
    test_cases = [
        {
            "category": "配置文件完整阅读",
            "request": "完整阅读文件 config/agents_config.py 并总结所有智能体的配置信息",
            "description": "测试对Python配置文件的完整理解"
        },
        {
            "category": "技术文档完整阅读",
            "request": "完整阅读文件 ../full.md 并回答：LKS32MC08x有哪些主要功能模块？",
            "description": "测试对大型技术文档的完整阅读"
        },
        {
            "category": "代码文件完整阅读",
            "request": "完整阅读文件 agents/file_reader.py 并解释这个类的完整实现逻辑",
            "description": "测试对代码文件的完整理解"
        }
    ]
    
    print(f"\n🧪 开始完整阅读测试 ({len(test_cases)} 个用例):")
    
    for i, test_case in enumerate(test_cases, 1):
        print(f"\n📝 测试 {i}: {test_case['category']}")
        print(f"📋 请求: {test_case['request']}")
        print(f"📄 描述: {test_case['description']}")
        print("-" * 60)
        
        try:
            start_time = asyncio.get_event_loop().time()
            response = await complete_reader.handle_message(test_case['request'])
            end_time = asyncio.get_event_loop().time()
            
            print(f"📖 完整阅读结果:")
            print(response)
            print(f"\n⏱️  总耗时: {end_time - start_time:.2f}秒")
            
            # 分析结果质量
            print(f"\n📊 结果分析:")
            print(f"  • 回答长度: {len(response)} 字符")
            
            # 检查是否包含完整性指标
            if "阅读统计" in response:
                print("  • ✅ 包含阅读统计信息")
            if "覆盖率" in response:
                print("  • ✅ 包含覆盖率验证")
            if "分块数量" in response:
                print("  • ✅ 包含分块处理信息")
            if "完整性" in response:
                print("  • ✅ 包含完整性验证")
            
        except Exception as e:
            print(f"❌ 测试失败: {e}")
        
        print("\n" + "=" * 60)
        
        # 添加延时
        await asyncio.sleep(2)

async def interactive_complete_reading():
    """交互式完整阅读"""
    print("\n🎯 交互式完整文件阅读")
    print("使用格式: '完整阅读文件 <文件路径> 并回答 <问题>'")
    print("输入 'quit' 退出")
    print("-" * 50)
    
    # 初始化
    try:
        model_client = get_agent_model_client("DocumentReader")
        system_message = AGENT_SYSTEM_PROMPTS["DocumentReader"]
        capabilities = AGENT_CAPABILITIES["DocumentReader"]
        complete_reader = CompleteFileReader(model_client, system_message, capabilities)
    except Exception as e:
        print(f"❌ 初始化失败: {e}")
        return
    
    while True:
        try:
            request = input("\n📖 请输入完整阅读请求: ").strip()
            
            if request.lower() in ['quit', 'exit', 'q']:
                break
            
            if not request:
                continue
            
            if not request.startswith('完整阅读文件'):
                print("⚠️  请使用正确格式: '完整阅读文件 <文件路径> 并回答 <问题>'")
                continue
            
            print("📖 开始完整阅读...")
            response = await complete_reader.handle_message(request)
            print(f"\n📋 完整阅读结果:\n{response}")
            
        except KeyboardInterrupt:
            break
        except Exception as e:
            print(f"❌ 错误: {e}")
    
    print("👋 完整阅读测试结束")

async def compare_reading_methods():
    """对比不同阅读方法"""
    print("\n🔍 对比阅读方法测试")
    print("-" * 40)
    
    # 初始化两种阅读器
    model_client = get_agent_model_client("DocumentReader")
    system_message = AGENT_SYSTEM_PROMPTS["DocumentReader"]
    capabilities = AGENT_CAPABILITIES["DocumentReader"]
    
    # 导入普通文件阅读器
    from agents.file_reader import FileReader
    
    normal_reader = FileReader(model_client, system_message, capabilities)
    complete_reader = CompleteFileReader(model_client, system_message, capabilities)
    
    test_file = "config/models_config.py"
    test_question = "这个文件的主要功能是什么？"
    
    print(f"📁 测试文件: {test_file}")
    print(f"❓ 测试问题: {test_question}")
    
    # 普通阅读
    print(f"\n📄 普通文件阅读:")
    try:
        start_time = asyncio.get_event_loop().time()
        normal_response = await normal_reader.handle_message(f"读取文件 {test_file} 并回答：{test_question}")
        normal_time = asyncio.get_event_loop().time() - start_time
        
        print(f"⏱️  耗时: {normal_time:.2f}秒")
        print(f"📏 长度: {len(normal_response)} 字符")
        print(f"📝 内容: {normal_response[:200]}...")
        
    except Exception as e:
        print(f"❌ 普通阅读失败: {e}")
        normal_response = ""
        normal_time = 0
    
    # 完整阅读
    print(f"\n📖 完整文件阅读:")
    try:
        start_time = asyncio.get_event_loop().time()
        complete_response = await complete_reader.handle_message(f"完整阅读文件 {test_file} 并回答：{test_question}")
        complete_time = asyncio.get_event_loop().time() - start_time
        
        print(f"⏱️  耗时: {complete_time:.2f}秒")
        print(f"📏 长度: {len(complete_response)} 字符")
        print(f"📝 内容: {complete_response[:200]}...")
        
    except Exception as e:
        print(f"❌ 完整阅读失败: {e}")
        complete_response = ""
        complete_time = 0
    
    # 对比分析
    print(f"\n📊 对比分析:")
    if normal_response and complete_response:
        print(f"  • 时间对比: 普通阅读 {normal_time:.1f}s vs 完整阅读 {complete_time:.1f}s")
        print(f"  • 长度对比: 普通阅读 {len(normal_response)} vs 完整阅读 {len(complete_response)} 字符")
        print(f"  • 详细程度: 完整阅读提供了 {len(complete_response) / len(normal_response):.1f}x 的详细信息")
        
        # 检查完整阅读特有的信息
        unique_features = ["阅读统计", "分块数量", "覆盖率", "完整性验证"]
        for feature in unique_features:
            if feature in complete_response and feature not in normal_response:
                print(f"  • ✅ 完整阅读包含: {feature}")

async def main():
    """主函数"""
    print("📖 完整文件阅读智能体测试工具")
    print("选择测试模式:")
    print("1. 预设测试用例")
    print("2. 交互式测试")
    print("3. 对比测试")
    
    try:
        choice = input("请选择 (1/2/3): ").strip()
        
        if choice == "1":
            await test_complete_reading()
        elif choice == "2":
            await interactive_complete_reading()
        elif choice == "3":
            await compare_reading_methods()
        else:
            print("无效选择，运行预设测试")
            await test_complete_reading()
            
    except KeyboardInterrupt:
        print("\n👋 再见！")

if __name__ == "__main__":
    asyncio.run(main())
