#!/usr/bin/env python3
"""
简化版文档阅读理解智能体测试
"""
import asyncio
import sys
import os

sys.path.append(os.path.dirname(os.path.abspath(__file__)))
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from config.models_config import get_agent_model_client
from config.agents_config import AGENT_SYSTEM_PROMPTS, AGENT_CAPABILITIES
from agents.simple_document_reader import SimpleDocumentReader

async def test_simple_document_reader():
    """测试简化版文档阅读理解智能体"""
    print("📚 简化版文档阅读理解智能体测试")
    print("=" * 50)
    
    # 初始化智能体
    print("🔧 初始化简化版文档阅读理解智能体...")
    try:
        model_client = get_agent_model_client("DocumentReader")
        system_message = AGENT_SYSTEM_PROMPTS["DocumentReader"]
        capabilities = AGENT_CAPABILITIES["DocumentReader"]
        
        doc_reader = SimpleDocumentReader(model_client, system_message, capabilities)
        print("✅ 初始化完成")
        
        # 显示文档统计
        stats = doc_reader.get_reading_stats()
        print(f"\n📊 文档处理统计:")
        for key, value in stats.items():
            if isinstance(value, float):
                print(f"  • {key}: {value:.1f}")
            else:
                print(f"  • {key}: {value}")
        
    except Exception as e:
        print(f"❌ 初始化失败: {e}")
        return
    
    # 测试用例
    test_cases = [
        {
            "category": "寄存器查询",
            "query": "SYS_AFE_REG1寄存器的功能是什么？",
            "expected": "应该找到寄存器的详细信息"
        },
        {
            "category": "模块概念",
            "query": "ADC模块的工作原理",
            "expected": "应该解释ADC的基本概念"
        },
        {
            "category": "配置方法",
            "query": "如何配置GPIO作为输出？",
            "expected": "应该提供配置步骤"
        },
        {
            "category": "技术参数",
            "query": "MCPWM的频率范围是多少？",
            "expected": "应该提供技术参数"
        },
        {
            "category": "地址查询",
            "query": "GPIO模块的基地址",
            "expected": "应该提供地址信息"
        },
        {
            "category": "功能对比",
            "query": "UART和SPI的区别",
            "expected": "应该对比两种通信方式"
        }
    ]
    
    print(f"\n🧪 开始测试 ({len(test_cases)} 个用例):")
    
    for i, test_case in enumerate(test_cases, 1):
        print(f"\n📝 测试 {i}: {test_case['category']}")
        print(f"❓ 查询: {test_case['query']}")
        print(f"🎯 期望: {test_case['expected']}")
        print("-" * 40)
        
        try:
            start_time = asyncio.get_event_loop().time()
            response = await doc_reader.handle_message(test_case['query'])
            end_time = asyncio.get_event_loop().time()
            
            print(f"💡 回答:\n{response}")
            print(f"⏱️  响应时间: {end_time - start_time:.2f}秒")
            
            # 简单的回答质量评估
            if len(response) > 100:
                print("✅ 回答详细")
            else:
                print("⚠️  回答较简短")
            
        except Exception as e:
            print(f"❌ 测试失败: {e}")
        
        print()
        
        # 添加延时
        await asyncio.sleep(0.5)
    
    # 显示最终统计
    final_stats = doc_reader.get_reading_stats()
    print(f"📊 最终统计:")
    print(f"  • 对话轮次: {final_stats['conversation_turns']}")
    
    # 测试检索功能
    await test_search_functionality(doc_reader)

async def test_search_functionality(doc_reader):
    """测试检索功能"""
    print("\n🔍 检索功能专项测试")
    print("-" * 40)
    
    # 测试不同类型的检索
    search_tests = [
        {
            "query": "GPIO",
            "description": "单关键词检索"
        },
        {
            "query": "ADC 配置 寄存器",
            "description": "多关键词检索"
        },
        {
            "query": "电机控制 PWM",
            "description": "概念组合检索"
        },
        {
            "query": "0x40010000",
            "description": "地址检索"
        },
        {
            "query": "中断处理",
            "description": "功能检索"
        }
    ]
    
    for test in search_tests:
        print(f"\n🔍 {test['description']}: {test['query']}")
        
        try:
            # 直接调用检索方法
            chunks = doc_reader._search_relevant_chunks(test['query'], top_k=3)
            
            print(f"📊 检索结果: {len(chunks)} 个相关块")
            
            for i, chunk in enumerate(chunks, 1):
                score = chunk.get('relevance_score', 0)
                chapter = chunk.get('chapter', '未知章节')
                print(f"  {i}. 章节: {chapter}, 相关度: {score:.2f}")
                
        except Exception as e:
            print(f"❌ 检索失败: {e}")

async def interactive_reading_test():
    """交互式阅读测试"""
    print("\n🎯 交互式文档阅读测试")
    print("输入 'stats' 查看统计信息")
    print("输入 'search <关键词>' 进行检索测试")
    print("输入 'quit' 退出")
    print("-" * 30)
    
    # 初始化
    try:
        model_client = get_agent_model_client("DocumentReader")
        system_message = AGENT_SYSTEM_PROMPTS["DocumentReader"]
        capabilities = AGENT_CAPABILITIES["DocumentReader"]
        doc_reader = SimpleDocumentReader(model_client, system_message, capabilities)
    except Exception as e:
        print(f"❌ 初始化失败: {e}")
        return
    
    while True:
        try:
            query = input("\n📚 请输入文档问题: ").strip()
            
            if query.lower() in ['quit', 'exit', 'q']:
                break
            
            if query.lower() == 'stats':
                stats = doc_reader.get_reading_stats()
                print(f"📊 统计信息:")
                for key, value in stats.items():
                    if isinstance(value, float):
                        print(f"  • {key}: {value:.1f}")
                    else:
                        print(f"  • {key}: {value}")
                continue
            
            if query.lower().startswith('search '):
                search_term = query[7:].strip()
                if search_term:
                    chunks = doc_reader._search_relevant_chunks(search_term, top_k=5)
                    print(f"🔍 检索 '{search_term}' 的结果:")
                    for i, chunk in enumerate(chunks, 1):
                        score = chunk.get('relevance_score', 0)
                        chapter = chunk.get('chapter', '未知章节')
                        print(f"  {i}. {chapter} (相关度: {score:.2f})")
                continue
            
            if not query:
                continue
            
            print("🔍 理解文档中...")
            response = await doc_reader.handle_message(query)
            print(f"\n📖 理解结果:\n{response}")
            
        except KeyboardInterrupt:
            break
        except Exception as e:
            print(f"❌ 错误: {e}")
    
    print("👋 文档阅读测试结束")

async def benchmark_performance():
    """性能基准测试"""
    print("\n⚡ 文档阅读性能基准测试")
    print("-" * 40)
    
    # 初始化
    try:
        model_client = get_agent_model_client("DocumentReader")
        system_message = AGENT_SYSTEM_PROMPTS["DocumentReader"]
        capabilities = AGENT_CAPABILITIES["DocumentReader"]
        doc_reader = SimpleDocumentReader(model_client, system_message, capabilities)
    except Exception as e:
        print(f"❌ 初始化失败: {e}")
        return
    
    # 性能测试查询
    performance_queries = [
        "GPIO寄存器配置",
        "ADC采样方法",
        "MCPWM电机控制",
        "UART通信设置",
        "系统时钟配置",
        "中断处理机制",
        "DMA传输配置",
        "定时器使用方法"
    ]
    
    total_time = 0
    successful_queries = 0
    
    for i, query in enumerate(performance_queries, 1):
        print(f"🔍 测试 {i}: {query}")
        
        try:
            start_time = asyncio.get_event_loop().time()
            response = await doc_reader.handle_message(query)
            end_time = asyncio.get_event_loop().time()
            
            query_time = end_time - start_time
            total_time += query_time
            successful_queries += 1
            
            print(f"  ✅ 完成，耗时: {query_time:.2f}秒，回答长度: {len(response)} 字符")
            
        except Exception as e:
            print(f"  ❌ 失败: {e}")
    
    if successful_queries > 0:
        avg_time = total_time / successful_queries
        print(f"\n📊 性能统计:")
        print(f"  • 成功查询: {successful_queries}/{len(performance_queries)}")
        print(f"  • 平均响应时间: {avg_time:.2f}秒")
        print(f"  • 总耗时: {total_time:.2f}秒")

async def main():
    """主函数"""
    print("📚 简化版文档阅读理解智能体测试工具")
    print("选择测试模式:")
    print("1. 预设测试用例")
    print("2. 交互式测试")
    print("3. 性能基准测试")
    
    try:
        choice = input("请选择 (1/2/3): ").strip()
        
        if choice == "1":
            await test_simple_document_reader()
        elif choice == "2":
            await interactive_reading_test()
        elif choice == "3":
            await benchmark_performance()
        else:
            print("无效选择，运行预设测试")
            await test_simple_document_reader()
            
    except KeyboardInterrupt:
        print("\n👋 再见！")

if __name__ == "__main__":
    asyncio.run(main())
