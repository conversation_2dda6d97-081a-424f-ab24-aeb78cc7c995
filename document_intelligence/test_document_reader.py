#!/usr/bin/env python3
"""
文档阅读理解智能体专项测试
测试长文档的智能理解能力
"""
import asyncio
import sys
import os

sys.path.append(os.path.dirname(os.path.abspath(__file__)))
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from config.models_config import get_agent_model_client
from config.agents_config import AGENT_SYSTEM_PROMPTS, AGENT_CAPABILITIES

# 由于DocumentReader可能需要额外依赖，我们先检查
try:
    from agents.document_reader import DocumentReader
    DOCUMENT_READER_AVAILABLE = True
except ImportError as e:
    print(f"⚠️  DocumentReader不可用: {e}")
    print("请安装依赖: pip install sentence-transformers faiss-cpu")
    DOCUMENT_READER_AVAILABLE = False

async def test_document_reader():
    """测试文档阅读理解智能体"""
    if not DOCUMENT_READER_AVAILABLE:
        print("❌ DocumentReader不可用，跳过测试")
        return
    
    print("📚 文档阅读理解智能体专项测试")
    print("=" * 50)
    
    # 初始化智能体
    print("🔧 初始化文档阅读理解智能体...")
    try:
        model_client = get_agent_model_client("DocumentReader")
        system_message = AGENT_SYSTEM_PROMPTS["DocumentReader"]
        capabilities = AGENT_CAPABILITIES["DocumentReader"]
        
        doc_reader = DocumentReader(model_client, system_message, capabilities)
        print("✅ 初始化完成")
        
        # 显示文档统计
        stats = doc_reader.get_reading_stats()
        print(f"\n📊 文档处理统计:")
        print(f"  • 文档块数量: {stats['total_chunks']}")
        print(f"  • 章节数量: {stats['total_sections']}")
        print(f"  • 嵌入模型: {'✅' if stats['embedding_model_loaded'] else '❌'}")
        print(f"  • 向量索引: {'✅' if stats['vector_index_built'] else '❌'}")
        
    except Exception as e:
        print(f"❌ 初始化失败: {e}")
        return
    
    # 测试用例
    test_cases = [
        {
            "category": "具体信息查找",
            "query": "SYS_AFE_REG1寄存器的功能和配置方法",
            "type": "specific_search",
            "expected": "应该找到寄存器的详细信息"
        },
        {
            "category": "概念理解",
            "query": "ADC模块的工作原理是什么？",
            "type": "conceptual_understanding",
            "expected": "应该解释ADC的基本概念和工作方式"
        },
        {
            "category": "配置查询",
            "query": "如何配置MCPWM模块进行电机控制？",
            "type": "specific_search",
            "expected": "应该提供MCPWM配置的具体步骤"
        },
        {
            "category": "对比分析",
            "query": "GPIO和ADC模块有什么区别？",
            "type": "comparative_analysis",
            "expected": "应该对比两个模块的功能差异"
        },
        {
            "category": "文档总结",
            "query": "请总结LKS32MC08x的主要功能模块",
            "type": "summary_request",
            "expected": "应该提供文档的整体概览"
        },
        {
            "category": "深度理解",
            "query": "电机控制系统中如何实现闭环控制？",
            "type": "conceptual_understanding",
            "expected": "应该解释闭环控制的实现方法"
        }
    ]
    
    print(f"\n🧪 开始测试 ({len(test_cases)} 个用例):")
    
    for i, test_case in enumerate(test_cases, 1):
        print(f"\n📝 测试 {i}: {test_case['category']}")
        print(f"❓ 查询: {test_case['query']}")
        print(f"🏷️  类型: {test_case['type']}")
        print(f"🎯 期望: {test_case['expected']}")
        print("-" * 40)
        
        try:
            start_time = asyncio.get_event_loop().time()
            response = await doc_reader.handle_message(test_case['query'])
            end_time = asyncio.get_event_loop().time()
            
            print(f"💡 回答:\n{response}")
            print(f"⏱️  响应时间: {end_time - start_time:.2f}秒")
            
            # 简单的回答质量评估
            if len(response) > 100:
                print("✅ 回答详细")
            else:
                print("⚠️  回答较简短")
            
        except Exception as e:
            print(f"❌ 测试失败: {e}")
        
        print()
        
        # 添加延时
        await asyncio.sleep(1)
    
    # 显示最终统计
    final_stats = doc_reader.get_reading_stats()
    print(f"📊 最终统计:")
    print(f"  • 对话轮次: {final_stats['conversation_turns']}")
    
    # 测试检索功能
    await test_retrieval_functionality(doc_reader)

async def test_retrieval_functionality(doc_reader):
    """测试检索功能"""
    print("\n🔍 检索功能专项测试")
    print("-" * 40)
    
    # 测试不同类型的检索
    retrieval_tests = [
        {
            "query": "GPIO",
            "description": "单关键词检索"
        },
        {
            "query": "ADC 配置 寄存器",
            "description": "多关键词检索"
        },
        {
            "query": "电机控制 PWM 频率",
            "description": "概念组合检索"
        },
        {
            "query": "0x40010000",
            "description": "地址检索"
        }
    ]
    
    for test in retrieval_tests:
        print(f"\n🔍 {test['description']}: {test['query']}")
        
        try:
            # 直接调用检索方法
            chunks = doc_reader._retrieve_relevant_chunks(test['query'], top_k=3)
            
            print(f"📊 检索结果: {len(chunks)} 个相关块")
            
            for i, chunk in enumerate(chunks, 1):
                score = chunk.get('relevance_score', 0)
                section = chunk.get('section', '未知章节')
                print(f"  {i}. 章节: {section}, 相关度: {score:.2f}")
                
        except Exception as e:
            print(f"❌ 检索失败: {e}")

async def interactive_reading_test():
    """交互式阅读测试"""
    if not DOCUMENT_READER_AVAILABLE:
        print("❌ DocumentReader不可用")
        return
    
    print("\n🎯 交互式文档阅读测试")
    print("输入 'stats' 查看统计信息")
    print("输入 'quit' 退出")
    print("-" * 30)
    
    # 初始化
    try:
        model_client = get_agent_model_client("DocumentReader")
        system_message = AGENT_SYSTEM_PROMPTS["DocumentReader"]
        capabilities = AGENT_CAPABILITIES["DocumentReader"]
        doc_reader = DocumentReader(model_client, system_message, capabilities)
    except Exception as e:
        print(f"❌ 初始化失败: {e}")
        return
    
    while True:
        try:
            query = input("\n📚 请输入文档问题: ").strip()
            
            if query.lower() in ['quit', 'exit', 'q']:
                break
            
            if query.lower() == 'stats':
                stats = doc_reader.get_reading_stats()
                print(f"📊 统计信息:")
                for key, value in stats.items():
                    print(f"  • {key}: {value}")
                continue
            
            if not query:
                continue
            
            print("🔍 理解文档中...")
            response = await doc_reader.handle_message(query)
            print(f"\n📖 理解结果:\n{response}")
            
        except KeyboardInterrupt:
            break
        except Exception as e:
            print(f"❌ 错误: {e}")
    
    print("👋 文档阅读测试结束")

async def benchmark_reading_performance():
    """基准性能测试"""
    if not DOCUMENT_READER_AVAILABLE:
        print("❌ DocumentReader不可用")
        return
    
    print("\n⚡ 文档阅读性能基准测试")
    print("-" * 40)
    
    # 初始化
    try:
        model_client = get_agent_model_client("DocumentReader")
        system_message = AGENT_SYSTEM_PROMPTS["DocumentReader"]
        capabilities = AGENT_CAPABILITIES["DocumentReader"]
        doc_reader = DocumentReader(model_client, system_message, capabilities)
    except Exception as e:
        print(f"❌ 初始化失败: {e}")
        return
    
    # 性能测试查询
    performance_queries = [
        "GPIO寄存器",
        "ADC配置方法",
        "MCPWM电机控制",
        "系统时钟配置",
        "中断处理机制"
    ]
    
    total_time = 0
    successful_queries = 0
    
    for i, query in enumerate(performance_queries, 1):
        print(f"🔍 测试 {i}: {query}")
        
        try:
            start_time = asyncio.get_event_loop().time()
            response = await doc_reader.handle_message(query)
            end_time = asyncio.get_event_loop().time()
            
            query_time = end_time - start_time
            total_time += query_time
            successful_queries += 1
            
            print(f"  ✅ 完成，耗时: {query_time:.2f}秒")
            
        except Exception as e:
            print(f"  ❌ 失败: {e}")
    
    if successful_queries > 0:
        avg_time = total_time / successful_queries
        print(f"\n📊 性能统计:")
        print(f"  • 成功查询: {successful_queries}/{len(performance_queries)}")
        print(f"  • 平均响应时间: {avg_time:.2f}秒")
        print(f"  • 总耗时: {total_time:.2f}秒")

async def main():
    """主函数"""
    print("📚 文档阅读理解智能体测试工具")
    print("选择测试模式:")
    print("1. 预设测试用例")
    print("2. 交互式测试")
    print("3. 性能基准测试")
    
    try:
        choice = input("请选择 (1/2/3): ").strip()
        
        if choice == "1":
            await test_document_reader()
        elif choice == "2":
            await interactive_reading_test()
        elif choice == "3":
            await benchmark_reading_performance()
        else:
            print("无效选择，运行预设测试")
            await test_document_reader()
            
    except KeyboardInterrupt:
        print("\n👋 再见！")

if __name__ == "__main__":
    asyncio.run(main())
