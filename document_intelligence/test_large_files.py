#!/usr/bin/env python3
"""
测试大文件处理能力
"""
import asyncio
import sys
import os

sys.path.append(os.path.dirname(os.path.abspath(__file__)))
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from config.models_config import get_agent_model_client
from config.agents_config import AGENT_SYSTEM_PROMPTS, AGENT_CAPABILITIES
from agents.file_reader import FileReader

async def test_large_file_handling():
    """测试大文件处理能力"""
    print("📊 大文件处理能力测试")
    print("=" * 50)
    
    # 初始化智能体
    print("🔧 初始化文件阅读智能体...")
    try:
        model_client = get_agent_model_client("DocumentReader")
        system_message = AGENT_SYSTEM_PROMPTS["DocumentReader"]
        capabilities = AGENT_CAPABILITIES["DocumentReader"]
        
        file_reader = FileReader(model_client, system_message, capabilities)
        print("✅ 初始化完成")
        
        # 显示当前限制
        limits = file_reader.get_current_limits()
        print(f"\n📋 当前文件大小限制:")
        for key, value in limits.items():
            print(f"  • {key}: {value}")
        
    except Exception as e:
        print(f"❌ 初始化失败: {e}")
        return
    
    # 创建不同大小的测试文件
    test_files = create_test_files_of_different_sizes()
    
    # 测试用例
    test_cases = [
        {
            "category": "小文件",
            "file": test_files['small'],
            "description": "测试正常大小文件处理"
        },
        {
            "category": "中等文件",
            "file": test_files['medium'],
            "description": "测试中等大小文件处理"
        },
        {
            "category": "大文件",
            "file": test_files['large'],
            "description": "测试大文件智能采样"
        }
    ]
    
    print(f"\n🧪 开始大文件处理测试 ({len(test_cases)} 个用例):")
    
    for i, test_case in enumerate(test_cases, 1):
        print(f"\n📝 测试 {i}: {test_case['category']}")
        print(f"📁 文件: {test_case['file']}")
        print(f"📋 描述: {test_case['description']}")
        
        # 显示文件信息
        file_size = os.path.getsize(test_case['file'])
        print(f"📊 文件大小: {file_size / 1024:.1f}KB")
        print("-" * 60)
        
        try:
            query = f"读取文件 {test_case['file']} 并总结主要内容"
            
            start_time = asyncio.get_event_loop().time()
            response = await file_reader.handle_message(query)
            end_time = asyncio.get_event_loop().time()
            
            print(f"💡 处理结果:\n{response[:500]}...")
            print(f"⏱️  处理时间: {end_time - start_time:.2f}秒")
            
        except Exception as e:
            print(f"❌ 测试失败: {e}")
        
        print("\n" + "=" * 60)
        
        # 添加延时
        await asyncio.sleep(1)
    
    # 测试配置调整
    await test_configuration_adjustment(file_reader)
    
    # 清理测试文件
    cleanup_test_files(test_files)

def create_test_files_of_different_sizes():
    """创建不同大小的测试文件"""
    print("📁 创建不同大小的测试文件...")
    
    test_dir = "large_file_tests"
    os.makedirs(test_dir, exist_ok=True)
    
    test_files = {}
    
    # 小文件 (5KB)
    small_content = "这是一个小文件测试。\n" * 200
    small_file = os.path.join(test_dir, "small_file.txt")
    with open(small_file, 'w', encoding='utf-8') as f:
        f.write(small_content)
    test_files['small'] = small_file
    
    # 中等文件 (50KB)
    medium_content = """
# 中等大小文件测试

这是一个中等大小的测试文件，用于验证文件阅读智能体的处理能力。

## 技术文档示例

### 系统架构
本系统采用模块化设计，包含以下主要组件：
1. 文件读取模块
2. 内容解析模块
3. 智能理解模块
4. 响应生成模块

### 核心功能
- 支持多种文件格式
- 自动编码检测
- 智能内容分块
- 上下文感知理解

### 技术特点
- 零依赖设计
- 内存优化
- 错误容错
- 性能监控

""" * 50  # 重复50次，约50KB
    
    medium_file = os.path.join(test_dir, "medium_file.md")
    with open(medium_file, 'w', encoding='utf-8') as f:
        f.write(medium_content)
    test_files['medium'] = medium_file
    
    # 大文件 (200KB)
    large_content = f"""
# 大文件测试文档

这是一个大文件测试，用于验证智能采样功能。

## 第一部分：系统概述

本系统是一个基于AutoGen框架的智能文档阅读理解系统，具有以下特点：

### 核心能力
1. **多格式支持**: 支持txt, md, py, js, json等多种文件格式
2. **智能理解**: 模型真正读取和理解文件内容
3. **大文件处理**: 支持智能采样和分块处理
4. **编码检测**: 自动检测文件编码格式

### 技术架构
- 文件系统访问：Python内置功能
- 文本处理：正则表达式和字符串操作
- 模型交互：AutoGen ChatCompletionClient
- 错误处理：异常捕获和优雅降级

## 第二部分：实现细节

### 文件读取策略
1. **小文件**: 直接读取到内存
2. **中等文件**: 流式读取
3. **大文件**: 智能采样

### 内容处理流程
1. 文件路径解析
2. 文件验证和编码检测
3. 内容读取和预处理
4. 智能分块或采样
5. 模型理解和回答生成

### 性能优化
- 内存使用优化
- 读取策略优化
- 响应时间优化
- 错误恢复机制

## 第三部分：使用示例

### 基本用法
```
读取文件 example.txt 并总结内容
分析文件 config.json 的配置项
查看文件 readme.md 并回答问题
```

### 高级功能
- 支持相对路径和绝对路径
- 自动文件类型识别
- 智能内容提取
- 多轮对话支持

""" * 20  # 重复20次，约200KB
    
    large_file = os.path.join(test_dir, "large_file.md")
    with open(large_file, 'w', encoding='utf-8') as f:
        f.write(large_content)
    test_files['large'] = large_file
    
    print(f"✅ 创建了 {len(test_files)} 个不同大小的测试文件")
    for name, path in test_files.items():
        size = os.path.getsize(path)
        print(f"  • {name}: {size / 1024:.1f}KB")
    
    return test_files

async def test_configuration_adjustment(file_reader):
    """测试配置调整功能"""
    print("\n⚙️  测试配置调整功能")
    print("-" * 40)
    
    # 显示当前配置
    print("📋 当前配置:")
    limits = file_reader.get_current_limits()
    for key, value in limits.items():
        print(f"  • {key}: {value}")
    
    # 调整配置
    print("\n🔧 调整配置...")
    file_reader.configure_limits(
        max_file_size=200 * 1024 * 1024,  # 200MB
        smart_sample_threshold=50000      # 5万字符
    )
    
    # 显示新配置
    print("\n📋 新配置:")
    new_limits = file_reader.get_current_limits()
    for key, value in new_limits.items():
        print(f"  • {key}: {value}")

def cleanup_test_files(test_files):
    """清理测试文件"""
    print("\n🧹 清理测试文件...")
    
    for file_path in test_files.values():
        try:
            if os.path.exists(file_path):
                os.remove(file_path)
        except Exception as e:
            print(f"⚠️  删除文件失败 {file_path}: {e}")
    
    # 删除测试目录
    try:
        test_dir = "large_file_tests"
        if os.path.exists(test_dir):
            os.rmdir(test_dir)
        print("✅ 测试文件清理完成")
    except Exception as e:
        print(f"⚠️  删除目录失败: {e}")

async def main():
    """主函数"""
    print("📊 大文件处理能力测试工具")
    
    try:
        await test_large_file_handling()
    except KeyboardInterrupt:
        print("\n👋 再见！")

if __name__ == "__main__":
    asyncio.run(main())
