"""
文件阅读智能体
让模型真正去读取和理解文件内容
"""
import os
from typing import Dict, Any, List
from .base_agent import BaseAgent, create_agent_response

class FileReader(BaseAgent):
    """文件阅读智能体 - 让模型真正读取文件"""

    def __init__(self, model_client, system_message: str, capabilities: Dict[str, Any]):
        super().__init__(
            name="FileReader",
            model_client=model_client,
            system_message=system_message,
            capabilities=capabilities
        )

        # 文件路径
        self.document_path = self._find_document_path()

        # 模型上下文限制（字符数）
        self.max_context_chars = 15000  # 保守估计，留给问题和回答的空间

    def _find_document_path(self) -> str:
        """查找文档路径"""
        possible_paths = [
            os.path.join(os.path.dirname(os.path.dirname(os.path.abspath(__file__))), "data", "full.md"),
            os.path.join(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))), "full.md"),
            "full.md"
        ]

        for path in possible_paths:
            if os.path.exists(path):
                return path

        return None

    async def process_request(self, request: Dict[str, Any]) -> Dict[str, Any]:
        """处理文件阅读请求"""
        message = request.get("message", "")

        if not self.document_path:
            return await create_agent_response(
                "❌ 未找到文档文件 full.md，请确保文件存在。"
            )

        # 读取文件内容
        try:
            with open(self.document_path, 'r', encoding='utf-8') as f:
                full_content = f.read()

            print(f"📄 读取文档成功，总长度: {len(full_content)} 字符")

        except Exception as e:
            return await create_agent_response(
                f"❌ 读取文档失败: {e}"
            )

        # 根据问题类型决定如何处理文档
        if self._is_specific_query(message):
            # 具体查询：先搜索相关部分，再让模型读取
            relevant_content = self._find_relevant_content(full_content, message)
            response = await self._ask_model_to_read(message, relevant_content)
        else:
            # 一般查询：分段让模型读取
            response = await self._ask_model_to_read_in_chunks(message, full_content)

        return await create_agent_response(response)

    def _is_specific_query(self, message: str) -> bool:
        """判断是否是具体查询"""
        specific_keywords = [
            "寄存器", "register", "地址", "address",
            "配置", "config", "参数", "parameter",
            "GPIO", "ADC", "UART", "MCPWM", "DMA"
        ]

        return any(keyword.lower() in message.lower() for keyword in specific_keywords)

    def _find_relevant_content(self, content: str, query: str) -> str:
        """简单搜索相关内容"""
        lines = content.split('\n')
        query_keywords = query.lower().split()

        relevant_sections = []
        current_section = []
        section_relevance = 0

        for line in lines:
            line_lower = line.lower()

            # 检查是否是新章节
            if line.startswith('#'):
                # 保存前一个章节（如果相关）
                if section_relevance > 0 and current_section:
                    relevant_sections.extend(current_section)
                    relevant_sections.append("")  # 空行分隔

                # 开始新章节
                current_section = [line]
                section_relevance = sum(1 for keyword in query_keywords if keyword in line_lower)
            else:
                current_section.append(line)
                # 累计相关度
                section_relevance += sum(1 for keyword in query_keywords if keyword in line_lower)

        # 保存最后一个章节
        if section_relevance > 0 and current_section:
            relevant_sections.extend(current_section)

        # 如果没找到相关内容，返回文档开头
        if not relevant_sections:
            relevant_sections = lines[:100]  # 前100行

        relevant_text = '\n'.join(relevant_sections)

        # 控制长度
        if len(relevant_text) > self.max_context_chars:
            relevant_text = relevant_text[:self.max_context_chars] + "\n\n[文档内容过长，已截断...]"

        print(f"🔍 找到相关内容，长度: {len(relevant_text)} 字符")
        return relevant_text

    async def _ask_model_to_read(self, question: str, content: str) -> str:
        """让模型读取指定内容并回答问题"""
        prompt = f"""请仔细阅读以下LKS32MC08x用户手册的内容，然后回答用户的问题。

文档内容：
{content}

用户问题：{question}

请基于你刚刚阅读的文档内容来回答问题。如果文档中没有相关信息，请明确说明。"""

        try:
            print("🤖 正在让模型阅读文档内容...")
            response = await self.model_client.create(
                messages=[{"role": "user", "content": prompt}]
            )

            # 检查不同的响应格式
            if hasattr(response, 'content') and response.content:
                # AutoGen 0.6.1 格式
                model_response = response.content
                print(f"✅ 模型阅读完成，回答长度: {len(model_response)} 字符")
                return model_response
            elif hasattr(response, 'choices') and response.choices:
                # OpenAI 格式
                model_response = response.choices[0].message.content
                print(f"✅ 模型阅读完成，回答长度: {len(model_response)} 字符")
                return model_response
            else:
                print(f"⚠️  响应格式: {type(response)}, 属性: {dir(response)}")
                return f"❌ 模型响应格式异常: {response}"

        except Exception as e:
            print(f"❌ 模型调用失败: {e}")
            return f"模型调用失败: {e}\n\n基于搜索到的内容：\n{content[:500]}..."

    async def _ask_model_to_read_in_chunks(self, question: str, full_content: str) -> str:
        """分块让模型读取文档"""
        # 简单分块策略
        chunk_size = self.max_context_chars - 1000  # 留给问题和指令的空间
        chunks = []

        lines = full_content.split('\n')
        current_chunk = []
        current_size = 0

        for line in lines:
            if current_size + len(line) > chunk_size and current_chunk:
                chunks.append('\n'.join(current_chunk))
                current_chunk = [line]
                current_size = len(line)
            else:
                current_chunk.append(line)
                current_size += len(line)

        if current_chunk:
            chunks.append('\n'.join(current_chunk))

        print(f"📚 文档分为 {len(chunks)} 个块进行阅读")

        # 让模型逐块阅读
        all_responses = []

        for i, chunk in enumerate(chunks[:3]):  # 最多读前3块，避免太长
            print(f"🤖 正在让模型阅读第 {i+1}/{min(3, len(chunks))} 块...")

            prompt = f"""请仔细阅读以下LKS32MC08x用户手册的第{i+1}部分内容：

文档内容（第{i+1}部分）：
{chunk}

基于这部分内容，请回答：{question}

如果这部分内容与问题相关，请提供详细回答。如果不相关，请简单说明这部分主要讲什么。"""

            try:
                response = await self.model_client.create(
                    messages=[{"role": "user", "content": prompt}]
                )

                if hasattr(response, 'choices') and response.choices:
                    chunk_response = response.choices[0].message.content
                    all_responses.append(f"## 文档第{i+1}部分的分析：\n{chunk_response}")
                    print(f"✅ 第 {i+1} 块阅读完成")
                else:
                    all_responses.append(f"## 文档第{i+1}部分：模型响应异常")

            except Exception as e:
                print(f"❌ 第 {i+1} 块阅读失败: {e}")
                all_responses.append(f"## 文档第{i+1}部分：读取失败 - {e}")

        # 汇总所有回答
        final_response = f"基于对LKS32MC08x用户手册的分块阅读，以下是分析结果：\n\n"
        final_response += "\n\n".join(all_responses)

        return final_response

    def get_document_info(self) -> Dict[str, Any]:
        """获取文档信息"""
        if not self.document_path:
            return {"status": "not_found"}

        try:
            with open(self.document_path, 'r', encoding='utf-8') as f:
                content = f.read()

            return {
                "status": "found",
                "path": self.document_path,
                "size_chars": len(content),
                "size_lines": len(content.split('\n')),
                "max_context_chars": self.max_context_chars
            }
        except Exception as e:
            return {"status": "error", "error": str(e)}
