"""
文件阅读智能体
让模型真正去读取和理解文件内容
"""
import os
from typing import Dict, Any, List
from .base_agent import BaseAgent, create_agent_response

class FileReader(BaseAgent):
    """文件阅读智能体 - 让模型真正读取任何文件"""

    def __init__(self, model_client, system_message: str, capabilities: Dict[str, Any]):
        super().__init__(
            name="FileReader",
            model_client=model_client,
            system_message=system_message,
            capabilities=capabilities
        )

        # 默认文档路径（向后兼容）
        self.default_document_path = self._find_document_path()

        # 模型上下文限制（字符数）
        self.max_context_chars = 15000  # 保守估计，留给问题和回答的空间

        # 支持的文件类型
        self.supported_extensions = {
            '.txt', '.md', '.py', '.js', '.html', '.css', '.json', '.xml',
            '.yaml', '.yml', '.ini', '.cfg', '.conf', '.log', '.csv',
            '.sql', '.sh', '.bat', '.dockerfile', '.gitignore', '.readme'
        }

    def _find_document_path(self) -> str:
        """查找文档路径"""
        possible_paths = [
            os.path.join(os.path.dirname(os.path.dirname(os.path.abspath(__file__))), "data", "full.md"),
            os.path.join(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))), "full.md"),
            "full.md"
        ]

        for path in possible_paths:
            if os.path.exists(path):
                return path

        return None

    def _extract_file_path(self, message: str) -> str:
        """从消息中提取文件路径"""
        import re

        # 匹配各种文件路径格式
        patterns = [
            r'读取文件\s+([^\s]+)',
            r'打开文件\s+([^\s]+)',
            r'查看文件\s+([^\s]+)',
            r'分析文件\s+([^\s]+)',
            r'文件路径[：:]\s*([^\s]+)',
            r'文件[：:]\s*([^\s]+)',
            r'([^\s]+\.[a-zA-Z0-9]+)',  # 任何带扩展名的文件
        ]

        for pattern in patterns:
            match = re.search(pattern, message)
            if match:
                file_path = match.group(1).strip()
                # 移除引号
                file_path = file_path.strip('"\'')
                return file_path

        return None

    def _validate_file(self, file_path: str) -> dict:
        """验证文件是否可读"""
        if not file_path:
            return {"valid": False, "error": "❌ 文件路径为空"}

        if not os.path.exists(file_path):
            return {"valid": False, "error": f"❌ 文件不存在: {file_path}"}

        if not os.path.isfile(file_path):
            return {"valid": False, "error": f"❌ 不是文件: {file_path}"}

        # 检查文件扩展名
        _, ext = os.path.splitext(file_path.lower())
        if ext and ext not in self.supported_extensions:
            return {
                "valid": False,
                "error": f"❌ 不支持的文件类型: {ext}\n支持的类型: {', '.join(sorted(self.supported_extensions))}"
            }

        # 检查文件大小（避免读取过大文件）
        try:
            file_size = os.path.getsize(file_path)
            max_size = 10 * 1024 * 1024  # 10MB
            if file_size > max_size:
                return {
                    "valid": False,
                    "error": f"❌ 文件过大: {file_size / 1024 / 1024:.1f}MB (最大支持10MB)"
                }
        except Exception as e:
            return {"valid": False, "error": f"❌ 无法获取文件大小: {e}"}

        return {"valid": True}

    def _read_file_content(self, file_path: str) -> str:
        """读取文件内容，自动检测编码"""
        encodings = ['utf-8', 'gbk', 'gb2312', 'latin1']

        for encoding in encodings:
            try:
                with open(file_path, 'r', encoding=encoding) as f:
                    content = f.read()
                print(f"📝 使用编码: {encoding}")
                return content
            except UnicodeDecodeError:
                continue
            except Exception as e:
                raise Exception(f"读取文件失败: {e}")

        raise Exception("无法识别文件编码")

    def _clean_message(self, message: str, file_path: str) -> str:
        """清理消息，移除文件路径部分"""
        import re

        # 移除文件路径相关的指令
        patterns = [
            r'读取文件\s+[^\s]+\s*',
            r'打开文件\s+[^\s]+\s*',
            r'查看文件\s+[^\s]+\s*',
            r'分析文件\s+[^\s]+\s*',
            r'文件路径[：:]\s*[^\s]+\s*',
            r'文件[：:]\s*[^\s]+\s*',
        ]

        clean_message = message
        for pattern in patterns:
            clean_message = re.sub(pattern, '', clean_message)

        # 移除多余的空格和标点
        clean_message = re.sub(r'\s+', ' ', clean_message).strip()
        clean_message = clean_message.lstrip('，。、；：！？,.')

        # 如果清理后为空，使用默认问题
        if not clean_message:
            clean_message = "请总结这个文件的主要内容"

        return clean_message

    def _get_file_type_description(self, file_path: str) -> str:
        """获取文件类型描述"""
        if not file_path:
            return "文档"

        _, ext = os.path.splitext(file_path.lower())

        type_map = {
            '.md': 'Markdown文档',
            '.txt': '文本文件',
            '.py': 'Python代码文件',
            '.js': 'JavaScript代码文件',
            '.html': 'HTML网页文件',
            '.css': 'CSS样式文件',
            '.json': 'JSON数据文件',
            '.xml': 'XML文件',
            '.yaml': 'YAML配置文件',
            '.yml': 'YAML配置文件',
            '.ini': '配置文件',
            '.cfg': '配置文件',
            '.conf': '配置文件',
            '.log': '日志文件',
            '.csv': 'CSV数据文件',
            '.sql': 'SQL脚本文件',
            '.sh': 'Shell脚本文件',
            '.bat': '批处理文件',
            '.dockerfile': 'Docker文件',
        }

        return type_map.get(ext, f'{ext}文件' if ext else '文档')

    async def process_request(self, request: Dict[str, Any]) -> Dict[str, Any]:
        """处理文件阅读请求"""
        message = request.get("message", "")

        # 检查是否指定了文件路径
        file_path = self._extract_file_path(message)

        if not file_path:
            # 使用默认文档
            file_path = self.default_document_path
            if not file_path:
                return await create_agent_response(
                    "❌ 未找到默认文档文件，请指定要读取的文件路径。\n"
                    "例如：'读取文件 /path/to/file.txt 并回答：...'"
                )

        # 验证文件
        validation_result = self._validate_file(file_path)
        if not validation_result["valid"]:
            return await create_agent_response(validation_result["error"])

        # 读取文件内容
        try:
            content = self._read_file_content(file_path)
            print(f"📄 读取文件成功: {file_path}")
            print(f"📊 文件大小: {len(content)} 字符")

        except Exception as e:
            return await create_agent_response(
                f"❌ 读取文件失败: {e}"
            )

        # 根据问题类型决定如何处理文档
        clean_message = self._clean_message(message, file_path)

        if self._is_specific_query(clean_message):
            # 具体查询：先搜索相关部分，再让模型读取
            relevant_content = self._find_relevant_content(content, clean_message)
            response = await self._ask_model_to_read(clean_message, relevant_content, file_path)
        else:
            # 一般查询：分段让模型读取
            response = await self._ask_model_to_read_in_chunks(clean_message, content, file_path)

        return await create_agent_response(response)

    def _is_specific_query(self, message: str) -> bool:
        """判断是否是具体查询"""
        specific_keywords = [
            "寄存器", "register", "地址", "address",
            "配置", "config", "参数", "parameter",
            "GPIO", "ADC", "UART", "MCPWM", "DMA"
        ]

        return any(keyword.lower() in message.lower() for keyword in specific_keywords)

    def _find_relevant_content(self, content: str, query: str) -> str:
        """简单搜索相关内容"""
        lines = content.split('\n')
        query_keywords = query.lower().split()

        relevant_sections = []
        current_section = []
        section_relevance = 0

        for line in lines:
            line_lower = line.lower()

            # 检查是否是新章节
            if line.startswith('#'):
                # 保存前一个章节（如果相关）
                if section_relevance > 0 and current_section:
                    relevant_sections.extend(current_section)
                    relevant_sections.append("")  # 空行分隔

                # 开始新章节
                current_section = [line]
                section_relevance = sum(1 for keyword in query_keywords if keyword in line_lower)
            else:
                current_section.append(line)
                # 累计相关度
                section_relevance += sum(1 for keyword in query_keywords if keyword in line_lower)

        # 保存最后一个章节
        if section_relevance > 0 and current_section:
            relevant_sections.extend(current_section)

        # 如果没找到相关内容，返回文档开头
        if not relevant_sections:
            relevant_sections = lines[:100]  # 前100行

        relevant_text = '\n'.join(relevant_sections)

        # 控制长度
        if len(relevant_text) > self.max_context_chars:
            relevant_text = relevant_text[:self.max_context_chars] + "\n\n[文档内容过长，已截断...]"

        print(f"🔍 找到相关内容，长度: {len(relevant_text)} 字符")
        return relevant_text

    async def _ask_model_to_read(self, question: str, content: str, file_path: str = None) -> str:
        """让模型读取指定内容并回答问题"""
        file_name = os.path.basename(file_path) if file_path else "文档"
        file_type = self._get_file_type_description(file_path) if file_path else "文档"

        prompt = f"""请仔细阅读以下{file_type}的内容，然后回答用户的问题。

文件名：{file_name}
文件内容：
{content}

用户问题：{question}

请基于你刚刚阅读的文件内容来回答问题。如果文件中没有相关信息，请明确说明。"""

        try:
            print("🤖 正在让模型阅读文档内容...")
            response = await self.model_client.create(
                messages=[{"role": "user", "content": prompt}]
            )

            # 检查不同的响应格式
            if hasattr(response, 'content') and response.content:
                # AutoGen 0.6.1 格式
                model_response = response.content
                print(f"✅ 模型阅读完成，回答长度: {len(model_response)} 字符")
                return model_response
            elif hasattr(response, 'choices') and response.choices:
                # OpenAI 格式
                model_response = response.choices[0].message.content
                print(f"✅ 模型阅读完成，回答长度: {len(model_response)} 字符")
                return model_response
            else:
                print(f"⚠️  响应格式: {type(response)}, 属性: {dir(response)}")
                return f"❌ 模型响应格式异常: {response}"

        except Exception as e:
            print(f"❌ 模型调用失败: {e}")
            return f"模型调用失败: {e}\n\n基于搜索到的内容：\n{content[:500]}..."

    async def _ask_model_to_read_in_chunks(self, question: str, full_content: str, file_path: str = None) -> str:
        """分块让模型读取文档"""
        # 简单分块策略
        chunk_size = self.max_context_chars - 1000  # 留给问题和指令的空间
        chunks = []

        lines = full_content.split('\n')
        current_chunk = []
        current_size = 0

        for line in lines:
            if current_size + len(line) > chunk_size and current_chunk:
                chunks.append('\n'.join(current_chunk))
                current_chunk = [line]
                current_size = len(line)
            else:
                current_chunk.append(line)
                current_size += len(line)

        if current_chunk:
            chunks.append('\n'.join(current_chunk))

        file_name = os.path.basename(file_path) if file_path else "文档"
        file_type = self._get_file_type_description(file_path) if file_path else "文档"

        print(f"📚 {file_type}分为 {len(chunks)} 个块进行阅读")

        # 让模型逐块阅读
        all_responses = []

        for i, chunk in enumerate(chunks[:3]):  # 最多读前3块，避免太长
            print(f"🤖 正在让模型阅读第 {i+1}/{min(3, len(chunks))} 块...")

            prompt = f"""请仔细阅读以下{file_type}的第{i+1}部分内容：

文件名：{file_name}
文件内容（第{i+1}部分）：
{chunk}

基于这部分内容，请回答：{question}

如果这部分内容与问题相关，请提供详细回答。如果不相关，请简单说明这部分主要讲什么。"""

            try:
                response = await self.model_client.create(
                    messages=[{"role": "user", "content": prompt}]
                )

                if hasattr(response, 'choices') and response.choices:
                    chunk_response = response.choices[0].message.content
                    all_responses.append(f"## 文档第{i+1}部分的分析：\n{chunk_response}")
                    print(f"✅ 第 {i+1} 块阅读完成")
                else:
                    all_responses.append(f"## 文档第{i+1}部分：模型响应异常")

            except Exception as e:
                print(f"❌ 第 {i+1} 块阅读失败: {e}")
                all_responses.append(f"## 文档第{i+1}部分：读取失败 - {e}")

        # 汇总所有回答
        final_response = f"基于对{file_type} ({file_name}) 的分块阅读，以下是分析结果：\n\n"
        final_response += "\n\n".join(all_responses)

        return final_response

    def get_document_info(self) -> Dict[str, Any]:
        """获取文档信息"""
        if not self.document_path:
            return {"status": "not_found"}

        try:
            with open(self.document_path, 'r', encoding='utf-8') as f:
                content = f.read()

            return {
                "status": "found",
                "path": self.document_path,
                "size_chars": len(content),
                "size_lines": len(content.split('\n')),
                "max_context_chars": self.max_context_chars
            }
        except Exception as e:
            return {"status": "error", "error": str(e)}
