"""
智能工程师级别的知识图谱查询系统
像开发工程师一样理解、整合和分析技术信息
"""
import os
import json
import re
import sqlite3
from typing import Dict, Any, List, Tuple
import asyncio
import networkx as nx

from .base_agent import BaseAgent, create_agent_response

class IntelligentKGQuery(BaseAgent):
    """智能工程师级别的知识图谱查询系统"""
    
    def __init__(self, model_client, system_message: str, capabilities: Dict[str, Any]):
        super().__init__(
            name="IntelligentKGQuery",
            model_client=model_client,
            system_message=system_message,
            capabilities=capabilities
        )
        
        self.db_path = "offline_kg.db"
        self.knowledge_graph = nx.DiGraph()
        
        # 工程师知识库
        self.engineering_knowledge = {
            'register_patterns': {
                'control': ['CTRL', 'CON', 'CFG', 'CONFIG'],
                'status': ['STAT', 'STA', 'STATUS', 'FLAG'],
                'data': ['DATA', 'DAT', 'BUF', 'BUFFER'],
                'interrupt': ['INT', 'IRQ', 'IE', 'IF'],
                'clock': ['CLK', 'CLOCK', 'OSC'],
                'power': ['PWR', 'POWER', 'PM']
            },
            'module_relationships': {
                'ADC': ['模数转换', '采样', '转换器', '分辨率', '参考电压'],
                'GPIO': ['通用输入输出', '引脚', '端口', '方向', '上拉下拉'],
                'UART': ['串行通信', '波特率', '数据位', '停止位', '校验位'],
                'PWM': ['脉宽调制', '占空比', '频率', '定时器'],
                'DMA': ['直接内存访问', '传输', '通道', '优先级'],
                'TIMER': ['定时器', '计数器', '预分频', '中断'],
                'SPI': ['串行外设接口', '时钟', '主从', '片选'],
                'I2C': ['两线接口', '地址', '应答', '时钟拉伸']
            },
            'common_use_cases': {
                'ADC': [
                    '模拟信号采集',
                    '传感器数据读取',
                    '电压监测',
                    '温度测量',
                    '电池电量检测'
                ],
                'GPIO': [
                    'LED控制',
                    '按键检测',
                    '外设使能',
                    '状态指示',
                    '中断输入'
                ],
                'UART': [
                    '调试输出',
                    '模块通信',
                    '数据传输',
                    '命令接收',
                    '日志记录'
                ],
                'PWM': [
                    '电机控制',
                    'LED亮度调节',
                    '音频输出',
                    '电源管理',
                    '伺服控制'
                ]
            }
        }
    
    async def process_request(self, request: Dict[str, Any]) -> Dict[str, Any]:
        """处理智能查询请求"""
        message = request.get("message", "")
        
        if "智能查询" in message or "工程师查询" in message:
            query = self._extract_query(message)
            result = await self._intelligent_query(query)
            return await create_agent_response(result)
        
        elif "使用场景" in message:
            entity = self._extract_entity(message)
            result = await self._analyze_use_cases(entity)
            return await create_agent_response(result)
        
        elif "设计建议" in message:
            context = self._extract_context(message)
            result = await self._provide_design_advice(context)
            return await create_agent_response(result)
        
        elif "关联分析" in message:
            entity = self._extract_entity(message)
            result = await self._analyze_relationships(entity)
            return await create_agent_response(result)
        
        else:
            return await create_agent_response(
                "请指定智能查询类型：\n"
                "- 智能查询 <技术问题>\n"
                "- 使用场景 <模块名>\n"
                "- 设计建议 <设计需求>\n"
                "- 关联分析 <实体名>"
            )
    
    def _extract_query(self, message: str) -> str:
        """提取查询内容"""
        query = re.sub(r'(智能查询|工程师查询)\s*', '', message).strip()
        return query if query else "技术咨询"
    
    def _extract_entity(self, message: str) -> str:
        """提取实体名称"""
        entity = re.sub(r'(使用场景|关联分析)\s*', '', message).strip()
        return entity if entity else "ADC"
    
    def _extract_context(self, message: str) -> str:
        """提取设计上下文"""
        context = re.sub(r'设计建议\s*', '', message).strip()
        return context if context else "嵌入式系统设计"
    
    async def _intelligent_query(self, query: str) -> str:
        """智能工程师级别查询"""
        print(f"🧠 工程师正在分析查询: {query}")
        
        # 1. 从数据库获取相关实体
        related_entities = await self._get_related_entities(query)
        
        if not related_entities:
            return f"❌ 未找到与'{query}'相关的技术信息"
        
        # 2. 加载图谱进行关系分析
        await self._load_graph_from_db()
        
        # 3. 工程师级别的信息整合
        analysis = await self._engineer_analysis(query, related_entities)
        
        return analysis
    
    async def _get_related_entities(self, query: str) -> List[Dict]:
        """获取相关实体"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        query_lower = query.lower()
        related_entities = []
        
        # 多策略搜索
        search_strategies = [
            # 精确匹配
            ("SELECT * FROM entities WHERE LOWER(name) = ?", (query_lower,)),
            # 名称包含
            ("SELECT * FROM entities WHERE LOWER(name) LIKE ?", (f'%{query_lower}%',)),
            # 上下文包含
            ("SELECT * FROM entities WHERE LOWER(context) LIKE ?", (f'%{query_lower}%',)),
            # 类型匹配
            ("SELECT * FROM entities WHERE LOWER(type) LIKE ?", (f'%{query_lower}%',))
        ]
        
        seen_ids = set()
        for sql, params in search_strategies:
            cursor.execute(sql, params)
            results = cursor.fetchall()
            
            for result in results:
                entity_id = result[0]
                if entity_id not in seen_ids:
                    seen_ids.add(entity_id)
                    related_entities.append({
                        'id': result[0],
                        'name': result[1],
                        'type': result[2],
                        'confidence': result[3],
                        'page': result[4],
                        'context': result[5]
                    })
        
        conn.close()
        return related_entities[:20]  # 限制数量
    
    async def _load_graph_from_db(self):
        """从数据库加载图谱"""
        if self.knowledge_graph.number_of_nodes() > 0:
            return  # 已加载
        
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        # 加载实体
        cursor.execute('SELECT id, name, type, confidence, page, context FROM entities')
        entities = cursor.fetchall()
        
        for entity_id, name, entity_type, confidence, page, context in entities:
            self.knowledge_graph.add_node(entity_id, 
                                        name=name,
                                        type=entity_type,
                                        confidence=confidence,
                                        page=page,
                                        context=context)
        
        # 加载关系
        cursor.execute('SELECT source_id, target_id, relation_type, confidence FROM relations')
        relations = cursor.fetchall()
        
        for source_id, target_id, relation_type, confidence in relations:
            if source_id in self.knowledge_graph.nodes and target_id in self.knowledge_graph.nodes:
                self.knowledge_graph.add_edge(source_id, target_id,
                                            type=relation_type,
                                            confidence=confidence)
        
        conn.close()
    
    async def _engineer_analysis(self, query: str, entities: List[Dict]) -> str:
        """工程师级别的分析"""
        
        # 构建分析提示
        entities_info = []
        for entity in entities[:10]:  # 限制数量避免过长
            entities_info.append(f"- {entity['name']} ({entity['type']}): {entity['context'][:100]}...")
        
        # 获取相关的工程知识
        engineering_context = self._get_engineering_context(entities)
        
        analysis_prompt = f"""你是一位资深的嵌入式系统工程师，请基于以下技术信息，对用户的查询进行专业分析。

用户查询: {query}

相关技术实体:
{chr(10).join(entities_info)}

工程背景知识:
{engineering_context}

请以工程师的角度提供:
1. 技术原理解释
2. 实际应用场景
3. 设计注意事项
4. 相关配置建议
5. 常见问题和解决方案

请用专业但易懂的语言回答，包含具体的技术细节和实用建议。"""

        try:
            response = await self.model_client.create(
                messages=[{"role": "user", "content": analysis_prompt}]
            )
            
            if hasattr(response, 'content'):
                ai_analysis = response.content
            elif hasattr(response, 'choices'):
                ai_analysis = response.choices[0].message.content
            else:
                ai_analysis = "AI分析暂时不可用"
            
            # 整合最终回答
            final_response = f"""# 🧠 工程师智能分析: {query}

## 🔍 发现的相关技术信息
找到 {len(entities)} 个相关技术实体，涵盖 {len(set(e['type'] for e in entities))} 种类型。

## 💡 专业工程师分析

{ai_analysis}

## 📊 技术实体详情
{self._format_entities_details(entities[:5])}

## 🔗 相关技术关联
{self._analyze_entity_relationships(entities)}

---
*本分析基于 {len(entities)} 个相关技术实体，结合工程实践经验生成*
"""
            
            return final_response
            
        except Exception as e:
            # AI分析失败时的降级方案
            return await self._fallback_analysis(query, entities)
    
    def _get_engineering_context(self, entities: List[Dict]) -> str:
        """获取工程背景知识"""
        context_parts = []
        
        # 分析实体类型
        entity_types = set(entity['type'] for entity in entities)
        
        for entity_type in entity_types:
            if entity_type in ['register', 'module', 'function']:
                # 查找相关的工程知识
                for entity in entities:
                    if entity['type'] == entity_type:
                        name = entity['name'].upper()
                        
                        # 匹配模块知识
                        for module, keywords in self.engineering_knowledge['module_relationships'].items():
                            if module in name or any(kw in name for kw in keywords):
                                if module in self.engineering_knowledge['common_use_cases']:
                                    use_cases = self.engineering_knowledge['common_use_cases'][module]
                                    context_parts.append(f"{module}模块常用于: {', '.join(use_cases[:3])}")
                                break
        
        return '\n'.join(context_parts) if context_parts else "通用嵌入式系统开发知识"
    
    def _format_entities_details(self, entities: List[Dict]) -> str:
        """格式化实体详情"""
        details = []
        for i, entity in enumerate(entities, 1):
            details.append(f"""
### {i}. {entity['name']} ({entity['type']})
- **置信度**: {entity['confidence']:.3f}
- **页面**: {entity['page']}
- **上下文**: {entity['context'][:150]}...
""")
        return '\n'.join(details)
    
    def _analyze_entity_relationships(self, entities: List[Dict]) -> str:
        """分析实体关系"""
        if self.knowledge_graph.number_of_nodes() == 0:
            return "图谱关系分析不可用"
        
        relationships = []
        entity_ids = [entity['id'] for entity in entities]
        
        # 查找实体间的关系
        for entity_id in entity_ids[:5]:  # 限制数量
            if entity_id in self.knowledge_graph.nodes:
                neighbors = list(self.knowledge_graph.neighbors(entity_id))
                if neighbors:
                    entity_name = self.knowledge_graph.nodes[entity_id]['name']
                    neighbor_names = []
                    for neighbor in neighbors[:3]:
                        neighbor_name = self.knowledge_graph.nodes[neighbor]['name']
                        neighbor_names.append(neighbor_name)
                    
                    if neighbor_names:
                        relationships.append(f"- **{entity_name}** 关联: {', '.join(neighbor_names)}")
        
        return '\n'.join(relationships) if relationships else "暂无直接关联关系"
    
    async def _fallback_analysis(self, query: str, entities: List[Dict]) -> str:
        """AI分析失败时的降级方案"""
        
        # 基于规则的分析
        analysis_parts = [f"# 🔧 技术分析: {query}"]
        
        # 实体类型统计
        type_counts = {}
        for entity in entities:
            entity_type = entity['type']
            type_counts[entity_type] = type_counts.get(entity_type, 0) + 1
        
        analysis_parts.append("## 📊 发现的技术组件")
        for entity_type, count in type_counts.items():
            type_name = {
                'register': '寄存器',
                'module': '功能模块', 
                'function': '功能',
                'parameter': '参数',
                'address': '地址',
                'bit_field': '位域'
            }.get(entity_type, entity_type)
            analysis_parts.append(f"- {type_name}: {count}个")
        
        # 主要实体
        analysis_parts.append("\n## 🎯 主要技术实体")
        for entity in entities[:5]:
            analysis_parts.append(f"- **{entity['name']}** ({entity['type']})")
        
        # 工程建议
        analysis_parts.append("\n## 💡 工程建议")
        if any('ADC' in entity['name'].upper() for entity in entities):
            analysis_parts.append("- ADC使用时注意参考电压配置和采样频率设置")
        if any('GPIO' in entity['name'].upper() for entity in entities):
            analysis_parts.append("- GPIO配置时注意上拉下拉电阻和驱动能力")
        if any('UART' in entity['name'].upper() for entity in entities):
            analysis_parts.append("- UART通信需要正确配置波特率和数据格式")
        
        return '\n'.join(analysis_parts)
    
    async def _analyze_use_cases(self, entity: str) -> str:
        """分析使用场景"""
        related_entities = await self._get_related_entities(entity)
        
        if not related_entities:
            return f"❌ 未找到'{entity}'的相关信息"
        
        # 构建使用场景分析
        use_cases = []
        
        # 基于实体类型推断使用场景
        for ent in related_entities:
            name = ent['name'].upper()
            entity_type = ent['type']
            
            if entity_type == 'module':
                for module, cases in self.engineering_knowledge['common_use_cases'].items():
                    if module in name:
                        use_cases.extend(cases)
                        break
        
        response = f"""# 🎯 {entity} 使用场景分析

## 📋 发现的相关组件
{chr(10).join(f"- {ent['name']} ({ent['type']})" for ent in related_entities[:8])}

## 🚀 典型应用场景
{chr(10).join(f"- {case}" for case in set(use_cases[:10]))}

## 🔧 配置要点
基于找到的 {len(related_entities)} 个相关组件，建议关注以下配置:
"""
        
        # 添加具体的配置建议
        if any('ADC' in ent['name'].upper() for ent in related_entities):
            response += "\n- **ADC配置**: 参考电压、采样频率、分辨率设置"
        if any('GPIO' in ent['name'].upper() for ent in related_entities):
            response += "\n- **GPIO配置**: 输入输出方向、上拉下拉、中断设置"
        if any('TIMER' in ent['name'].upper() for ent in related_entities):
            response += "\n- **定时器配置**: 时钟源、预分频、计数模式"
        
        return response
    
    async def _provide_design_advice(self, context: str) -> str:
        """提供设计建议"""
        
        # 分析设计上下文中的关键词
        keywords = re.findall(r'\b[A-Z]{2,}\b', context.upper())
        
        advice = f"""# 💡 设计建议: {context}

## 🔍 设计分析
基于您的设计需求，以下是专业建议:

## 🏗️ 架构建议
- 采用模块化设计，便于维护和扩展
- 合理规划中断优先级和处理流程
- 考虑功耗优化和电源管理

## ⚡ 性能优化
- 选择合适的时钟频率平衡性能和功耗
- 使用DMA减少CPU负载
- 优化代码结构避免阻塞操作

## 🛡️ 可靠性设计
- 添加看门狗保护机制
- 实现错误检测和恢复
- 考虑EMC和信号完整性
"""
        
        if keywords:
            advice += f"\n## 🎯 针对性建议\n基于检测到的关键技术: {', '.join(set(keywords))}"
        
        return advice
    
    async def _analyze_relationships(self, entity: str) -> str:
        """分析关联关系"""
        await self._load_graph_from_db()
        related_entities = await self._get_related_entities(entity)
        
        if not related_entities:
            return f"❌ 未找到'{entity}'的关联信息"
        
        # 分析图谱中的关系
        relationships = []
        
        for ent in related_entities[:5]:
            entity_id = ent['id']
            if entity_id in self.knowledge_graph.nodes:
                # 获取邻居节点
                neighbors = list(self.knowledge_graph.neighbors(entity_id))
                predecessors = list(self.knowledge_graph.predecessors(entity_id))
                
                if neighbors or predecessors:
                    relationships.append({
                        'entity': ent['name'],
                        'type': ent['type'],
                        'neighbors': [self.knowledge_graph.nodes[n]['name'] for n in neighbors[:3]],
                        'predecessors': [self.knowledge_graph.nodes[p]['name'] for p in predecessors[:3]]
                    })
        
        response = f"""# 🔗 {entity} 关联分析

## 📊 关联统计
- 发现相关实体: {len(related_entities)}个
- 图谱关系: {len(relationships)}个有关联的实体

## 🌐 关系网络
"""
        
        for rel in relationships:
            response += f"\n### {rel['entity']} ({rel['type']})"
            if rel['neighbors']:
                response += f"\n- **关联到**: {', '.join(rel['neighbors'])}"
            if rel['predecessors']:
                response += f"\n- **被关联**: {', '.join(rel['predecessors'])}"
        
        return response
