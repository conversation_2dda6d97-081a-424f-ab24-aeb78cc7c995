"""
离线知识图谱构建智能体
高效实体提取 + 本地存储 + 快速查询
"""
import os
import json
import re
import time
import sqlite3
from typing import Dict, Any, List, Tuple
import asyncio
from concurrent.futures import ThreadPoolExecutor
import networkx as nx
import matplotlib.pyplot as plt

from .base_agent import BaseAgent, create_agent_response

class OfflineKGBuilder(BaseAgent):
    """离线知识图谱构建智能体"""
    
    def __init__(self, model_client, system_message: str, capabilities: Dict[str, Any]):
        super().__init__(
            name="OfflineKGBuilder",
            model_client=model_client,
            system_message=system_message,
            capabilities=capabilities
        )
        
        # 初始化组件
        self.knowledge_graph = nx.DiGraph()
        self.db_path = "offline_kg.db"
        self.executor = ThreadPoolExecutor(max_workers=4)
        
        # 配置
        self.config = {
            'batch_size': 100,  # 批处理大小
            'max_entities_per_text': 50,  # 每段文本最大实体数
            'confidence_threshold': 0.5,  # 置信度阈值
            'enable_ai_extraction': True,  # 是否启用AI提取
            'ai_batch_size': 10  # AI处理批次大小
        }
        
        # 增强的实体模式库
        self.entity_patterns = {
            'register': [
                r'\b([A-Z][A-Z0-9_]{2,})\s*寄存器',
                r'\b([A-Z][A-Z0-9_]{2,})\s*REG\b',
                r'\b([A-Z][A-Z0-9_]{2,})\s*Register\b',
                r'\b([A-Z][A-Z0-9_]{2,})\s*配置寄存器',
                r'\b([A-Z][A-Z0-9_]{2,})\s*控制寄存器'
            ],
            'address': [
                r'\b(0x[0-9A-Fa-f]{4,})\b',
                r'\b([0-9A-Fa-f]{4,}H)\b',
                r'地址\s*(0x[0-9A-Fa-f]+)',
                r'基地址\s*(0x[0-9A-Fa-f]+)'
            ],
            'module': [
                r'\b(ADC|GPIO|UART|MCPWM|DMA|PWM|SPI|I2C|CAN|FLASH|RAM|RTC|WDT|TIMER|COUNTER)\b',
                r'\b(Timer|Counter|Comparator|Oscillator|Multiplexer)\b',
                r'(模数转换器|数模转换器|通用输入输出|串行通信|脉宽调制|直接内存访问)',
                r'(实时时钟|看门狗|定时器|计数器|比较器|振荡器)'
            ],
            'bit_field': [
                r'\bbit\s*\[(\d+:\d+)\]',
                r'\bbits?\s*(\d+)\s*[-~]\s*(\d+)',
                r'\b([A-Z][A-Z0-9_]*)\s*\[(\d+:\d+)\]',
                r'位\s*(\d+)\s*[-~]\s*(\d+)',
                r'第\s*(\d+)\s*位'
            ],
            'parameter': [
                r'(\d+)\s*(MHz|KHz|Hz|V|mV|mA|μA|Ω|kΩ|MΩ|pF|nF|μF|ms|μs|ns)',
                r'(波特率|频率|电压|电流|阻抗|分辨率|精度|采样率|转换速度)',
                r'(\d+)\s*(bit|位|通道|路|级)',
                r'(最大|最小|典型|标准)\s*(\d+\.?\d*)\s*(MHz|KHz|Hz|V|mV|mA|μA)'
            ],
            'function': [
                r'(模数转换|数模转换|信号调理|滤波|放大|比较|计数|定时)',
                r'(数据传输|通信|控制|监测|保护|复位|中断)',
                r'(配置|设置|使能|禁用|启动|停止|暂停|恢复)'
            ]
        }
        
        # 增强的关系模式库
        self.relation_patterns = [
            (r'(.+?)\s*控制\s*(.+)', 'controls'),
            (r'(.+?)\s*配置\s*(.+)', 'configures'),
            (r'(.+?)\s*包含\s*(.+)', 'contains'),
            (r'(.+?)\s*位于\s*(.+)', 'located_at'),
            (r'(.+?)\s*连接\s*(.+)', 'connects_to'),
            (r'(.+?)\s*依赖\s*(.+)', 'depends_on'),
            (r'(.+?)\s*实现\s*(.+)', 'implements'),
            (r'(.+?)\s*支持\s*(.+)', 'supports'),
            (r'(.+?)\s*用于\s*(.+)', 'used_for'),
            (r'(.+?)\s*提供\s*(.+)', 'provides')
        ]
        
        # 输出目录
        self.output_dir = "offline_knowledge_graphs"
        os.makedirs(self.output_dir, exist_ok=True)
        
        # 初始化数据库
        self._init_database()
    
    def _init_database(self):
        """初始化SQLite数据库"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        # 创建实体表
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS entities (
                id TEXT PRIMARY KEY,
                name TEXT NOT NULL,
                type TEXT NOT NULL,
                confidence REAL,
                page INTEGER,
                context TEXT,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )
        ''')
        
        # 创建关系表
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS relations (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                source_id TEXT NOT NULL,
                target_id TEXT NOT NULL,
                relation_type TEXT NOT NULL,
                confidence REAL,
                page INTEGER,
                context TEXT,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (source_id) REFERENCES entities (id),
                FOREIGN KEY (target_id) REFERENCES entities (id)
            )
        ''')
        
        # 创建索引
        cursor.execute('CREATE INDEX IF NOT EXISTS idx_entity_type ON entities (type)')
        cursor.execute('CREATE INDEX IF NOT EXISTS idx_entity_name ON entities (name)')
        cursor.execute('CREATE INDEX IF NOT EXISTS idx_relation_type ON relations (relation_type)')
        
        conn.commit()
        conn.close()
    
    async def process_request(self, request: Dict[str, Any]) -> Dict[str, Any]:
        """处理离线知识图谱构建请求"""
        message = request.get("message", "")
        
        if "构建知识图谱" in message or "build knowledge graph" in message.lower():
            mineru_path = self._extract_mineru_path(message)
            if not mineru_path:
                mineru_path = "/Users/<USER>/Public/AutoGen/monkey_ocr_project/mineru_output"
            
            result = await self._build_offline_knowledge_graph(mineru_path)
            return await create_agent_response(result)
        
        elif "分析知识图谱" in message:
            result = await self._analyze_offline_graph()
            return await create_agent_response(result)
        
        elif "查询实体" in message:
            query = self._extract_query(message)
            result = await self._query_entities_offline(query)
            return await create_agent_response(result)
        
        elif "导出知识图谱" in message:
            export_format = self._extract_export_format(message)
            result = await self._export_offline_graph(export_format)
            return await create_agent_response(result)
        
        elif "清空图谱" in message:
            result = await self._clear_graph()
            return await create_agent_response(result)
        
        else:
            return await create_agent_response(
                "请指定操作类型：\n"
                "- 构建知识图谱 [路径]\n"
                "- 分析知识图谱\n"
                "- 查询实体 <查询内容>\n"
                "- 导出知识图谱 [格式]\n"
                "- 清空图谱"
            )
    
    def _extract_mineru_path(self, message: str) -> str:
        """提取Mineru输出路径"""
        import re
        patterns = [
            r'路径[：:]\s*([^\s]+)',
            r'从\s+([^\s]+)\s+构建',
            r'输出目录[：:]\s*([^\s]+)'
        ]
        
        for pattern in patterns:
            match = re.search(pattern, message)
            if match:
                return match.group(1).strip()
        return None
    
    def _extract_query(self, message: str) -> str:
        """提取查询内容"""
        query = re.sub(r'查询实体\s*', '', message).strip()
        return query if query else "所有实体"
    
    def _extract_export_format(self, message: str) -> str:
        """提取导出格式"""
        formats = ['html', 'json', 'gexf', 'graphml', 'png', 'sql']
        for fmt in formats:
            if fmt in message.lower():
                return fmt
        return 'html'
    
    async def _build_offline_knowledge_graph(self, mineru_path: str) -> str:
        """构建离线知识图谱"""
        print(f"🚀 开始构建离线知识图谱: {mineru_path}")
        start_time = time.time()
        
        if not os.path.exists(mineru_path):
            return f"❌ Mineru输出路径不存在: {mineru_path}"
        
        # 清空现有数据
        await self._clear_graph()
        
        # 扫描文件
        content_files = []
        for root, _, files in os.walk(mineru_path):
            for file in files:
                if file.endswith('_content_list.json'):
                    file_path = os.path.join(root, file)
                    content_files.append(file_path)
        
        if not content_files:
            return f"❌ 未找到content_list.json文件: {mineru_path}"
        
        print(f"📄 找到 {len(content_files)} 个文件")
        
        # 批量处理文件
        total_entities = 0
        total_relations = 0
        processed_texts = 0
        
        for i, file_path in enumerate(content_files, 1):
            print(f"📊 处理文件 {i}/{len(content_files)}: {os.path.basename(file_path)}")
            
            try:
                entities, relations, texts = await self._process_file_offline(file_path)
                total_entities += entities
                total_relations += relations
                processed_texts += texts
                print(f"  ✅ 文本: {texts}, 实体: {entities}, 关系: {relations}")
            except Exception as e:
                print(f"  ❌ 处理失败: {e}")
        
        # 从数据库加载到内存图谱
        await self._load_graph_from_db()
        
        # 构建统计
        processing_time = time.time() - start_time
        graph_stats = await self._calculate_offline_stats()
        
        # 生成报告
        report = f"""# 🚀 离线知识图谱构建完成

## ⚡ 性能统计
- **处理时间**: {processing_time:.2f}秒
- **处理文件**: {len(content_files)}个
- **处理文本**: {processed_texts:,}条
- **图谱节点**: {self.knowledge_graph.number_of_nodes():,}
- **图谱边**: {self.knowledge_graph.number_of_edges():,}
- **处理速度**: {processed_texts / processing_time:.1f} 文本/秒

## 📊 提取效率
- **实体提取率**: {total_entities / processed_texts:.1f} 实体/文本
- **关系提取率**: {total_relations / processed_texts:.1f} 关系/文本
- **图谱密度**: {nx.density(self.knowledge_graph):.4f}

{graph_stats}

## 🔍 技术特性
- ✅ 离线高速处理
- ✅ SQLite持久化存储
- ✅ 批量并行提取
- ✅ 增强正则模式
- ✅ 智能去重过滤

## 💾 输出文件
离线知识图谱已保存到: `{self.output_dir}/`
数据库文件: `{self.db_path}`
"""
        
        # 自动导出
        await self._export_offline_graph('html')
        await self._export_offline_graph('json')
        
        return report

    async def _process_file_offline(self, file_path: str) -> Tuple[int, int, int]:
        """离线文件处理"""
        with open(file_path, 'r', encoding='utf-8') as f:
            content_list = json.load(f)

        # 过滤和预处理文本
        valid_texts = []
        for item in content_list:
            if item.get('type') == 'text':
                text = item.get('text', '').strip()
                if text and len(text) > 10:  # 过滤太短的文本
                    valid_texts.append({
                        'text': text,
                        'page': item.get('page_idx', 0),
                        'level': item.get('text_level', 0)
                    })

        if not valid_texts:
            return 0, 0, 0

        # 批量处理
        total_entities = 0
        total_relations = 0

        # 分批处理以提高效率
        for i in range(0, len(valid_texts), self.config['batch_size']):
            batch = valid_texts[i:i + self.config['batch_size']]
            entities, relations = await self._process_text_batch_offline(batch)
            total_entities += entities
            total_relations += relations

        return total_entities, total_relations, len(valid_texts)

    async def _process_text_batch_offline(self, text_batch: List[Dict]) -> Tuple[int, int]:
        """离线批量处理文本"""
        entities_count = 0
        relations_count = 0

        # 并行提取实体和关系
        tasks = []
        for text_item in text_batch:
            task = self._extract_entities_offline(text_item['text'], text_item['page'])
            tasks.append(task)

        # 等待所有任务完成
        results = await asyncio.gather(*tasks, return_exceptions=True)

        # 批量存储到数据库
        entities_to_store = []
        relations_to_store = []

        for i, result in enumerate(results):
            if isinstance(result, Exception):
                print(f"⚠️  文本处理失败: {result}")
                continue

            entities, relations = result
            entities_to_store.extend(entities)
            relations_to_store.extend(relations)

        # 批量存储
        if entities_to_store:
            entities_count = await self._store_entities_batch(entities_to_store)

        if relations_to_store:
            relations_count = await self._store_relations_batch(relations_to_store)

        return entities_count, relations_count

    async def _extract_entities_offline(self, text: str, page: int) -> Tuple[List[Dict], List[Dict]]:
        """离线实体提取"""
        entities = []
        relations = []

        # 使用增强正则表达式提取
        for entity_type, patterns in self.entity_patterns.items():
            for pattern in patterns:
                try:
                    matches = re.finditer(pattern, text, re.IGNORECASE)
                    for match in matches:
                        if match.groups():
                            entity_name = match.group(1).strip()
                        else:
                            entity_name = match.group(0).strip()

                        if len(entity_name) > 1:  # 过滤单字符
                            entities.append({
                                'name': entity_name,
                                'type': entity_type,
                                'context': text[max(0, match.start()-30):match.end()+30],
                                'page': page,
                                'confidence': 0.9  # 正则匹配高置信度
                            })
                except Exception as e:
                    print(f"⚠️  正则提取失败: {e}")

        # 提取关系
        for pattern, relation_type in self.relation_patterns:
            try:
                matches = re.finditer(pattern, text, re.IGNORECASE)
                for match in matches:
                    if len(match.groups()) >= 2:
                        source = match.group(1).strip()
                        target = match.group(2).strip()
                        if source and target and source != target and len(source) > 1 and len(target) > 1:
                            relations.append({
                                'source': source,
                                'target': target,
                                'type': relation_type,
                                'context': match.group(0),
                                'page': page,
                                'confidence': 0.8
                            })
            except Exception as e:
                print(f"⚠️  关系提取失败: {e}")

        # 去重和过滤
        entities = self._deduplicate_entities(entities)
        relations = self._deduplicate_relations(relations)

        # 限制数量
        entities = entities[:self.config['max_entities_per_text']]
        relations = relations[:self.config['max_entities_per_text']]

        return entities, relations

    async def _store_entities_batch(self, entities: List[Dict]) -> int:
        """批量存储实体"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()

        stored_count = 0

        for entity in entities:
            entity_id = self._normalize_entity_id(entity['name'], entity['type'])

            # 检查是否已存在
            cursor.execute('SELECT confidence FROM entities WHERE id = ?', (entity_id,))
            existing = cursor.fetchone()

            if existing:
                # 更新置信度更高的实体
                if entity['confidence'] > existing[0]:
                    cursor.execute('''
                        UPDATE entities
                        SET name=?, type=?, confidence=?, page=?, context=?
                        WHERE id=?
                    ''', (entity['name'], entity['type'], entity['confidence'],
                         entity['page'], entity['context'], entity_id))
                    stored_count += 1
            else:
                # 插入新实体
                cursor.execute('''
                    INSERT INTO entities (id, name, type, confidence, page, context)
                    VALUES (?, ?, ?, ?, ?, ?)
                ''', (entity_id, entity['name'], entity['type'], entity['confidence'],
                     entity['page'], entity['context']))
                stored_count += 1

        conn.commit()
        conn.close()
        return stored_count

    async def _store_relations_batch(self, relations: List[Dict]) -> int:
        """批量存储关系"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()

        stored_count = 0

        for relation in relations:
            source_id = self._normalize_entity_id(relation['source'], 'entity')
            target_id = self._normalize_entity_id(relation['target'], 'entity')

            # 检查实体是否存在
            cursor.execute('SELECT id FROM entities WHERE id IN (?, ?)', (source_id, target_id))
            existing_entities = cursor.fetchall()

            if len(existing_entities) == 2:  # 两个实体都存在
                # 检查关系是否已存在
                cursor.execute('''
                    SELECT confidence FROM relations
                    WHERE source_id=? AND target_id=? AND relation_type=?
                ''', (source_id, target_id, relation['type']))

                existing_relation = cursor.fetchone()

                if existing_relation:
                    # 更新置信度更高的关系
                    if relation['confidence'] > existing_relation[0]:
                        cursor.execute('''
                            UPDATE relations
                            SET confidence=?, page=?, context=?
                            WHERE source_id=? AND target_id=? AND relation_type=?
                        ''', (relation['confidence'], relation['page'], relation['context'],
                             source_id, target_id, relation['type']))
                        stored_count += 1
                else:
                    # 插入新关系
                    cursor.execute('''
                        INSERT INTO relations (source_id, target_id, relation_type, confidence, page, context)
                        VALUES (?, ?, ?, ?, ?, ?)
                    ''', (source_id, target_id, relation['type'], relation['confidence'],
                         relation['page'], relation['context']))
                    stored_count += 1

        conn.commit()
        conn.close()
        return stored_count

    def _normalize_entity_id(self, name: str, entity_type: str) -> str:
        """标准化实体ID"""
        # 清理名称
        clean_name = re.sub(r'[^\w\u4e00-\u9fff]', '_', name.lower())
        clean_name = re.sub(r'_+', '_', clean_name).strip('_')
        return f"{entity_type}_{clean_name}"

    def _deduplicate_entities(self, entities: List[Dict]) -> List[Dict]:
        """实体去重"""
        seen = set()
        unique_entities = []

        for entity in entities:
            # 使用名称和类型作为去重键
            key = (entity['name'].lower(), entity['type'])
            if key not in seen:
                seen.add(key)
                unique_entities.append(entity)

        return unique_entities

    def _deduplicate_relations(self, relations: List[Dict]) -> List[Dict]:
        """关系去重"""
        seen = set()
        unique_relations = []

        for relation in relations:
            key = (relation['source'].lower(), relation['target'].lower(), relation['type'])
            if key not in seen:
                seen.add(key)
                unique_relations.append(relation)

        return unique_relations

    async def _load_graph_from_db(self):
        """从数据库加载图谱到内存"""
        self.knowledge_graph.clear()

        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()

        # 加载实体
        cursor.execute('SELECT id, name, type, confidence, page, context FROM entities')
        entities = cursor.fetchall()

        for entity_id, name, entity_type, confidence, page, context in entities:
            self.knowledge_graph.add_node(entity_id,
                                        name=name,
                                        type=entity_type,
                                        confidence=confidence,
                                        page=page,
                                        context=context)

        # 加载关系
        cursor.execute('''
            SELECT source_id, target_id, relation_type, confidence, page, context
            FROM relations
        ''')
        relations = cursor.fetchall()

        for source_id, target_id, relation_type, confidence, page, context in relations:
            if source_id in self.knowledge_graph.nodes and target_id in self.knowledge_graph.nodes:
                self.knowledge_graph.add_edge(source_id, target_id,
                                            type=relation_type,
                                            confidence=confidence,
                                            page=page,
                                            context=context)

        conn.close()
        print(f"📊 从数据库加载: {self.knowledge_graph.number_of_nodes()}个节点, {self.knowledge_graph.number_of_edges()}条边")

    async def _analyze_offline_graph(self) -> str:
        """分析离线知识图谱"""
        if self.knowledge_graph.number_of_nodes() == 0:
            # 尝试从数据库加载
            await self._load_graph_from_db()

            if self.knowledge_graph.number_of_nodes() == 0:
                return "❌ 知识图谱为空，请先构建图谱"

        # 基本统计
        num_nodes = self.knowledge_graph.number_of_nodes()
        num_edges = self.knowledge_graph.number_of_edges()
        density = nx.density(self.knowledge_graph)

        # 从数据库获取详细统计
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()

        # 实体类型分布
        cursor.execute('SELECT type, COUNT(*) FROM entities GROUP BY type ORDER BY COUNT(*) DESC')
        entity_types = dict(cursor.fetchall())

        # 关系类型分布
        cursor.execute('SELECT relation_type, COUNT(*) FROM relations GROUP BY relation_type ORDER BY COUNT(*) DESC')
        relation_types = dict(cursor.fetchall())

        # 置信度统计
        cursor.execute('SELECT AVG(confidence), MIN(confidence), MAX(confidence) FROM entities')
        entity_confidence = cursor.fetchone()
        entity_confidence = entity_confidence if entity_confidence[0] is not None else (0, 0, 0)

        cursor.execute('SELECT AVG(confidence), MIN(confidence), MAX(confidence) FROM relations')
        relation_confidence = cursor.fetchone()
        relation_confidence = relation_confidence if relation_confidence[0] is not None else (0, 0, 0)

        # 页面覆盖统计
        cursor.execute('SELECT COUNT(DISTINCT page) FROM entities')
        pages_covered = cursor.fetchone()[0]

        conn.close()

        # 中心性分析
        try:
            degree_centrality = nx.degree_centrality(self.knowledge_graph)
            top_nodes = sorted(degree_centrality.items(), key=lambda x: x[1], reverse=True)[:5]

            centrality_analysis = "### 🎯 中心性分析\n"
            for node_id, centrality in top_nodes:
                node_data = self.knowledge_graph.nodes[node_id]
                centrality_analysis += f"- **{node_data.get('name', node_id)}** ({node_data.get('type', 'unknown')}): {centrality:.3f}\n"
        except Exception as e:
            centrality_analysis = f"中心性分析失败: {e}"

        # 连通性分析
        try:
            undirected = self.knowledge_graph.to_undirected()
            num_components = nx.number_connected_components(undirected)
            largest_component = max(nx.connected_components(undirected), key=len)
            largest_size = len(largest_component)
        except Exception as e:
            num_components = "计算失败"
            largest_size = "计算失败"

        analysis = f"""# 🔍 离线知识图谱分析报告

## 📊 基本统计
- **节点数**: {num_nodes:,}
- **边数**: {num_edges:,}
- **图谱密度**: {density:.4f}
- **页面覆盖**: {pages_covered}页

## 🏷️ 实体类型分布
{chr(10).join(f'- **{entity_type}**: {count:,}个' for entity_type, count in entity_types.items())}

## 🔗 关系类型分布
{chr(10).join(f'- **{relation_type}**: {count:,}个' for relation_type, count in relation_types.items())}

## 📈 质量指标
- **实体置信度**: 平均 {entity_confidence[0]:.3f}, 范围 [{entity_confidence[1]:.3f}, {entity_confidence[2]:.3f}]
- **关系置信度**: 平均 {relation_confidence[0]:.3f}, 范围 [{relation_confidence[1]:.3f}, {relation_confidence[2]:.3f}]

{centrality_analysis}

## 🌐 连通性分析
- **连通分量数**: {num_components}
- **最大连通分量**: {largest_size}个节点
- **连通率**: {largest_size/num_nodes*100:.1f}%

## ⚡ 存储信息
- **数据库文件**: {self.db_path}
- **文件大小**: {os.path.getsize(self.db_path) / 1024:.1f}KB
- **存储效率**: {(num_nodes + num_edges) / (os.path.getsize(self.db_path) / 1024):.1f} 条目/KB
"""

        return analysis

    async def _query_entities_offline(self, query: str) -> str:
        """离线查询实体"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()

        # 构建查询条件
        query_lower = query.lower()

        # 多种查询策略

        # 1. 精确名称匹配
        cursor.execute('''
            SELECT id, name, type, confidence, page, context
            FROM entities
            WHERE LOWER(name) = ?
            ORDER BY confidence DESC
        ''', (query_lower,))
        exact_matches = cursor.fetchall()

        # 2. 名称包含查询
        cursor.execute('''
            SELECT id, name, type, confidence, page, context
            FROM entities
            WHERE LOWER(name) LIKE ?
            ORDER BY confidence DESC
            LIMIT 10
        ''', (f'%{query_lower}%',))
        name_matches = cursor.fetchall()

        # 3. 上下文包含查询
        cursor.execute('''
            SELECT id, name, type, confidence, page, context
            FROM entities
            WHERE LOWER(context) LIKE ?
            ORDER BY confidence DESC
            LIMIT 10
        ''', (f'%{query_lower}%',))
        context_matches = cursor.fetchall()

        # 4. 类型匹配
        cursor.execute('''
            SELECT id, name, type, confidence, page, context
            FROM entities
            WHERE LOWER(type) = ?
            ORDER BY confidence DESC
            LIMIT 10
        ''', (query_lower,))
        type_matches = cursor.fetchall()

        conn.close()

        # 合并和去重结果
        all_results = []
        seen_ids = set()

        # 按优先级添加结果
        for result_set, priority in [(exact_matches, 1), (name_matches, 2), (context_matches, 3), (type_matches, 4)]:
            for result in result_set:
                entity_id = result[0]
                if entity_id not in seen_ids:
                    seen_ids.add(entity_id)
                    all_results.append((result, priority))

        if not all_results:
            return f"❌ 未找到与'{query}'相关的实体"

        # 格式化结果
        response = f"# 🔍 实体查询结果: '{query}'\n\n"
        response += f"找到 {len(all_results)} 个相关实体:\n\n"

        for i, (result, priority) in enumerate(all_results[:10], 1):  # 只显示前10个
            entity_id, name, entity_type, confidence, page, context = result

            match_type = {1: "精确匹配", 2: "名称匹配", 3: "上下文匹配", 4: "类型匹配"}[priority]

            response += f"## {i}. {name}\n"
            response += f"- **类型**: {entity_type}\n"
            response += f"- **匹配方式**: {match_type}\n"
            response += f"- **置信度**: {confidence:.3f}\n"
            response += f"- **页面**: {page}\n"

            # 显示相关关系
            if entity_id in self.knowledge_graph.nodes:
                neighbors = list(self.knowledge_graph.neighbors(entity_id))
                if neighbors:
                    neighbor_names = []
                    for neighbor in neighbors[:3]:  # 只显示前3个
                        neighbor_data = self.knowledge_graph.nodes[neighbor]
                        neighbor_names.append(neighbor_data.get('name', neighbor))
                    response += f"- **相关实体**: {', '.join(neighbor_names)}\n"

            # 显示上下文片段
            if context and len(context) > 50:
                response += f"- **上下文**: {context[:100]}...\n"

            response += "\n"

        return response

    async def _calculate_offline_stats(self) -> str:
        """计算离线统计信息"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()

        # 数据库统计
        cursor.execute('SELECT COUNT(*) FROM entities')
        total_entities = cursor.fetchone()[0]

        cursor.execute('SELECT COUNT(*) FROM relations')
        total_relations = cursor.fetchone()[0]

        # 质量统计
        cursor.execute('SELECT AVG(confidence) FROM entities WHERE confidence > 0')
        avg_entity_confidence = cursor.fetchone()[0] or 0

        cursor.execute('SELECT AVG(confidence) FROM relations WHERE confidence > 0')
        avg_relation_confidence = cursor.fetchone()[0] or 0

        # 覆盖度统计
        cursor.execute('SELECT COUNT(DISTINCT page) FROM entities')
        pages_with_entities = cursor.fetchone()[0]

        cursor.execute('SELECT MIN(page), MAX(page) FROM entities')
        page_range = cursor.fetchone()

        conn.close()

        stats = f"""
### 📊 数据库统计
- **存储实体**: {total_entities:,}个
- **存储关系**: {total_relations:,}个
- **数据库大小**: {os.path.getsize(self.db_path) / 1024:.1f}KB

### 📈 质量指标
- **实体平均置信度**: {avg_entity_confidence:.3f}
- **关系平均置信度**: {avg_relation_confidence:.3f}
- **页面覆盖**: {pages_with_entities}页 (范围: {page_range[0]}-{page_range[1]})

### 🎯 提取效率
- **实体密度**: {total_entities / pages_with_entities:.1f} 实体/页
- **关系密度**: {total_relations / pages_with_entities:.1f} 关系/页
- **关系/实体比**: {total_relations / total_entities:.2f}
"""
        return stats

    async def _clear_graph(self) -> str:
        """清空图谱"""
        # 清空内存图谱
        self.knowledge_graph.clear()

        # 清空数据库
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()

        cursor.execute('DELETE FROM relations')
        cursor.execute('DELETE FROM entities')

        conn.commit()
        conn.close()

        # 单独执行VACUUM（不能在事务中执行）
        conn = sqlite3.connect(self.db_path)
        conn.execute('VACUUM')
        conn.close()

        return "✅ 知识图谱已清空"

    async def _export_offline_graph(self, format_type: str = 'html') -> str:
        """导出离线知识图谱"""
        if self.knowledge_graph.number_of_nodes() == 0:
            await self._load_graph_from_db()

            if self.knowledge_graph.number_of_nodes() == 0:
                return "❌ 知识图谱为空，无法导出"

        try:
            if format_type == 'html':
                return await self._export_to_offline_html()
            elif format_type == 'json':
                return await self._export_to_offline_json()
            elif format_type == 'png':
                return await self._export_to_offline_png()
            elif format_type == 'sql':
                return await self._export_to_sql()
            else:
                return f"❌ 不支持的导出格式: {format_type}"
        except Exception as e:
            return f"❌ 导出失败: {e}"

    async def _export_to_offline_html(self) -> str:
        """导出离线HTML"""
        try:
            from pyvis.network import Network
        except ImportError:
            return "❌ 缺少pyvis依赖，请安装: pip install pyvis"

        net = Network(height="900px", width="100%", bgcolor="#1a1a1a", font_color="white")

        # 设置物理引擎
        net.set_options("""
        var options = {
          "physics": {
            "enabled": true,
            "stabilization": {"iterations": 300},
            "barnesHut": {
              "gravitationalConstant": -8000,
              "centralGravity": 0.3,
              "springLength": 95,
              "springConstant": 0.04,
              "damping": 0.09
            }
          },
          "nodes": {
            "font": {"size": 14, "color": "white"},
            "borderWidth": 2,
            "shadow": true
          },
          "edges": {
            "font": {"size": 10, "color": "white"},
            "shadow": true,
            "smooth": {"type": "continuous"}
          }
        }
        """)

        # 颜色映射
        color_map = {
            'register': '#ff6b6b',
            'module': '#4ecdc4',
            'function': '#45b7d1',
            'parameter': '#96ceb4',
            'address': '#feca57',
            'bit_field': '#ff9ff3',
            'chapter': '#54a0ff',
            'section': '#5f27cd',
            'concept': '#00d2d3',
            'entity': '#cccccc'
        }

        # 添加节点
        for node, data in self.knowledge_graph.nodes(data=True):
            entity_type = data.get('type', 'concept')
            color = color_map.get(entity_type, '#cccccc')
            confidence = data.get('confidence', 0)

            # 节点大小基于度数和置信度
            degree = self.knowledge_graph.degree(node)
            size = min(60, 20 + degree * 3 + confidence * 15)

            title = f"""
            名称: {data.get('name', node)}
            类型: {entity_type}
            置信度: {confidence:.3f}
            页面: {data.get('page', 'N/A')}
            度数: {degree}
            上下文: {data.get('context', '')[:100]}...
            """

            net.add_node(node,
                        label=data.get('name', node)[:25],
                        color=color,
                        title=title.strip(),
                        size=size)

        # 添加边
        for source, target, data in self.knowledge_graph.edges(data=True):
            relation_type = data.get('type', 'related_to')
            confidence = data.get('confidence', 0)

            # 边宽度基于置信度
            width = max(1, confidence * 4)

            net.add_edge(source, target,
                        label=relation_type,
                        title=f"关系: {relation_type}\n置信度: {confidence:.3f}\n上下文: {data.get('context', '')}",
                        width=width)

        output_path = os.path.join(self.output_dir, "offline_knowledge_graph.html")
        net.save_graph(output_path)

        return f"✅ 离线HTML图谱已导出: {output_path}"

    async def _export_to_offline_json(self) -> str:
        """导出离线JSON"""
        graph_data = {
            'metadata': {
                'nodes': self.knowledge_graph.number_of_nodes(),
                'edges': self.knowledge_graph.number_of_edges(),
                'density': nx.density(self.knowledge_graph),
                'export_time': time.time(),
                'database_size': os.path.getsize(self.db_path)
            },
            'nodes': [],
            'edges': []
        }

        # 导出节点
        for node, data in self.knowledge_graph.nodes(data=True):
            node_data = {
                'id': node,
                'name': data.get('name', node),
                'type': data.get('type', 'unknown'),
                'confidence': data.get('confidence', 0),
                'page': data.get('page', 0),
                'context': data.get('context', ''),
                'degree': self.knowledge_graph.degree(node)
            }
            graph_data['nodes'].append(node_data)

        # 导出边
        for source, target, data in self.knowledge_graph.edges(data=True):
            edge_data = {
                'source': source,
                'target': target,
                'type': data.get('type', 'related_to'),
                'confidence': data.get('confidence', 0),
                'context': data.get('context', ''),
                'page': data.get('page', 0)
            }
            graph_data['edges'].append(edge_data)

        output_path = os.path.join(self.output_dir, "offline_knowledge_graph.json")
        with open(output_path, 'w', encoding='utf-8') as f:
            json.dump(graph_data, f, ensure_ascii=False, indent=2)

        return f"✅ 离线JSON格式图谱已导出: {output_path}"

    async def _export_to_offline_png(self) -> str:
        """导出离线PNG"""
        plt.figure(figsize=(24, 18))

        # 使用force-directed布局
        pos = nx.spring_layout(self.knowledge_graph, k=3, iterations=100)

        # 按类型分组绘制节点
        entity_types = {}
        for node, data in self.knowledge_graph.nodes(data=True):
            entity_type = data.get('type', 'concept')
            if entity_type not in entity_types:
                entity_types[entity_type] = []
            entity_types[entity_type].append(node)

        # 颜色映射
        colors = ['red', 'blue', 'green', 'orange', 'purple', 'brown', 'pink', 'gray', 'olive', 'cyan']

        for i, (entity_type, nodes) in enumerate(entity_types.items()):
            color = colors[i % len(colors)]

            # 节点大小基于度数和置信度
            node_sizes = []
            for node in nodes:
                degree = self.knowledge_graph.degree(node)
                confidence = self.knowledge_graph.nodes[node].get('confidence', 0)
                size = min(1500, 200 + degree * 100 + confidence * 300)
                node_sizes.append(size)

            nx.draw_networkx_nodes(self.knowledge_graph, pos,
                                  nodelist=nodes,
                                  node_color=color,
                                  node_size=node_sizes,
                                  alpha=0.8,
                                  label=f"{entity_type} ({len(nodes)})")

        # 绘制边
        nx.draw_networkx_edges(self.knowledge_graph, pos,
                              alpha=0.3,
                              edge_color='gray',
                              width=0.5)

        # 绘制标签（只显示重要节点）
        important_nodes = {}
        for node, data in self.knowledge_graph.nodes(data=True):
            degree = self.knowledge_graph.degree(node)
            confidence = data.get('confidence', 0)
            if degree > 2 or confidence > 0.8:  # 重要节点
                important_nodes[node] = data.get('name', node)[:20]

        nx.draw_networkx_labels(self.knowledge_graph, pos,
                               labels=important_nodes,
                               font_size=8,
                               font_color='black')

        plt.title("离线知识图谱", fontsize=20, pad=20)
        plt.legend(bbox_to_anchor=(1.05, 1), loc='upper left')
        plt.axis('off')

        output_path = os.path.join(self.output_dir, "offline_knowledge_graph.png")
        plt.savefig(output_path, dpi=300, bbox_inches='tight',
                   facecolor='white', edgecolor='none')
        plt.close()

        return f"✅ 离线PNG图片已导出: {output_path}"

    async def _export_to_sql(self) -> str:
        """导出SQL脚本"""
        output_path = os.path.join(self.output_dir, "knowledge_graph_backup.sql")

        conn = sqlite3.connect(self.db_path)

        with open(output_path, 'w', encoding='utf-8') as f:
            # 导出表结构和数据
            for line in conn.iterdump():
                f.write(f"{line}\n")

        conn.close()

        file_size = os.path.getsize(output_path)
        return f"✅ SQL备份已导出: {output_path} ({file_size / 1024:.1f}KB)"
