"""
知识图谱构建智能体
将Mineru输出文件转换为知识图谱
"""
import os
import json
import re
from typing import Dict, Any, List, Tuple, Set
import networkx as nx
from pyvis.network import Network
import matplotlib.pyplot as plt
from .base_agent import BaseAgent, create_agent_response

class KnowledgeGraphBuilder(BaseAgent):
    """知识图谱构建智能体"""
    
    def __init__(self, model_client, system_message: str, capabilities: Dict[str, Any]):
        super().__init__(
            name="KnowledgeGraphBuilder",
            model_client=model_client,
            system_message=system_message,
            capabilities=capabilities
        )
        
        # 知识图谱
        self.knowledge_graph = nx.DiGraph()
        
        # 实体类型定义
        self.entity_types = {
            'register': '寄存器',
            'module': '模块',
            'function': '功能',
            'parameter': '参数',
            'address': '地址',
            'bit_field': '位域',
            'chapter': '章节',
            'concept': '概念'
        }
        
        # 关系类型定义
        self.relation_types = {
            'contains': '包含',
            'belongs_to': '属于',
            'controls': '控制',
            'configures': '配置',
            'implements': '实现',
            'references': '引用',
            'depends_on': '依赖于',
            'related_to': '相关于'
        }
        
        # 输出目录
        self.output_dir = "knowledge_graphs"
        os.makedirs(self.output_dir, exist_ok=True)
    
    async def process_request(self, request: Dict[str, Any]) -> Dict[str, Any]:
        """处理知识图谱构建请求"""
        message = request.get("message", "")
        
        # 解析请求类型
        if "构建知识图谱" in message or "build knowledge graph" in message.lower():
            # 提取Mineru输出路径
            mineru_path = self._extract_mineru_path(message)
            if not mineru_path:
                mineru_path = "/Users/<USER>/Public/AutoGen/monkey_ocr_project/mineru_output"
            
            result = await self._build_knowledge_graph_from_mineru(mineru_path)
            return await create_agent_response(result)
        
        elif "分析知识图谱" in message:
            result = await self._analyze_knowledge_graph()
            return await create_agent_response(result)
        
        elif "导出知识图谱" in message:
            export_format = self._extract_export_format(message)
            result = await self._export_knowledge_graph(export_format)
            return await create_agent_response(result)
        
        else:
            return await create_agent_response(
                "请指定操作类型：\n"
                "- 构建知识图谱 [路径]\n"
                "- 分析知识图谱\n"
                "- 导出知识图谱 [格式]"
            )
    
    def _extract_mineru_path(self, message: str) -> str:
        """提取Mineru输出路径"""
        import re
        patterns = [
            r'路径[：:]\s*([^\s]+)',
            r'从\s+([^\s]+)\s+构建',
            r'输出目录[：:]\s*([^\s]+)'
        ]
        
        for pattern in patterns:
            match = re.search(pattern, message)
            if match:
                return match.group(1).strip()
        return None
    
    def _extract_export_format(self, message: str) -> str:
        """提取导出格式"""
        formats = ['html', 'json', 'gexf', 'graphml', 'png']
        for fmt in formats:
            if fmt in message.lower():
                return fmt
        return 'html'  # 默认格式
    
    async def _build_knowledge_graph_from_mineru(self, mineru_path: str) -> str:
        """从Mineru输出构建知识图谱"""
        print(f"🔨 开始从Mineru输出构建知识图谱: {mineru_path}")
        
        if not os.path.exists(mineru_path):
            return f"❌ Mineru输出路径不存在: {mineru_path}"
        
        # 清空现有图谱
        self.knowledge_graph.clear()
        
        # 扫描所有Mineru输出文件
        processed_files = []
        total_entities = 0
        total_relations = 0
        
        for root, dirs, files in os.walk(mineru_path):
            for file in files:
                if file.endswith('_content_list.json'):
                    file_path = os.path.join(root, file)
                    print(f"📄 处理文件: {file}")
                    
                    try:
                        entities, relations = await self._process_content_list(file_path)
                        total_entities += entities
                        total_relations += relations
                        processed_files.append(file)
                    except Exception as e:
                        print(f"❌ 处理文件失败 {file}: {e}")
        
        # 构建图谱统计
        graph_stats = self._calculate_graph_statistics()
        
        # 生成报告
        report = f"""# 🧠 知识图谱构建完成

## 📊 构建统计
- **处理文件数**: {len(processed_files)}
- **实体总数**: {self.knowledge_graph.number_of_nodes()}
- **关系总数**: {self.knowledge_graph.number_of_edges()}
- **图谱密度**: {nx.density(self.knowledge_graph):.4f}

## 📁 处理的文件
{chr(10).join(f'- {file}' for file in processed_files)}

## 🔍 图谱结构分析
{graph_stats}

## 💾 输出文件
知识图谱已保存到: `{self.output_dir}/`
- 交互式HTML: `knowledge_graph.html`
- JSON格式: `knowledge_graph.json`
- 图片格式: `knowledge_graph.png`
"""
        
        # 自动导出多种格式
        await self._export_knowledge_graph('html')
        await self._export_knowledge_graph('json')
        await self._export_knowledge_graph('png')
        
        return report
    
    async def _process_content_list(self, file_path: str) -> Tuple[int, int]:
        """处理content_list.json文件"""
        with open(file_path, 'r', encoding='utf-8') as f:
            content_list = json.load(f)
        
        entities_added = 0
        relations_added = 0
        current_chapter = None
        current_section = None
        
        for item in content_list:
            if item.get('type') == 'text':
                text = item.get('text', '').strip()
                text_level = item.get('text_level', 0)
                page_idx = item.get('page_idx', 0)
                
                if not text:
                    continue
                
                # 处理章节标题
                if text_level == 1:
                    current_chapter = text
                    chapter_id = f"chapter_{self._normalize_id(text)}"
                    self._add_entity(chapter_id, text, 'chapter', {'page': page_idx})
                    entities_added += 1
                    
                elif text_level == 2:
                    current_section = text
                    section_id = f"section_{self._normalize_id(text)}"
                    self._add_entity(section_id, text, 'section', {'page': page_idx})
                    entities_added += 1
                    
                    # 建立章节-小节关系
                    if current_chapter:
                        chapter_id = f"chapter_{self._normalize_id(current_chapter)}"
                        self._add_relation(chapter_id, section_id, 'contains')
                        relations_added += 1
                
                # 提取实体和关系
                extracted = await self._extract_entities_and_relations(text, page_idx)
                entities_added += len(extracted['entities'])
                relations_added += len(extracted['relations'])
                
                # 建立与当前章节的关系
                for entity in extracted['entities']:
                    if current_section:
                        section_id = f"section_{self._normalize_id(current_section)}"
                        self._add_relation(section_id, entity['id'], 'contains')
                        relations_added += 1
        
        return entities_added, relations_added
    
    async def _extract_entities_and_relations(self, text: str, page_idx: int) -> Dict[str, List]:
        """使用AI模型提取实体和关系"""
        
        # 构建提示
        prompt = f"""请从以下技术文档文本中提取实体和关系，用于构建知识图谱。

文本内容：
{text}

请提取以下类型的实体：
1. 寄存器名称（如：SYS_AFE_REG1）
2. 模块名称（如：ADC、GPIO、UART）
3. 功能描述（如：模数转换、时钟控制）
4. 参数名称（如：波特率、频率）
5. 地址值（如：0x40010000）
6. 位域（如：bit[7:0]）

请识别以下类型的关系：
1. 包含关系（A包含B）
2. 控制关系（A控制B）
3. 配置关系（A配置B）
4. 依赖关系（A依赖B）

请以JSON格式返回结果：
{{
    "entities": [
        {{"id": "实体ID", "name": "实体名称", "type": "实体类型", "description": "描述"}}
    ],
    "relations": [
        {{"source": "源实体ID", "target": "目标实体ID", "type": "关系类型", "description": "关系描述"}}
    ]
}}"""

        try:
            response = await self.model_client.create(
                messages=[{"role": "user", "content": prompt}]
            )
            
            if hasattr(response, 'content'):
                content = response.content
            elif hasattr(response, 'choices'):
                content = response.choices[0].message.content
            else:
                content = str(response)
            
            # 解析JSON响应
            extracted_data = self._parse_extraction_response(content)
            
            # 添加实体到图谱
            for entity in extracted_data['entities']:
                entity_id = f"{entity['type']}_{self._normalize_id(entity['name'])}"
                self._add_entity(entity_id, entity['name'], entity['type'], {
                    'description': entity.get('description', ''),
                    'page': page_idx
                })
                entity['id'] = entity_id  # 更新ID
            
            # 添加关系到图谱
            for relation in extracted_data['relations']:
                source_id = f"{relation.get('source_type', 'entity')}_{self._normalize_id(relation['source'])}"
                target_id = f"{relation.get('target_type', 'entity')}_{self._normalize_id(relation['target'])}"
                
                # 确保实体存在
                if source_id in self.knowledge_graph.nodes and target_id in self.knowledge_graph.nodes:
                    self._add_relation(source_id, target_id, relation['type'], {
                        'description': relation.get('description', ''),
                        'page': page_idx
                    })
            
            return extracted_data
            
        except Exception as e:
            print(f"⚠️  AI提取失败，使用规则提取: {e}")
            return self._rule_based_extraction(text, page_idx)
    
    def _parse_extraction_response(self, content: str) -> Dict[str, List]:
        """解析AI提取的响应"""
        try:
            # 尝试直接解析JSON
            if content.strip().startswith('{'):
                return json.loads(content)
            
            # 查找JSON块
            import re
            json_match = re.search(r'\{.*\}', content, re.DOTALL)
            if json_match:
                return json.loads(json_match.group())
            
            # 如果无法解析，返回空结果
            return {'entities': [], 'relations': []}
            
        except json.JSONDecodeError:
            return {'entities': [], 'relations': []}
    
    def _rule_based_extraction(self, text: str, page_idx: int) -> Dict[str, List]:
        """基于规则的实体关系提取"""
        entities = []
        relations = []
        
        # 寄存器模式
        register_pattern = r'\b([A-Z][A-Z0-9_]+)\s*寄存器'
        registers = re.findall(register_pattern, text)
        for reg in registers:
            entities.append({
                'id': f"register_{self._normalize_id(reg)}",
                'name': reg,
                'type': 'register',
                'description': f'{reg}寄存器'
            })
        
        # 地址模式
        address_pattern = r'0x[0-9A-Fa-f]+'
        addresses = re.findall(address_pattern, text)
        for addr in addresses:
            entities.append({
                'id': f"address_{self._normalize_id(addr)}",
                'name': addr,
                'type': 'address',
                'description': f'地址{addr}'
            })
        
        # 模块模式
        modules = ['ADC', 'GPIO', 'UART', 'MCPWM', 'DMA', 'PWM', 'SPI', 'I2C', 'CAN', 'FLASH', 'RAM']
        for module in modules:
            if module in text.upper():
                entities.append({
                    'id': f"module_{self._normalize_id(module)}",
                    'name': module,
                    'type': 'module',
                    'description': f'{module}模块'
                })
        
        return {'entities': entities, 'relations': relations}
    
    def _normalize_id(self, name: str) -> str:
        """标准化实体ID"""
        # 移除特殊字符，转换为小写
        normalized = re.sub(r'[^\w\u4e00-\u9fff]', '_', name.lower())
        return normalized.strip('_')
    
    def _add_entity(self, entity_id: str, name: str, entity_type: str, attributes: Dict = None):
        """添加实体到知识图谱"""
        if attributes is None:
            attributes = {}
        
        self.knowledge_graph.add_node(entity_id, 
                                    name=name, 
                                    type=entity_type,
                                    **attributes)
    
    def _add_relation(self, source_id: str, target_id: str, relation_type: str, attributes: Dict = None):
        """添加关系到知识图谱"""
        if attributes is None:
            attributes = {}
        
        self.knowledge_graph.add_edge(source_id, target_id,
                                    type=relation_type,
                                    **attributes)
    
    def _calculate_graph_statistics(self) -> str:
        """计算图谱统计信息"""
        if self.knowledge_graph.number_of_nodes() == 0:
            return "图谱为空"
        
        # 基本统计
        num_nodes = self.knowledge_graph.number_of_nodes()
        num_edges = self.knowledge_graph.number_of_edges()
        density = nx.density(self.knowledge_graph)
        
        # 实体类型统计
        entity_types = {}
        for node, data in self.knowledge_graph.nodes(data=True):
            entity_type = data.get('type', 'unknown')
            entity_types[entity_type] = entity_types.get(entity_type, 0) + 1
        
        # 关系类型统计
        relation_types = {}
        for source, target, data in self.knowledge_graph.edges(data=True):
            relation_type = data.get('type', 'unknown')
            relation_types[relation_type] = relation_types.get(relation_type, 0) + 1
        
        # 连通性分析
        if num_nodes > 0:
            try:
                # 转换为无向图计算连通分量
                undirected = self.knowledge_graph.to_undirected()
                num_components = nx.number_connected_components(undirected)
                largest_component_size = len(max(nx.connected_components(undirected), key=len))
            except:
                num_components = "计算失败"
                largest_component_size = "计算失败"
        else:
            num_components = 0
            largest_component_size = 0
        
        stats = f"""
### 实体类型分布
{chr(10).join(f'- {entity_type}: {count}个' for entity_type, count in entity_types.items())}

### 关系类型分布
{chr(10).join(f'- {relation_type}: {count}个' for relation_type, count in relation_types.items())}

### 连通性分析
- 连通分量数: {num_components}
- 最大连通分量大小: {largest_component_size}
- 图谱密度: {density:.4f}
"""
        return stats
    
    async def _analyze_knowledge_graph(self) -> str:
        """分析知识图谱"""
        if self.knowledge_graph.number_of_nodes() == 0:
            return "❌ 知识图谱为空，请先构建图谱"
        
        stats = self._calculate_graph_statistics()
        
        # 中心性分析
        centrality_analysis = self._analyze_centrality()
        
        # 社区检测
        community_analysis = self._analyze_communities()
        
        analysis = f"""# 🔍 知识图谱分析报告

## 📊 基本统计
- **节点数**: {self.knowledge_graph.number_of_nodes()}
- **边数**: {self.knowledge_graph.number_of_edges()}
- **密度**: {nx.density(self.knowledge_graph):.4f}

{stats}

## 🎯 中心性分析
{centrality_analysis}

## 🏘️ 社区结构
{community_analysis}
"""
        
        return analysis
    
    def _analyze_centrality(self) -> str:
        """分析节点中心性"""
        try:
            # 度中心性
            degree_centrality = nx.degree_centrality(self.knowledge_graph)
            top_degree = sorted(degree_centrality.items(), key=lambda x: x[1], reverse=True)[:5]
            
            # 介数中心性
            betweenness_centrality = nx.betweenness_centrality(self.knowledge_graph)
            top_betweenness = sorted(betweenness_centrality.items(), key=lambda x: x[1], reverse=True)[:5]
            
            analysis = f"""
### 度中心性Top5（连接最多的节点）
{chr(10).join(f'- {self.knowledge_graph.nodes[node]["name"]}: {score:.3f}' for node, score in top_degree)}

### 介数中心性Top5（桥梁节点）
{chr(10).join(f'- {self.knowledge_graph.nodes[node]["name"]}: {score:.3f}' for node, score in top_betweenness)}
"""
            return analysis
        except Exception as e:
            return f"中心性分析失败: {e}"
    
    def _analyze_communities(self) -> str:
        """分析社区结构"""
        try:
            # 转换为无向图
            undirected = self.knowledge_graph.to_undirected()
            
            # 使用Louvain算法检测社区
            import networkx.algorithms.community as nx_comm
            communities = list(nx_comm.greedy_modularity_communities(undirected))
            
            analysis = f"检测到 {len(communities)} 个社区:\n"
            for i, community in enumerate(communities, 1):
                if len(community) > 1:  # 只显示有多个节点的社区
                    community_names = [self.knowledge_graph.nodes[node]["name"] for node in list(community)[:5]]
                    analysis += f"- 社区{i} ({len(community)}个节点): {', '.join(community_names)}\n"
            
            return analysis
        except Exception as e:
            return f"社区分析失败: {e}"
    
    async def _export_knowledge_graph(self, format_type: str = 'html') -> str:
        """导出知识图谱"""
        if self.knowledge_graph.number_of_nodes() == 0:
            return "❌ 知识图谱为空，无法导出"
        
        try:
            if format_type == 'html':
                return self._export_to_html()
            elif format_type == 'json':
                return self._export_to_json()
            elif format_type == 'png':
                return self._export_to_png()
            elif format_type == 'gexf':
                return self._export_to_gexf()
            else:
                return f"❌ 不支持的导出格式: {format_type}"
        except Exception as e:
            return f"❌ 导出失败: {e}"
    
    def _export_to_html(self) -> str:
        """导出为交互式HTML"""
        net = Network(height="800px", width="100%", bgcolor="#222222", font_color="white")
        
        # 定义颜色映射
        color_map = {
            'register': '#ff6b6b',
            'module': '#4ecdc4', 
            'function': '#45b7d1',
            'parameter': '#96ceb4',
            'address': '#feca57',
            'bit_field': '#ff9ff3',
            'chapter': '#54a0ff',
            'section': '#5f27cd',
            'concept': '#00d2d3'
        }
        
        # 添加节点
        for node, data in self.knowledge_graph.nodes(data=True):
            entity_type = data.get('type', 'concept')
            color = color_map.get(entity_type, '#cccccc')
            
            net.add_node(node, 
                        label=data.get('name', node),
                        color=color,
                        title=f"类型: {entity_type}\n描述: {data.get('description', '')}",
                        size=20)
        
        # 添加边
        for source, target, data in self.knowledge_graph.edges(data=True):
            relation_type = data.get('type', 'related_to')
            net.add_edge(source, target, 
                        label=relation_type,
                        title=data.get('description', ''))
        
        # 设置物理引擎
        net.set_options("""
        var options = {
          "physics": {
            "enabled": true,
            "stabilization": {"iterations": 100}
          }
        }
        """)
        
        output_path = os.path.join(self.output_dir, "knowledge_graph.html")
        net.save_graph(output_path)
        
        return f"✅ 交互式HTML图谱已导出: {output_path}"
    
    def _export_to_json(self) -> str:
        """导出为JSON格式"""
        graph_data = {
            'nodes': [],
            'edges': []
        }
        
        # 导出节点
        for node, data in self.knowledge_graph.nodes(data=True):
            graph_data['nodes'].append({
                'id': node,
                'name': data.get('name', node),
                'type': data.get('type', 'unknown'),
                'description': data.get('description', ''),
                'page': data.get('page', 0)
            })
        
        # 导出边
        for source, target, data in self.knowledge_graph.edges(data=True):
            graph_data['edges'].append({
                'source': source,
                'target': target,
                'type': data.get('type', 'related_to'),
                'description': data.get('description', ''),
                'page': data.get('page', 0)
            })
        
        output_path = os.path.join(self.output_dir, "knowledge_graph.json")
        with open(output_path, 'w', encoding='utf-8') as f:
            json.dump(graph_data, f, ensure_ascii=False, indent=2)
        
        return f"✅ JSON格式图谱已导出: {output_path}"
    
    def _export_to_png(self) -> str:
        """导出为PNG图片"""
        plt.figure(figsize=(20, 16))
        
        # 使用spring布局
        pos = nx.spring_layout(self.knowledge_graph, k=3, iterations=50)
        
        # 绘制节点
        node_colors = []
        color_map = {
            'register': 'red',
            'module': 'blue', 
            'function': 'green',
            'parameter': 'orange',
            'address': 'purple',
            'chapter': 'brown',
            'section': 'pink'
        }
        
        for node, data in self.knowledge_graph.nodes(data=True):
            entity_type = data.get('type', 'concept')
            node_colors.append(color_map.get(entity_type, 'gray'))
        
        nx.draw_networkx_nodes(self.knowledge_graph, pos, 
                              node_color=node_colors, 
                              node_size=300, 
                              alpha=0.8)
        
        # 绘制边
        nx.draw_networkx_edges(self.knowledge_graph, pos, 
                              alpha=0.5, 
                              edge_color='gray')
        
        # 绘制标签
        labels = {node: data.get('name', node)[:10] for node, data in self.knowledge_graph.nodes(data=True)}
        nx.draw_networkx_labels(self.knowledge_graph, pos, labels, font_size=8)
        
        plt.title("LKS32MC08x 知识图谱", fontsize=16)
        plt.axis('off')
        
        output_path = os.path.join(self.output_dir, "knowledge_graph.png")
        plt.savefig(output_path, dpi=300, bbox_inches='tight')
        plt.close()
        
        return f"✅ PNG图片已导出: {output_path}"
    
    def _export_to_gexf(self) -> str:
        """导出为GEXF格式（Gephi可读）"""
        output_path = os.path.join(self.output_dir, "knowledge_graph.gexf")
        nx.write_gexf(self.knowledge_graph, output_path)
        return f"✅ GEXF格式图谱已导出: {output_path}"
