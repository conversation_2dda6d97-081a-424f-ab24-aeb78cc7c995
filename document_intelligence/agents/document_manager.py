"""
文档管理员智能体
负责文档解析、检索和知识管理
"""
import re
import os
from typing import Dict, Any, List, Optional, Tuple
from .base_agent import BaseAgent, create_agent_response

class DocumentManager(BaseAgent):
    """文档管理员智能体"""
    
    def __init__(self, model_client, system_message: str, capabilities: Dict[str, Any]):
        super().__init__(
            name="DocumentManager",
            model_client=model_client,
            system_message=system_message,
            capabilities=capabilities
        )
        
        # 文档内容缓存
        self.document_cache = {}
        self.register_map = {}
        self.chapter_index = {}
        
        # 加载文档
        self._load_document()
    
    def _load_document(self):
        """加载和解析文档"""
        try:
            # 获取文档路径
            doc_path = os.path.join(
                os.path.dirname(os.path.dirname(os.path.abspath(__file__))),
                "..", "full.md"
            )
            
            if os.path.exists(doc_path):
                with open(doc_path, 'r', encoding='utf-8') as f:
                    content = f.read()
                
                self.document_cache["full_content"] = content
                self._parse_document_structure(content)
                self._build_register_map(content)
                
        except Exception as e:
            print(f"加载文档失败: {e}")
    
    def _parse_document_structure(self, content: str):
        """解析文档结构"""
        # 提取章节标题
        chapter_pattern = r'^#\s+(.+)$'
        section_pattern = r'^##\s+(.+)$'
        subsection_pattern = r'^###\s+(.+)$'
        
        lines = content.split('\n')
        current_chapter = None
        current_section = None
        
        for i, line in enumerate(lines):
            # 章节
            if re.match(chapter_pattern, line):
                current_chapter = re.match(chapter_pattern, line).group(1).strip()
                self.chapter_index[current_chapter] = {
                    "line_number": i,
                    "sections": {},
                    "content_start": i
                }
            
            # 小节
            elif re.match(section_pattern, line) and current_chapter:
                current_section = re.match(section_pattern, line).group(1).strip()
                self.chapter_index[current_chapter]["sections"][current_section] = {
                    "line_number": i,
                    "subsections": {}
                }
            
            # 子小节
            elif re.match(subsection_pattern, line) and current_chapter and current_section:
                subsection = re.match(subsection_pattern, line).group(1).strip()
                self.chapter_index[current_chapter]["sections"][current_section]["subsections"][subsection] = {
                    "line_number": i
                }
    
    def _build_register_map(self, content: str):
        """构建寄存器映射"""
        # 寄存器名称模式
        register_patterns = [
            r'([A-Z][A-Z0-9_]+)\s+寄存器',
            r'([A-Z][A-Z0-9_]+)寄存器',
            r'([A-Z][A-Z0-9_]+)\s+配置寄存器',
            r'([A-Z][A-Z0-9_]+)\s+控制寄存器',
            r'([A-Z][A-Z0-9_]+)\s+状态寄存器'
        ]
        
        lines = content.split('\n')
        for i, line in enumerate(lines):
            for pattern in register_patterns:
                matches = re.findall(pattern, line)
                for match in matches:
                    if match not in self.register_map:
                        self.register_map[match] = []
                    self.register_map[match].append({
                        "line_number": i,
                        "context": line.strip(),
                        "surrounding_lines": lines[max(0, i-2):i+3]
                    })
    
    async def process_request(self, request: Dict[str, Any]) -> Dict[str, Any]:
        """处理文档相关请求"""
        message = request.get("message", "")
        context = request.get("context", {})
        
        # 分析请求类型
        request_type = self._analyze_request_type(message)
        
        if request_type == "register_query":
            return await self._handle_register_query(message, context)
        elif request_type == "chapter_search":
            return await self._handle_chapter_search(message, context)
        elif request_type == "specification_lookup":
            return await self._handle_specification_lookup(message, context)
        else:
            return await self._handle_general_query(message, context)
    
    def _analyze_request_type(self, message: str) -> str:
        """分析请求类型"""
        message_lower = message.lower()
        
        # 寄存器查询
        if any(keyword in message_lower for keyword in ["寄存器", "register", "reg", "地址", "配置"]):
            return "register_query"
        
        # 章节搜索
        if any(keyword in message_lower for keyword in ["章节", "目录", "第", "章", "节"]):
            return "chapter_search"
        
        # 规格查询
        if any(keyword in message_lower for keyword in ["规格", "参数", "特性", "功能", "spec"]):
            return "specification_lookup"
        
        return "general_query"
    
    async def _handle_register_query(self, message: str, context: Dict) -> Dict[str, Any]:
        """处理寄存器查询"""
        # 提取寄存器名称
        register_names = self._extract_register_names(message)
        
        if not register_names:
            return await create_agent_response(
                "未能识别到具体的寄存器名称。请提供完整的寄存器名称，例如：SYS_AFE_REG1、FLASH_CFG等。"
            )
        
        results = []
        for reg_name in register_names:
            reg_info = self._get_register_info(reg_name)
            if reg_info:
                results.append(reg_info)
        
        if results:
            response = self._format_register_response(results)
            return await create_agent_response(response, {"registers": results})
        else:
            return await create_agent_response(
                f"未找到寄存器 {', '.join(register_names)} 的相关信息。请检查寄存器名称是否正确。"
            )
    
    def _extract_register_names(self, message: str) -> List[str]:
        """从消息中提取寄存器名称"""
        # 寄存器名称模式
        patterns = [
            r'\b([A-Z][A-Z0-9_]{2,})\b',  # 大写字母开头的标识符
            r'\b(SYS_[A-Z0-9_]+)\b',      # SYS_开头的寄存器
            r'\b(FLASH_[A-Z0-9_]+)\b',    # FLASH_开头的寄存器
            r'\b(GPIO[A-Z0-9_]*)\b',      # GPIO相关寄存器
            r'\b(ADC[0-9_]*[A-Z0-9_]*)\b', # ADC相关寄存器
        ]
        
        found_registers = set()
        for pattern in patterns:
            matches = re.findall(pattern, message.upper())
            for match in matches:
                if match in self.register_map:
                    found_registers.add(match)
        
        return list(found_registers)
    
    def _get_register_info(self, register_name: str) -> Optional[Dict]:
        """获取寄存器信息"""
        if register_name not in self.register_map:
            return None
        
        reg_entries = self.register_map[register_name]
        
        # 获取最相关的条目（通常是第一个）
        main_entry = reg_entries[0]
        
        # 提取周围的内容作为描述
        surrounding_text = '\n'.join(main_entry["surrounding_lines"])
        
        return {
            "name": register_name,
            "line_number": main_entry["line_number"],
            "context": main_entry["context"],
            "description": surrounding_text,
            "occurrences": len(reg_entries)
        }
    
    def _format_register_response(self, register_info_list: List[Dict]) -> str:
        """格式化寄存器响应"""
        response_parts = []
        
        for reg_info in register_info_list:
            response_parts.append(f"""
📋 **{reg_info['name']} 寄存器**
📍 位置：第 {reg_info['line_number']} 行
📝 描述：{reg_info['context']}

详细信息：
{reg_info['description']}

出现次数：{reg_info['occurrences']} 次
""")
        
        return '\n'.join(response_parts)
    
    async def _handle_chapter_search(self, message: str, context: Dict) -> Dict[str, Any]:
        """处理章节搜索"""
        # 简单的章节匹配
        matching_chapters = []
        
        for chapter_name in self.chapter_index.keys():
            if any(keyword.lower() in chapter_name.lower() for keyword in message.split()):
                matching_chapters.append(chapter_name)
        
        if matching_chapters:
            response = "找到以下相关章节：\n\n"
            for chapter in matching_chapters:
                chapter_info = self.chapter_index[chapter]
                response += f"📖 **{chapter}**\n"
                response += f"   位置：第 {chapter_info['line_number']} 行\n"
                if chapter_info['sections']:
                    response += f"   包含 {len(chapter_info['sections'])} 个小节\n"
                response += "\n"
            
            return await create_agent_response(response, {"chapters": matching_chapters})
        else:
            return await create_agent_response("未找到匹配的章节。请尝试使用更具体的关键词。")
    
    async def _handle_specification_lookup(self, message: str, context: Dict) -> Dict[str, Any]:
        """处理规格查询"""
        # 这里可以实现更复杂的规格查询逻辑
        return await create_agent_response(
            "规格查询功能正在开发中。请使用寄存器查询或章节搜索功能。"
        )
    
    async def _handle_general_query(self, message: str, context: Dict) -> Dict[str, Any]:
        """处理一般查询"""
        # 使用AutoGen智能体进行一般性回答
        try:
            # 构建包含文档上下文的提示
            enhanced_message = f"""
基于LKS32MC08x用户手册内容回答以下问题：

用户问题：{message}

请基于文档内容提供准确的技术信息。如果需要具体的寄存器信息或章节内容，请明确指出。
"""
            
            # 这里应该调用AutoGen智能体，但为了简化，我们返回一个基本响应
            response = f"收到查询：{message}\n\n这是一个一般性查询，建议使用更具体的关键词来获得更准确的信息。"
            
            return await create_agent_response(response)
            
        except Exception as e:
            return await create_agent_response(f"处理查询时发生错误：{str(e)}")
    
    def get_document_stats(self) -> Dict[str, Any]:
        """获取文档统计信息"""
        return {
            "total_chapters": len(self.chapter_index),
            "total_registers": len(self.register_map),
            "document_loaded": bool(self.document_cache),
            "cache_size": len(str(self.document_cache))
        }
