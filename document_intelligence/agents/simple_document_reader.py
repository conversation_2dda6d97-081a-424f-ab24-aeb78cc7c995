"""
简化版文档阅读理解智能体
不依赖外部向量化库，使用纯Python实现
"""
import os
import re
import json
from typing import Dict, Any, List, Optional, Tuple
from .base_agent import BaseAgent, create_agent_response

class SimpleDocumentReader(BaseAgent):
    """简化版文档阅读理解智能体"""

    def __init__(self, model_client, system_message: str, capabilities: Dict[str, Any]):
        super().__init__(
            name="SimpleDocumentReader",
            model_client=model_client,
            system_message=system_message,
            capabilities=capabilities
        )

        # 文档处理配置
        self.chunk_size = 800  # 每个文档块的字符数
        self.chunk_overlap = 100  # 块之间的重叠
        self.max_context_length = 4000  # 最大上下文长度

        # 文档存储
        self.document_chunks = []
        self.document_index = {}  # 关键词索引
        self.chapter_index = {}   # 章节索引

        # 对话上下文
        self.conversation_context = []

        # 初始化
        self._load_and_process_document()

    def _load_and_process_document(self):
        """加载和处理文档"""
        try:
            # 获取文档路径
            doc_path = os.path.join(
                os.path.dirname(os.path.dirname(os.path.abspath(__file__))),
                "data", "full.md"
            )

            if not os.path.exists(doc_path):
                doc_path = os.path.join(
                    os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))),
                    "full.md"
                )

            if os.path.exists(doc_path):
                with open(doc_path, 'r', encoding='utf-8') as f:
                    content = f.read()

                print(f"📄 文档加载成功，长度: {len(content)} 字符")

                # 处理文档
                self._process_document_content(content)
                self._build_keyword_index()

                print(f"📊 文档处理完成:")
                print(f"  • 文档块: {len(self.document_chunks)}")
                print(f"  • 章节: {len(self.chapter_index)}")
                print(f"  • 索引关键词: {len(self.document_index)}")

            else:
                print("❌ 未找到文档文件")

        except Exception as e:
            print(f"❌ 文档处理失败: {e}")

    def _process_document_content(self, content: str):
        """处理文档内容"""
        lines = content.split('\n')
        current_chapter = "文档开始"
        current_chunk = []
        current_chunk_size = 0
        chunk_id = 0

        for line_num, line in enumerate(lines, 1):
            # 检测章节标题
            if re.match(r'^#+\s+', line):
                # 保存当前块
                if current_chunk:
                    self._save_chunk(chunk_id, current_chunk, current_chapter, line_num)
                    chunk_id += 1
                    current_chunk = []
                    current_chunk_size = 0

                # 更新章节
                chapter_title = re.sub(r'^#+\s+', '', line).strip()
                current_chapter = chapter_title

                # 记录章节位置
                self.chapter_index[chapter_title] = {
                    'line_start': line_num,
                    'chunk_start': chunk_id
                }

            # 添加行到当前块
            current_chunk.append(line)
            current_chunk_size += len(line)

            # 检查是否需要分块
            if current_chunk_size >= self.chunk_size:
                self._save_chunk(chunk_id, current_chunk, current_chapter, line_num)
                chunk_id += 1

                # 保留重叠内容
                overlap_lines = current_chunk[-5:] if len(current_chunk) > 5 else current_chunk
                current_chunk = overlap_lines
                current_chunk_size = sum(len(l) for l in overlap_lines)

        # 保存最后一个块
        if current_chunk:
            self._save_chunk(chunk_id, current_chunk, current_chapter, len(lines))

    def _save_chunk(self, chunk_id: int, lines: List[str], chapter: str, line_num: int):
        """保存文档块"""
        text = '\n'.join(lines)

        chunk = {
            'id': chunk_id,
            'text': text,
            'chapter': chapter,
            'line_start': line_num - len(lines),
            'line_end': line_num,
            'size': len(text),
            'keywords': self._extract_chunk_keywords(text)
        }

        self.document_chunks.append(chunk)

    def _extract_chunk_keywords(self, text: str) -> List[str]:
        """提取文档块关键词"""
        keywords = set()

        # 寄存器名称 (大写字母开头，包含下划线和数字)
        register_pattern = r'\b([A-Z][A-Z0-9_]{2,})\b'
        registers = re.findall(register_pattern, text)
        keywords.update(registers)

        # 技术术语
        tech_terms = [
            'ADC', 'GPIO', 'UART', 'MCPWM', 'DMA', 'PWM', 'SPI', 'I2C', 'CAN',
            'CPU', 'MCU', 'FLASH', 'RAM', 'ROM', 'EEPROM', 'WDT', 'RTC',
            '时钟', '中断', '定时器', '计数器', '比较器', '电机', '传感器'
        ]
        for term in tech_terms:
            if term in text.upper() or term in text:
                keywords.add(term)

        # 数值和地址
        hex_pattern = r'0x[0-9A-Fa-f]+'
        hex_values = re.findall(hex_pattern, text)
        keywords.update(hex_values[:5])  # 最多5个地址

        # 频率和时间单位
        freq_pattern = r'\d+[kKmM]?[Hh][zZ]|\d+[μumns]?[sS]'
        freq_values = re.findall(freq_pattern, text)
        keywords.update(freq_values[:3])  # 最多3个频率

        return list(keywords)

    def _build_keyword_index(self):
        """构建关键词索引"""
        for chunk in self.document_chunks:
            for keyword in chunk['keywords']:
                keyword_lower = keyword.lower()
                if keyword_lower not in self.document_index:
                    self.document_index[keyword_lower] = []

                self.document_index[keyword_lower].append({
                    'chunk_id': chunk['id'],
                    'chapter': chunk['chapter'],
                    'relevance': 1.0  # 基础相关度
                })

    async def process_request(self, request: Dict[str, Any]) -> Dict[str, Any]:
        """处理文档理解请求"""
        message = request.get("message", "")
        context = request.get("context", {})

        # 分析查询类型
        query_type = self._analyze_query_type(message)

        # 检索相关文档块
        relevant_chunks = self._search_relevant_chunks(message, top_k=5)

        # 构建上下文
        context_text = self._build_context_from_chunks(relevant_chunks, message)

        # 生成回答
        response = await self._generate_contextual_response(message, context_text, query_type)

        # 更新对话上下文
        self.conversation_context.append({
            'query': message,
            'response': response,
            'chunks_used': len(relevant_chunks)
        })

        return await create_agent_response(
            response,
            {
                "query_type": query_type,
                "chunks_used": len(relevant_chunks),
                "chapters": list(set(chunk['chapter'] for chunk in relevant_chunks))
            }
        )

    def _analyze_query_type(self, message: str) -> str:
        """分析查询类型"""
        message_lower = message.lower()

        # 具体信息查找
        if any(keyword in message_lower for keyword in ["寄存器", "地址", "配置", "参数", "设置"]):
            return "specific_info"

        # 概念理解
        if any(keyword in message_lower for keyword in ["是什么", "原理", "工作方式", "如何工作"]):
            return "concept_explanation"

        # 操作指导
        if any(keyword in message_lower for keyword in ["如何", "怎么", "步骤", "方法"]):
            return "how_to"

        # 对比分析
        if any(keyword in message_lower for keyword in ["区别", "差异", "对比", "比较"]):
            return "comparison"

        # 总结概述
        if any(keyword in message_lower for keyword in ["总结", "概述", "整体", "全部"]):
            return "summary"

        return "general_query"

    def _search_relevant_chunks(self, query: str, top_k: int = 5) -> List[Dict[str, Any]]:
        """搜索相关文档块"""
        query_keywords = self._extract_query_keywords(query)
        chunk_scores = {}

        # 基于关键词匹配计算相关度
        for keyword in query_keywords:
            keyword_lower = keyword.lower()

            # 精确匹配
            if keyword_lower in self.document_index:
                for match in self.document_index[keyword_lower]:
                    chunk_id = match['chunk_id']
                    if chunk_id not in chunk_scores:
                        chunk_scores[chunk_id] = 0
                    chunk_scores[chunk_id] += 3.0  # 精确匹配高分

            # 模糊匹配
            for indexed_keyword, matches in self.document_index.items():
                if keyword_lower in indexed_keyword or indexed_keyword in keyword_lower:
                    for match in matches:
                        chunk_id = match['chunk_id']
                        if chunk_id not in chunk_scores:
                            chunk_scores[chunk_id] = 0
                        chunk_scores[chunk_id] += 1.0  # 模糊匹配低分

        # 文本相似度匹配
        for chunk in self.document_chunks:
            chunk_id = chunk['id']
            text_score = self._calculate_text_similarity(query, chunk['text'])

            if chunk_id not in chunk_scores:
                chunk_scores[chunk_id] = 0
            chunk_scores[chunk_id] += text_score

        # 排序并返回top_k
        sorted_chunks = sorted(chunk_scores.items(), key=lambda x: x[1], reverse=True)

        result_chunks = []
        for chunk_id, score in sorted_chunks[:top_k]:
            if score > 0:  # 只返回有相关性的块
                chunk = self.document_chunks[chunk_id].copy()
                chunk['relevance_score'] = score
                result_chunks.append(chunk)

        return result_chunks

    def _extract_query_keywords(self, query: str) -> List[str]:
        """提取查询关键词"""
        # 移除停用词
        stop_words = {'的', '是', '在', '有', '和', '与', '或', '但', '如何', '什么', '哪些', '怎么'}

        # 分词（简单按空格和标点分割）
        words = re.findall(r'\b\w+\b', query)
        keywords = [word for word in words if word not in stop_words and len(word) > 1]

        return keywords

    def _calculate_text_similarity(self, query: str, text: str) -> float:
        """计算文本相似度（简单实现）"""
        query_lower = query.lower()
        text_lower = text.lower()

        # 计算查询词在文本中的出现次数
        query_words = set(re.findall(r'\b\w+\b', query_lower))
        text_words = set(re.findall(r'\b\w+\b', text_lower))

        # 计算交集比例
        intersection = query_words.intersection(text_words)
        if not query_words:
            return 0.0

        similarity = len(intersection) / len(query_words)

        # 考虑文本长度，避免过短文本得分过高
        length_factor = min(1.0, len(text) / 200)

        return similarity * length_factor

    def _build_context_from_chunks(self, chunks: List[Dict[str, Any]], query: str) -> str:
        """从文档块构建上下文"""
        if not chunks:
            return "未找到相关文档内容。"

        context_parts = []
        total_length = 0

        for i, chunk in enumerate(chunks):
            if total_length >= self.max_context_length:
                break

            chunk_text = chunk['text']

            # 截取相关部分
            relevant_text = self._extract_relevant_text(chunk_text, query)

            if relevant_text:
                header = f"\n## 相关内容 {i+1} (来自: {chunk['chapter']})"
                context_parts.append(header)
                context_parts.append(relevant_text)

                total_length += len(header) + len(relevant_text)

        return '\n'.join(context_parts)

    def _extract_relevant_text(self, text: str, query: str) -> str:
        """从文本中提取与查询相关的部分"""
        query_keywords = self._extract_query_keywords(query)

        # 按段落分割
        paragraphs = text.split('\n\n')
        relevant_paragraphs = []

        for paragraph in paragraphs:
            paragraph = paragraph.strip()
            if not paragraph:
                continue

            # 检查段落是否包含查询关键词
            paragraph_lower = paragraph.lower()
            relevance_score = 0

            for keyword in query_keywords:
                if keyword.lower() in paragraph_lower:
                    relevance_score += 1

            if relevance_score > 0:
                relevant_paragraphs.append((paragraph, relevance_score))

        # 按相关度排序，取前几个段落
        relevant_paragraphs.sort(key=lambda x: x[1], reverse=True)

        result_text = []
        total_length = 0

        for paragraph, score in relevant_paragraphs[:3]:  # 最多3个段落
            if total_length + len(paragraph) <= 800:  # 控制长度
                result_text.append(paragraph)
                total_length += len(paragraph)
            else:
                # 截取部分内容
                remaining = 800 - total_length
                if remaining > 100:
                    result_text.append(paragraph[:remaining] + "...")
                break

        return '\n\n'.join(result_text) if result_text else text[:500] + "..."

    async def _generate_contextual_response(self, query: str, context: str, query_type: str) -> str:
        """生成基于上下文的回答"""
        # 构建增强的提示
        enhanced_prompt = f"""
基于以下LKS32MC08x用户手册的相关内容，回答用户问题。

相关文档内容：
{context}

用户问题：{query}
查询类型：{query_type}

请基于提供的文档内容给出准确、详细的回答。如果文档中没有足够信息，请明确说明。
回答要求：
1. 基于文档内容，不要编造信息
2. 如果是技术配置问题，提供具体的步骤或参数
3. 如果是概念解释，要清晰易懂
4. 标明信息来源的章节
"""

        try:
            # 调用模型生成回答
            response = await self.model_client.create(
                messages=[{"role": "user", "content": enhanced_prompt}]
            )

            if hasattr(response, 'choices') and response.choices:
                model_response = response.choices[0].message.content
                print(f"🤖 模型成功理解文档并生成回答 (长度: {len(model_response)} 字符)")
                return model_response
            else:
                print("⚠️  模型响应格式异常，使用备用回答")
                return self._generate_fallback_response(query, context)

        except Exception as e:
            print(f"❌ 模型调用失败: {e}")
            print("🔄 使用基于检索的备用回答")
            return self._generate_fallback_response(query, context)

    def _generate_fallback_response(self, query: str, context: str) -> str:
        """生成备用回答"""
        if not context or context == "未找到相关文档内容。":
            return f"抱歉，在文档中未找到关于 '{query}' 的相关信息。请尝试使用更具体的关键词。"

        # 简单的基于模板的回答
        response = f"基于LKS32MC08x用户手册，找到以下相关信息：\n\n{context}\n\n"
        response += "以上信息来自用户手册的相关章节。如需更详细的信息，请参考完整的技术文档。"

        return response

    def get_reading_stats(self) -> Dict[str, Any]:
        """获取阅读统计"""
        return {
            "total_chunks": len(self.document_chunks),
            "total_chapters": len(self.chapter_index),
            "indexed_keywords": len(self.document_index),
            "conversation_turns": len(self.conversation_context),
            "avg_chunk_size": sum(chunk['size'] for chunk in self.document_chunks) / len(self.document_chunks) if self.document_chunks else 0
        }
