"""
增强型知识图谱构建智能体
使用向量数据库 + 高效实体提取 + 图数据库存储
"""
import os
import json
import re
import time
from typing import Dict, Any, List, Tuple, Set
import asyncio
from concurrent.futures import ThreadPoolExecutor
import networkx as nx
from pyvis.network import Network
import matplotlib.pyplot as plt

# 向量数据库和NLP
import chromadb
from sentence_transformers import SentenceTransformer
import spacy

from .base_agent import BaseAgent, create_agent_response

class EnhancedKGBuilder(BaseAgent):
    """增强型知识图谱构建智能体"""

    def __init__(self, model_client, system_message: str, capabilities: Dict[str, Any]):
        super().__init__(
            name="EnhancedKGBuilder",
            model_client=model_client,
            system_message=system_message,
            capabilities=capabilities
        )

        # 初始化组件
        self.knowledge_graph = nx.DiGraph()
        self.vector_db = None
        self.embedding_model = None
        self.nlp = None
        self.executor = ThreadPoolExecutor(max_workers=4)

        # 配置
        self.config = {
            'batch_size': 50,  # 批处理大小
            'embedding_model': 'all-MiniLM-L6-v2',  # 轻量级嵌入模型
            'similarity_threshold': 0.7,  # 相似度阈值
            'max_entities_per_text': 20,  # 每段文本最大实体数
            'use_gpu': False  # 是否使用GPU
        }

        # 实体模式库
        self.entity_patterns = {
            'register': [
                r'\b([A-Z][A-Z0-9_]{2,})\s*寄存器',
                r'\b([A-Z][A-Z0-9_]{2,})\s*REG\b',
                r'\b([A-Z][A-Z0-9_]{2,})\s*Register\b'
            ],
            'address': [
                r'\b(0x[0-9A-Fa-f]{4,})\b',
                r'\b([0-9A-Fa-f]{4,}H)\b'
            ],
            'module': [
                r'\b(ADC|GPIO|UART|MCPWM|DMA|PWM|SPI|I2C|CAN|FLASH|RAM|RTC|WDT)\b',
                r'\b(Timer|Counter|Comparator|Oscillator)\b'
            ],
            'bit_field': [
                r'\bbit\s*\[(\d+:\d+)\]',
                r'\bbits?\s*(\d+)\s*[-~]\s*(\d+)',
                r'\b([A-Z][A-Z0-9_]*)\s*\[(\d+:\d+)\]'
            ],
            'parameter': [
                r'(\d+)\s*(MHz|KHz|Hz|V|mV|mA|μA|Ω|kΩ|MΩ)',
                r'(波特率|频率|电压|电流|阻抗|分辨率|精度)',
                r'(\d+)\s*(bit|位|通道|路)'
            ]
        }

        # 关系模式库
        self.relation_patterns = [
            (r'(.+?)\s*控制\s*(.+)', 'controls'),
            (r'(.+?)\s*配置\s*(.+)', 'configures'),
            (r'(.+?)\s*包含\s*(.+)', 'contains'),
            (r'(.+?)\s*位于\s*(.+)', 'located_at'),
            (r'(.+?)\s*连接\s*(.+)', 'connects_to'),
            (r'(.+?)\s*依赖\s*(.+)', 'depends_on')
        ]

        # 输出目录
        self.output_dir = "enhanced_knowledge_graphs"
        os.makedirs(self.output_dir, exist_ok=True)

    async def initialize_components(self):
        """异步初始化组件"""
        if self.vector_db is None:
            print("🔧 初始化向量数据库...")
            self.vector_db = chromadb.Client()

            # 创建或获取集合
            try:
                self.collection = self.vector_db.get_collection("kg_entities")
            except:
                self.collection = self.vector_db.create_collection("kg_entities")

            print("✅ 向量数据库初始化完成")

        if self.embedding_model is None:
            print("🔧 加载嵌入模型...")
            self.embedding_model = SentenceTransformer(self.config['embedding_model'])
            print("✅ 嵌入模型加载完成")

        if self.nlp is None:
            print("🔧 初始化NLP模型...")
            try:
                # 尝试加载中文模型
                self.nlp = spacy.load("zh_core_web_sm")
            except OSError:
                try:
                    # 回退到英文模型
                    self.nlp = spacy.load("en_core_web_sm")
                except OSError:
                    # 使用空白模型
                    self.nlp = spacy.blank("zh")
                    print("⚠️  使用空白NLP模型")
            print("✅ NLP模型初始化完成")

    async def process_request(self, request: Dict[str, Any]) -> Dict[str, Any]:
        """处理增强知识图谱构建请求"""
        message = request.get("message", "")

        # 初始化组件
        await self.initialize_components()

        if "构建知识图谱" in message or "build knowledge graph" in message.lower():
            mineru_path = self._extract_mineru_path(message)
            if not mineru_path:
                mineru_path = "/Users/<USER>/Public/AutoGen/monkey_ocr_project/mineru_output"

            result = await self._build_enhanced_knowledge_graph(mineru_path)
            return await create_agent_response(result)

        elif "分析知识图谱" in message:
            result = await self._analyze_enhanced_graph()
            return await create_agent_response(result)

        elif "查询实体" in message:
            query = self._extract_query(message)
            result = await self._query_entities(query)
            return await create_agent_response(result)

        elif "导出知识图谱" in message:
            export_format = self._extract_export_format(message)
            result = await self._export_enhanced_graph(export_format)
            return await create_agent_response(result)

        else:
            return await create_agent_response(
                "请指定操作类型：\n"
                "- 构建知识图谱 [路径]\n"
                "- 分析知识图谱\n"
                "- 查询实体 <查询内容>\n"
                "- 导出知识图谱 [格式]"
            )

    def _extract_mineru_path(self, message: str) -> str:
        """提取Mineru输出路径"""
        import re
        patterns = [
            r'路径[：:]\s*([^\s]+)',
            r'从\s+([^\s]+)\s+构建',
            r'输出目录[：:]\s*([^\s]+)'
        ]

        for pattern in patterns:
            match = re.search(pattern, message)
            if match:
                return match.group(1).strip()
        return None

    def _extract_query(self, message: str) -> str:
        """提取查询内容"""
        # 移除命令部分
        query = re.sub(r'查询实体\s*', '', message).strip()
        return query if query else "所有实体"

    def _extract_export_format(self, message: str) -> str:
        """提取导出格式"""
        formats = ['html', 'json', 'gexf', 'graphml', 'png']
        for fmt in formats:
            if fmt in message.lower():
                return fmt
        return 'html'

    async def _build_enhanced_knowledge_graph(self, mineru_path: str) -> str:
        """构建增强知识图谱"""
        print(f"🚀 开始构建增强知识图谱: {mineru_path}")
        start_time = time.time()

        if not os.path.exists(mineru_path):
            return f"❌ Mineru输出路径不存在: {mineru_path}"

        # 清空现有数据
        self.knowledge_graph.clear()

        # 扫描并批量处理文件
        content_files = []
        for root, dirs, files in os.walk(mineru_path):
            for file in files:
                if file.endswith('_content_list.json'):
                    file_path = os.path.join(root, file)
                    content_files.append(file_path)

        if not content_files:
            return f"❌ 未找到content_list.json文件: {mineru_path}"

        print(f"📄 找到 {len(content_files)} 个文件")

        # 批量处理文件
        total_entities = 0
        total_relations = 0

        for i, file_path in enumerate(content_files, 1):
            print(f"📊 处理文件 {i}/{len(content_files)}: {os.path.basename(file_path)}")

            try:
                entities, relations = await self._process_file_enhanced(file_path)
                total_entities += entities
                total_relations += relations
                print(f"  ✅ 提取实体: {entities}, 关系: {relations}")
            except Exception as e:
                print(f"  ❌ 处理失败: {e}")

        # 构建图谱统计
        processing_time = time.time() - start_time
        graph_stats = self._calculate_enhanced_stats()

        # 生成报告
        report = f"""# 🚀 增强知识图谱构建完成

## ⚡ 性能统计
- **处理时间**: {processing_time:.2f}秒
- **处理文件**: {len(content_files)}个
- **图谱节点**: {self.knowledge_graph.number_of_nodes()}
- **图谱边**: {self.knowledge_graph.number_of_edges()}
- **处理速度**: {self.knowledge_graph.number_of_nodes() / processing_time:.1f} 节点/秒

## 📊 图谱质量
{graph_stats}

## 🔍 技术特性
- ✅ 向量数据库索引
- ✅ 批量并行处理
- ✅ 高精度实体提取
- ✅ 智能关系推理
- ✅ 相似度去重

## 💾 输出文件
增强知识图谱已保存到: `{self.output_dir}/`
"""

        # 自动导出
        await self._export_enhanced_graph('html')
        await self._export_enhanced_graph('json')

        return report

    async def _process_file_enhanced(self, file_path: str) -> Tuple[int, int]:
        """增强文件处理"""
        with open(file_path, 'r', encoding='utf-8') as f:
            content_list = json.load(f)

        # 批量处理文本
        text_batches = []
        current_batch = []

        for item in content_list:
            if item.get('type') == 'text':
                text = item.get('text', '').strip()
                if text and len(text) > 10:  # 过滤太短的文本
                    current_batch.append({
                        'text': text,
                        'page': item.get('page_idx', 0),
                        'level': item.get('text_level', 0)
                    })

                    if len(current_batch) >= self.config['batch_size']:
                        text_batches.append(current_batch)
                        current_batch = []

        if current_batch:
            text_batches.append(current_batch)

        # 并行处理批次
        total_entities = 0
        total_relations = 0

        for batch in text_batches:
            entities, relations = await self._process_text_batch(batch)
            total_entities += entities
            total_relations += relations

        return total_entities, total_relations

    async def _process_text_batch(self, text_batch: List[Dict]) -> Tuple[int, int]:
        """批量处理文本"""
        entities_count = 0
        relations_count = 0

        # 并行提取实体
        tasks = []
        for text_item in text_batch:
            task = self._extract_entities_fast(text_item['text'], text_item['page'])
            tasks.append(task)

        # 等待所有任务完成
        results = await asyncio.gather(*tasks, return_exceptions=True)

        # 处理结果
        for i, result in enumerate(results):
            if isinstance(result, Exception):
                print(f"⚠️  文本处理失败: {result}")
                continue

            entities, relations = result

            # 添加实体到图谱
            for entity in entities:
                entity_id = self._normalize_entity_id(entity['name'], entity['type'])
                self._add_entity_enhanced(entity_id, entity)
                entities_count += 1

            # 添加关系到图谱
            for relation in relations:
                source_id = self._normalize_entity_id(relation['source'], relation.get('source_type', 'entity'))
                target_id = self._normalize_entity_id(relation['target'], relation.get('target_type', 'entity'))

                if source_id in self.knowledge_graph.nodes and target_id in self.knowledge_graph.nodes:
                    self._add_relation_enhanced(source_id, target_id, relation)
                    relations_count += 1

        return entities_count, relations_count

    async def _extract_entities_fast(self, text: str, page: int) -> Tuple[List[Dict], List[Dict]]:
        """快速实体提取"""
        entities = []
        relations = []

        # 使用正则表达式快速提取
        for entity_type, patterns in self.entity_patterns.items():
            for pattern in patterns:
                matches = re.finditer(pattern, text, re.IGNORECASE)
                for match in matches:
                    entity_name = match.group(1) if match.groups() else match.group(0)
                    entities.append({
                        'name': entity_name.strip(),
                        'type': entity_type,
                        'context': text[max(0, match.start()-50):match.end()+50],
                        'page': page,
                        'confidence': 0.9  # 正则匹配高置信度
                    })

        # 使用NLP提取命名实体
        if self.nlp and len(text) < 1000:  # 限制长度避免超时
            try:
                doc = self.nlp(text)
                for ent in doc.ents:
                    if len(ent.text) > 2:  # 过滤太短的实体
                        entities.append({
                            'name': ent.text,
                            'type': self._map_spacy_label(ent.label_),
                            'context': text[max(0, ent.start_char-30):ent.end_char+30],
                            'page': page,
                            'confidence': 0.7  # NLP提取中等置信度
                        })
            except Exception as e:
                print(f"⚠️  NLP提取失败: {e}")

        # 提取关系
        for pattern, relation_type in self.relation_patterns:
            matches = re.finditer(pattern, text, re.IGNORECASE)
            for match in matches:
                if len(match.groups()) >= 2:
                    source = match.group(1).strip()
                    target = match.group(2).strip()
                    if source and target and source != target:
                        relations.append({
                            'source': source,
                            'target': target,
                            'type': relation_type,
                            'context': match.group(0),
                            'page': page,
                            'confidence': 0.8
                        })

        # 去重和过滤
        entities = self._deduplicate_entities(entities)
        relations = self._deduplicate_relations(relations)

        return entities[:self.config['max_entities_per_text']], relations

    def _normalize_entity_id(self, name: str, entity_type: str) -> str:
        """标准化实体ID"""
        # 清理名称
        clean_name = re.sub(r'[^\w\u4e00-\u9fff]', '_', name.lower())
        clean_name = re.sub(r'_+', '_', clean_name).strip('_')
        return f"{entity_type}_{clean_name}"

    def _add_entity_enhanced(self, entity_id: str, entity: Dict):
        """增强实体添加"""
        if entity_id not in self.knowledge_graph.nodes:
            # 添加到图谱
            self.knowledge_graph.add_node(entity_id, **entity)

            # 添加到向量数据库
            try:
                embedding = self.embedding_model.encode([entity['context']])[0]
                self.collection.add(
                    embeddings=[embedding.tolist()],
                    documents=[entity['context']],
                    metadatas=[{
                        'entity_id': entity_id,
                        'name': entity['name'],
                        'type': entity['type'],
                        'page': entity['page']
                    }],
                    ids=[entity_id]
                )
            except Exception as e:
                print(f"⚠️  向量存储失败: {e}")
        else:
            # 更新现有实体
            existing = self.knowledge_graph.nodes[entity_id]
            if entity.get('confidence', 0) > existing.get('confidence', 0):
                self.knowledge_graph.nodes[entity_id].update(entity)

    def _add_relation_enhanced(self, source_id: str, target_id: str, relation: Dict):
        """增强关系添加"""
        if not self.knowledge_graph.has_edge(source_id, target_id):
            self.knowledge_graph.add_edge(source_id, target_id, **relation)
        else:
            # 更新现有关系
            existing = self.knowledge_graph.edges[source_id, target_id]
            if relation.get('confidence', 0) > existing.get('confidence', 0):
                self.knowledge_graph.edges[source_id, target_id].update(relation)

    def _deduplicate_entities(self, entities: List[Dict]) -> List[Dict]:
        """实体去重"""
        seen = set()
        unique_entities = []

        for entity in entities:
            # 使用名称和类型作为去重键
            key = (entity['name'].lower(), entity['type'])
            if key not in seen:
                seen.add(key)
                unique_entities.append(entity)

        return unique_entities

    def _deduplicate_relations(self, relations: List[Dict]) -> List[Dict]:
        """关系去重"""
        seen = set()
        unique_relations = []

        for relation in relations:
            key = (relation['source'].lower(), relation['target'].lower(), relation['type'])
            if key not in seen:
                seen.add(key)
                unique_relations.append(relation)

        return unique_relations

    def _map_spacy_label(self, label: str) -> str:
        """映射spaCy标签到我们的实体类型"""
        mapping = {
            'PERSON': 'person',
            'ORG': 'organization',
            'GPE': 'location',
            'PRODUCT': 'product',
            'EVENT': 'event',
            'WORK_OF_ART': 'concept',
            'LAW': 'concept',
            'LANGUAGE': 'concept',
            'DATE': 'date',
            'TIME': 'time',
            'PERCENT': 'parameter',
            'MONEY': 'parameter',
            'QUANTITY': 'parameter',
            'ORDINAL': 'parameter',
            'CARDINAL': 'parameter'
        }
        return mapping.get(label, 'concept')

    async def _analyze_enhanced_graph(self) -> str:
        """分析增强知识图谱"""
        if self.knowledge_graph.number_of_nodes() == 0:
            return "❌ 知识图谱为空，请先构建图谱"

        # 基本统计
        num_nodes = self.knowledge_graph.number_of_nodes()
        num_edges = self.knowledge_graph.number_of_edges()
        density = nx.density(self.knowledge_graph)

        # 实体类型分布
        entity_types = {}
        confidence_stats = []

        for node, data in self.knowledge_graph.nodes(data=True):
            entity_type = data.get('type', 'unknown')
            entity_types[entity_type] = entity_types.get(entity_type, 0) + 1
            confidence_stats.append(data.get('confidence', 0))

        avg_confidence = sum(confidence_stats) / len(confidence_stats) if confidence_stats else 0

        # 关系类型分布
        relation_types = {}
        for source, target, data in self.knowledge_graph.edges(data=True):
            relation_type = data.get('type', 'unknown')
            relation_types[relation_type] = relation_types.get(relation_type, 0) + 1

        # 中心性分析
        try:
            degree_centrality = nx.degree_centrality(self.knowledge_graph)
            top_nodes = sorted(degree_centrality.items(), key=lambda x: x[1], reverse=True)[:5]

            centrality_analysis = "### 🎯 中心性分析\n"
            for node_id, centrality in top_nodes:
                node_data = self.knowledge_graph.nodes[node_id]
                centrality_analysis += f"- **{node_data.get('name', node_id)}** ({node_data.get('type', 'unknown')}): {centrality:.3f}\n"
        except Exception as e:
            centrality_analysis = f"中心性分析失败: {e}"

        # 连通性分析
        try:
            undirected = self.knowledge_graph.to_undirected()
            num_components = nx.number_connected_components(undirected)
            largest_component = max(nx.connected_components(undirected), key=len)
            largest_size = len(largest_component)
        except Exception as e:
            num_components = "计算失败"
            largest_size = "计算失败"

        analysis = f"""# 🔍 增强知识图谱分析报告

## 📊 基本统计
- **节点数**: {num_nodes:,}
- **边数**: {num_edges:,}
- **图谱密度**: {density:.4f}
- **平均置信度**: {avg_confidence:.3f}

## 🏷️ 实体类型分布
{chr(10).join(f'- **{entity_type}**: {count}个' for entity_type, count in sorted(entity_types.items(), key=lambda x: x[1], reverse=True))}

## 🔗 关系类型分布
{chr(10).join(f'- **{relation_type}**: {count}个' for relation_type, count in sorted(relation_types.items(), key=lambda x: x[1], reverse=True))}

{centrality_analysis}

## 🌐 连通性分析
- **连通分量数**: {num_components}
- **最大连通分量**: {largest_size}个节点
- **连通率**: {largest_size/num_nodes*100:.1f}%

## ⚡ 性能指标
- **向量数据库**: {self.collection.count() if self.collection else 0}个向量
- **查询就绪**: {'✅' if self.embedding_model else '❌'}
- **NLP支持**: {'✅' if self.nlp else '❌'}
"""

        return analysis

    async def _query_entities(self, query: str) -> str:
        """查询实体"""
        if not self.embedding_model or not self.collection:
            return "❌ 向量查询未初始化"

        try:
            # 生成查询向量
            query_embedding = self.embedding_model.encode([query])[0]

            # 向量相似度搜索
            results = self.collection.query(
                query_embeddings=[query_embedding.tolist()],
                n_results=10
            )

            if not results['ids'][0]:
                return f"❌ 未找到与'{query}'相关的实体"

            # 格式化结果
            response = f"# 🔍 实体查询结果: '{query}'\n\n"

            for i, (entity_id, distance, metadata) in enumerate(zip(
                results['ids'][0],
                results['distances'][0],
                results['metadatas'][0]
            ), 1):
                similarity = 1 - distance  # 转换为相似度

                response += f"## {i}. {metadata['name']}\n"
                response += f"- **类型**: {metadata['type']}\n"
                response += f"- **相似度**: {similarity:.3f}\n"
                response += f"- **页面**: {metadata['page']}\n"

                # 获取图谱中的详细信息
                if entity_id in self.knowledge_graph.nodes:
                    node_data = self.knowledge_graph.nodes[entity_id]
                    response += f"- **置信度**: {node_data.get('confidence', 0):.3f}\n"

                    # 显示相关关系
                    neighbors = list(self.knowledge_graph.neighbors(entity_id))
                    if neighbors:
                        neighbor_names = []
                        for neighbor in neighbors[:3]:  # 只显示前3个
                            neighbor_data = self.knowledge_graph.nodes[neighbor]
                            neighbor_names.append(neighbor_data.get('name', neighbor))
                        response += f"- **相关实体**: {', '.join(neighbor_names)}\n"

                response += "\n"

            return response

        except Exception as e:
            return f"❌ 查询失败: {e}"

    def _calculate_enhanced_stats(self) -> str:
        """计算增强统计信息"""
        if self.knowledge_graph.number_of_nodes() == 0:
            return "图谱为空"

        # 质量指标
        total_confidence = 0
        confidence_count = 0

        for node, data in self.knowledge_graph.nodes(data=True):
            if 'confidence' in data:
                total_confidence += data['confidence']
                confidence_count += 1

        avg_confidence = total_confidence / confidence_count if confidence_count > 0 else 0

        # 覆盖度分析
        pages_covered = set()
        for node, data in self.knowledge_graph.nodes(data=True):
            if 'page' in data:
                pages_covered.add(data['page'])

        stats = f"""
### 📈 质量指标
- **平均置信度**: {avg_confidence:.3f}
- **页面覆盖**: {len(pages_covered)}页
- **实体密度**: {self.knowledge_graph.number_of_nodes() / len(pages_covered) if pages_covered else 0:.1f} 实体/页

### 🎯 提取效率
- **向量索引**: {self.collection.count() if self.collection else 0}个
- **图谱连通性**: {nx.is_connected(self.knowledge_graph.to_undirected()) if self.knowledge_graph.number_of_nodes() > 0 else False}
"""
        return stats

    async def _export_enhanced_graph(self, format_type: str = 'html') -> str:
        """导出增强知识图谱"""
        if self.knowledge_graph.number_of_nodes() == 0:
            return "❌ 知识图谱为空，无法导出"

        try:
            if format_type == 'html':
                return await self._export_to_enhanced_html()
            elif format_type == 'json':
                return await self._export_to_enhanced_json()
            elif format_type == 'png':
                return await self._export_to_enhanced_png()
            else:
                return f"❌ 不支持的导出格式: {format_type}"
        except Exception as e:
            return f"❌ 导出失败: {e}"

    async def _export_to_enhanced_html(self) -> str:
        """导出增强HTML"""
        net = Network(height="900px", width="100%", bgcolor="#1a1a1a", font_color="white")

        # 设置物理引擎
        net.set_options("""
        var options = {
          "physics": {
            "enabled": true,
            "stabilization": {"iterations": 200},
            "barnesHut": {
              "gravitationalConstant": -8000,
              "centralGravity": 0.3,
              "springLength": 95,
              "springConstant": 0.04,
              "damping": 0.09
            }
          },
          "nodes": {
            "font": {"size": 12, "color": "white"},
            "borderWidth": 2,
            "shadow": true
          },
          "edges": {
            "font": {"size": 10, "color": "white"},
            "shadow": true,
            "smooth": {"type": "continuous"}
          }
        }
        """)

        # 颜色映射
        color_map = {
            'register': '#ff6b6b',
            'module': '#4ecdc4',
            'function': '#45b7d1',
            'parameter': '#96ceb4',
            'address': '#feca57',
            'bit_field': '#ff9ff3',
            'chapter': '#54a0ff',
            'section': '#5f27cd',
            'concept': '#00d2d3',
            'person': '#fd79a8',
            'organization': '#fdcb6e',
            'location': '#6c5ce7',
            'product': '#a29bfe',
            'event': '#fd79a8'
        }

        # 添加节点
        for node, data in self.knowledge_graph.nodes(data=True):
            entity_type = data.get('type', 'concept')
            color = color_map.get(entity_type, '#cccccc')
            confidence = data.get('confidence', 0)

            # 节点大小基于度数和置信度
            degree = self.knowledge_graph.degree(node)
            size = min(50, 15 + degree * 2 + confidence * 10)

            title = f"""
            名称: {data.get('name', node)}
            类型: {entity_type}
            置信度: {confidence:.3f}
            页面: {data.get('page', 'N/A')}
            度数: {degree}
            """

            net.add_node(node,
                        label=data.get('name', node)[:20],
                        color=color,
                        title=title.strip(),
                        size=size)

        # 添加边
        for source, target, data in self.knowledge_graph.edges(data=True):
            relation_type = data.get('type', 'related_to')
            confidence = data.get('confidence', 0)

            # 边宽度基于置信度
            width = max(1, confidence * 3)

            net.add_edge(source, target,
                        label=relation_type,
                        title=f"关系: {relation_type}\n置信度: {confidence:.3f}",
                        width=width)

        output_path = os.path.join(self.output_dir, "enhanced_knowledge_graph.html")
        net.save_graph(output_path)

        return f"✅ 增强HTML图谱已导出: {output_path}"

    async def _export_to_enhanced_json(self) -> str:
        """导出增强JSON"""
        graph_data = {
            'metadata': {
                'nodes': self.knowledge_graph.number_of_nodes(),
                'edges': self.knowledge_graph.number_of_edges(),
                'density': nx.density(self.knowledge_graph),
                'export_time': time.time()
            },
            'nodes': [],
            'edges': []
        }

        # 导出节点
        for node, data in self.knowledge_graph.nodes(data=True):
            node_data = {
                'id': node,
                'name': data.get('name', node),
                'type': data.get('type', 'unknown'),
                'confidence': data.get('confidence', 0),
                'page': data.get('page', 0),
                'context': data.get('context', ''),
                'degree': self.knowledge_graph.degree(node)
            }
            graph_data['nodes'].append(node_data)

        # 导出边
        for source, target, data in self.knowledge_graph.edges(data=True):
            edge_data = {
                'source': source,
                'target': target,
                'type': data.get('type', 'related_to'),
                'confidence': data.get('confidence', 0),
                'context': data.get('context', ''),
                'page': data.get('page', 0)
            }
            graph_data['edges'].append(edge_data)

        output_path = os.path.join(self.output_dir, "enhanced_knowledge_graph.json")
        with open(output_path, 'w', encoding='utf-8') as f:
            json.dump(graph_data, f, ensure_ascii=False, indent=2)

        return f"✅ 增强JSON格式图谱已导出: {output_path}"

    async def _export_to_enhanced_png(self) -> str:
        """导出增强PNG"""
        plt.figure(figsize=(24, 18))

        # 使用force-directed布局
        pos = nx.spring_layout(self.knowledge_graph, k=3, iterations=100)

        # 按类型分组绘制节点
        entity_types = {}
        for node, data in self.knowledge_graph.nodes(data=True):
            entity_type = data.get('type', 'concept')
            if entity_type not in entity_types:
                entity_types[entity_type] = []
            entity_types[entity_type].append(node)

        # 颜色映射
        colors = ['red', 'blue', 'green', 'orange', 'purple', 'brown', 'pink', 'gray', 'olive', 'cyan']

        for i, (entity_type, nodes) in enumerate(entity_types.items()):
            color = colors[i % len(colors)]

            # 节点大小基于度数
            node_sizes = [min(1000, 100 + self.knowledge_graph.degree(node) * 50) for node in nodes]

            nx.draw_networkx_nodes(self.knowledge_graph, pos,
                                  nodelist=nodes,
                                  node_color=color,
                                  node_size=node_sizes,
                                  alpha=0.8,
                                  label=entity_type)

        # 绘制边
        nx.draw_networkx_edges(self.knowledge_graph, pos,
                              alpha=0.3,
                              edge_color='gray',
                              width=0.5)

        # 绘制标签（只显示重要节点）
        important_nodes = {}
        for node, data in self.knowledge_graph.nodes(data=True):
            if self.knowledge_graph.degree(node) > 2:  # 度数大于2的重要节点
                important_nodes[node] = data.get('name', node)[:15]

        nx.draw_networkx_labels(self.knowledge_graph, pos,
                               labels=important_nodes,
                               font_size=8,
                               font_color='black')

        plt.title("增强知识图谱", fontsize=20, pad=20)
        plt.legend(bbox_to_anchor=(1.05, 1), loc='upper left')
        plt.axis('off')

        output_path = os.path.join(self.output_dir, "enhanced_knowledge_graph.png")
        plt.savefig(output_path, dpi=300, bbox_inches='tight',
                   facecolor='white', edgecolor='none')
        plt.close()

        return f"✅ 增强PNG图片已导出: {output_path}"