"""
技术专家智能体
负责深度技术分析和问题解决
"""
import re
from typing import Dict, Any, List, Optional
from .base_agent import BaseAgent, create_agent_response

class TechnicalExpert(BaseAgent):
    """技术专家智能体"""
    
    def __init__(self, model_client, system_message: str, capabilities: Dict[str, Any]):
        super().__init__(
            name="TechnicalExpert",
            model_client=model_client,
            system_message=system_message,
            capabilities=capabilities
        )
        
        # 技术知识库
        self.technical_knowledge = {
            "adc": {
                "description": "模数转换器，用于将模拟信号转换为数字信号",
                "key_features": ["多通道采样", "可配置增益", "DMA支持", "中断触发"],
                "common_issues": ["基准电压选择", "采样时序", "噪声干扰"],
                "optimization_tips": ["合理设置采样频率", "使用硬件滤波", "优化PCB布局"]
            },
            "mcpwm": {
                "description": "电机控制PWM模块，专用于电机驱动控制",
                "key_features": ["中心对齐模式", "边沿对齐模式", "死区控制", "故障保护"],
                "common_issues": ["死区时间设置", "互补输出配置", "故障检测"],
                "optimization_tips": ["合理设置载波频率", "优化死区时间", "使用硬件保护"]
            },
            "gpio": {
                "description": "通用输入输出端口，提供数字信号接口",
                "key_features": ["可配置方向", "上拉下拉", "中断支持", "复用功能"],
                "common_issues": ["引脚复用冲突", "驱动能力不足", "中断配置"],
                "optimization_tips": ["合理分配引脚", "使用适当的驱动强度", "避免冲突"]
            },
            "uart": {
                "description": "通用异步收发器，用于串行通信",
                "key_features": ["可配置波特率", "DMA支持", "中断驱动", "流控制"],
                "common_issues": ["波特率误差", "数据丢失", "时序问题"],
                "optimization_tips": ["使用精确时钟源", "启用FIFO", "合理设置超时"]
            },
            "dma": {
                "description": "直接内存访问控制器，提供高效数据传输",
                "key_features": ["多通道支持", "优先级控制", "循环模式", "中断通知"],
                "common_issues": ["通道冲突", "传输错误", "同步问题"],
                "optimization_tips": ["合理分配优先级", "使用双缓冲", "避免总线冲突"]
            }
        }
        
        # 性能参数
        self.performance_specs = {
            "cpu_frequency": "48MHz",
            "flash_size": "64KB",
            "ram_size": "8KB",
            "adc_resolution": "12-bit",
            "adc_channels": "20",
            "pwm_resolution": "16-bit",
            "uart_baudrate_max": "1Mbps"
        }
    
    async def process_request(self, request: Dict[str, Any]) -> Dict[str, Any]:
        """处理技术分析请求"""
        message = request.get("message", "")
        context = request.get("context", {})
        
        # 分析技术问题类型
        analysis_type = self._analyze_technical_type(message)
        
        if analysis_type == "performance_analysis":
            return await self._handle_performance_analysis(message, context)
        elif analysis_type == "troubleshooting":
            return await self._handle_troubleshooting(message, context)
        elif analysis_type == "optimization":
            return await self._handle_optimization(message, context)
        elif analysis_type == "design_guidance":
            return await self._handle_design_guidance(message, context)
        else:
            return await self._handle_general_technical_query(message, context)
    
    def _analyze_technical_type(self, message: str) -> str:
        """分析技术问题类型"""
        message_lower = message.lower()
        
        # 性能分析
        if any(keyword in message_lower for keyword in ["性能", "速度", "频率", "延迟", "吞吐量"]):
            return "performance_analysis"
        
        # 故障排除
        if any(keyword in message_lower for keyword in ["问题", "错误", "故障", "不工作", "异常"]):
            return "troubleshooting"
        
        # 优化建议
        if any(keyword in message_lower for keyword in ["优化", "改进", "提升", "降低功耗"]):
            return "optimization"
        
        # 设计指导
        if any(keyword in message_lower for keyword in ["设计", "选择", "配置", "如何", "怎么"]):
            return "design_guidance"
        
        return "general_technical"
    
    async def _handle_performance_analysis(self, message: str, context: Dict) -> Dict[str, Any]:
        """处理性能分析"""
        # 识别相关模块
        modules = self._identify_modules(message)
        
        analysis_result = "## 🚀 性能分析报告\n\n"
        
        if modules:
            for module in modules:
                if module in self.technical_knowledge:
                    knowledge = self.technical_knowledge[module]
                    analysis_result += f"### {module.upper()} 模块性能分析\n"
                    analysis_result += f"**功能描述**: {knowledge['description']}\n\n"
                    analysis_result += f"**关键特性**: {', '.join(knowledge['key_features'])}\n\n"
                    
                    # 添加性能相关的具体分析
                    if module == "adc":
                        analysis_result += self._analyze_adc_performance()
                    elif module == "mcpwm":
                        analysis_result += self._analyze_mcpwm_performance()
                    elif module == "uart":
                        analysis_result += self._analyze_uart_performance()
                    
                    analysis_result += "\n"
        else:
            analysis_result += "### 系统整体性能\n"
            analysis_result += f"- **CPU频率**: {self.performance_specs['cpu_frequency']}\n"
            analysis_result += f"- **Flash容量**: {self.performance_specs['flash_size']}\n"
            analysis_result += f"- **RAM容量**: {self.performance_specs['ram_size']}\n\n"
            analysis_result += "**性能优化建议**:\n"
            analysis_result += "1. 合理使用DMA减少CPU负载\n"
            analysis_result += "2. 优化中断处理程序\n"
            analysis_result += "3. 使用适当的时钟配置\n"
        
        return await create_agent_response(analysis_result, {"modules": modules})
    
    def _analyze_adc_performance(self) -> str:
        """分析ADC性能"""
        return """**ADC性能指标**:
- 分辨率: 12位
- 采样率: 最高1MSPS
- 通道数: 20个
- 转换时间: 1μs (典型值)

**性能优化要点**:
1. 选择合适的采样频率避免混叠
2. 使用DMA减少CPU干预
3. 合理配置基准电压源
4. 注意模拟地和数字地的分离

"""
    
    def _analyze_mcpwm_performance(self) -> str:
        """分析MCPWM性能"""
        return """**MCPWM性能指标**:
- PWM分辨率: 16位
- 最高频率: 100kHz
- 死区精度: 10ns
- 通道数: 6个互补输出

**性能优化要点**:
1. 合理设置载波频率平衡效率和EMI
2. 优化死区时间防止直通
3. 使用硬件故障保护
4. 考虑开关损耗和导通损耗

"""
    
    def _analyze_uart_performance(self) -> str:
        """分析UART性能"""
        return """**UART性能指标**:
- 最高波特率: 1Mbps
- FIFO深度: 16字节
- 支持DMA传输
- 硬件流控制

**性能优化要点**:
1. 使用精确的时钟源减少波特率误差
2. 启用FIFO提高传输效率
3. 使用DMA进行大数据量传输
4. 合理设置超时和中断

"""
    
    async def _handle_troubleshooting(self, message: str, context: Dict) -> Dict[str, Any]:
        """处理故障排除"""
        modules = self._identify_modules(message)
        
        troubleshooting_guide = "## 🔧 故障排除指南\n\n"
        
        if modules:
            for module in modules:
                if module in self.technical_knowledge:
                    knowledge = self.technical_knowledge[module]
                    troubleshooting_guide += f"### {module.upper()} 模块常见问题\n"
                    
                    for i, issue in enumerate(knowledge['common_issues'], 1):
                        troubleshooting_guide += f"{i}. **{issue}**\n"
                        troubleshooting_guide += f"   - 检查相关寄存器配置\n"
                        troubleshooting_guide += f"   - 验证硬件连接\n"
                        troubleshooting_guide += f"   - 查看时序要求\n\n"
        else:
            troubleshooting_guide += "### 通用故障排除步骤\n"
            troubleshooting_guide += "1. **检查电源和时钟**\n"
            troubleshooting_guide += "   - 验证供电电压范围\n"
            troubleshooting_guide += "   - 确认时钟源配置\n\n"
            troubleshooting_guide += "2. **验证寄存器配置**\n"
            troubleshooting_guide += "   - 检查模块使能位\n"
            troubleshooting_guide += "   - 确认引脚复用设置\n\n"
            troubleshooting_guide += "3. **分析时序和信号**\n"
            troubleshooting_guide += "   - 使用示波器检查信号\n"
            troubleshooting_guide += "   - 验证时序参数\n\n"
        
        return await create_agent_response(troubleshooting_guide, {"modules": modules})
    
    async def _handle_optimization(self, message: str, context: Dict) -> Dict[str, Any]:
        """处理优化建议"""
        modules = self._identify_modules(message)
        
        optimization_guide = "## ⚡ 优化建议\n\n"
        
        if modules:
            for module in modules:
                if module in self.technical_knowledge:
                    knowledge = self.technical_knowledge[module]
                    optimization_guide += f"### {module.upper()} 模块优化\n"
                    
                    for i, tip in enumerate(knowledge['optimization_tips'], 1):
                        optimization_guide += f"{i}. {tip}\n"
                    optimization_guide += "\n"
        else:
            optimization_guide += "### 系统级优化建议\n"
            optimization_guide += "1. **功耗优化**\n"
            optimization_guide += "   - 使用休眠模式\n"
            optimization_guide += "   - 关闭未使用的外设时钟\n"
            optimization_guide += "   - 降低系统时钟频率\n\n"
            optimization_guide += "2. **性能优化**\n"
            optimization_guide += "   - 使用DMA传输数据\n"
            optimization_guide += "   - 优化中断处理\n"
            optimization_guide += "   - 合理分配内存\n\n"
            optimization_guide += "3. **可靠性优化**\n"
            optimization_guide += "   - 启用看门狗\n"
            optimization_guide += "   - 使用硬件保护机制\n"
            optimization_guide += "   - 实现错误检测和恢复\n\n"
        
        return await create_agent_response(optimization_guide, {"modules": modules})
    
    async def _handle_design_guidance(self, message: str, context: Dict) -> Dict[str, Any]:
        """处理设计指导"""
        guidance = "## 📐 设计指导\n\n"
        
        # 根据消息内容提供相应的设计指导
        if "电机" in message or "motor" in message.lower():
            guidance += self._get_motor_control_guidance()
        elif "adc" in message.lower() or "采样" in message:
            guidance += self._get_adc_design_guidance()
        elif "通信" in message or "uart" in message.lower() or "can" in message.lower():
            guidance += self._get_communication_guidance()
        else:
            guidance += self._get_general_design_guidance()
        
        return await create_agent_response(guidance)
    
    def _get_motor_control_guidance(self) -> str:
        """获取电机控制设计指导"""
        return """### 电机控制系统设计
1. **硬件设计要点**
   - 选择合适的功率器件
   - 设计保护电路
   - 优化PCB布局减少EMI

2. **软件设计要点**
   - 实现FOC或六步换相算法
   - 配置MCPWM模块
   - 实现故障保护机制

3. **调试和优化**
   - 调整PID参数
   - 优化PWM频率
   - 测试保护功能

"""
    
    def _get_adc_design_guidance(self) -> str:
        """获取ADC设计指导"""
        return """### ADC采样系统设计
1. **硬件设计**
   - 选择合适的基准电压
   - 设计抗混叠滤波器
   - 优化模拟前端电路

2. **软件配置**
   - 配置采样时序
   - 设置DMA传输
   - 实现数据处理算法

3. **性能优化**
   - 校准ADC精度
   - 减少噪声干扰
   - 优化采样策略

"""
    
    def _get_communication_guidance(self) -> str:
        """获取通信设计指导"""
        return """### 通信系统设计
1. **协议选择**
   - UART: 简单点对点通信
   - CAN: 工业现场总线
   - I2C: 短距离多设备

2. **硬件设计**
   - 选择合适的收发器
   - 设计隔离和保护
   - 考虑EMC要求

3. **软件实现**
   - 实现协议栈
   - 设计错误处理
   - 优化传输效率

"""
    
    def _get_general_design_guidance(self) -> str:
        """获取通用设计指导"""
        return """### 通用设计原则
1. **系统架构**
   - 模块化设计
   - 分层架构
   - 接口标准化

2. **可靠性设计**
   - 冗余设计
   - 故障检测
   - 自恢复机制

3. **可维护性**
   - 代码规范
   - 文档完善
   - 测试覆盖

"""
    
    async def _handle_general_technical_query(self, message: str, context: Dict) -> Dict[str, Any]:
        """处理一般技术查询"""
        # 使用AutoGen智能体进行深度技术分析
        enhanced_message = f"""
作为LKS32MC08x微控制器技术专家，请分析以下技术问题：

{message}

请提供：
1. 技术原理分析
2. 实现方案建议
3. 潜在问题和解决方案
4. 性能优化建议
"""
        
        # 这里应该调用AutoGen智能体，暂时返回基本响应
        response = f"技术分析：{message}\n\n这需要更详细的技术分析，建议提供更具体的技术细节。"
        
        return await create_agent_response(response)
    
    def _identify_modules(self, message: str) -> List[str]:
        """识别消息中提到的模块"""
        modules = []
        message_lower = message.lower()
        
        module_keywords = {
            "adc": ["adc", "模数转换", "采样", "转换器"],
            "mcpwm": ["mcpwm", "pwm", "电机", "motor"],
            "gpio": ["gpio", "引脚", "输入输出", "io"],
            "uart": ["uart", "串口", "通信"],
            "dma": ["dma", "直接内存", "传输"]
        }
        
        for module, keywords in module_keywords.items():
            if any(keyword in message_lower for keyword in keywords):
                modules.append(module)
        
        return modules
