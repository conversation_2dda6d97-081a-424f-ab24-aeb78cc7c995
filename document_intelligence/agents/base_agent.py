"""
基础智能体类
定义所有智能体的通用接口和功能
"""
import asyncio
from abc import ABC, abstractmethod
from typing import Dict, Any, List, Optional
from autogen_agentchat.agents import AssistantAgent
from autogen_agentchat.messages import ChatMessage

class BaseAgent(ABC):
    """基础智能体抽象类"""

    def __init__(
        self,
        name: str,
        model_client,
        system_message: str,
        capabilities: Dict[str, Any],
        tools: Optional[List] = None
    ):
        self.name = name
        self.model_client = model_client
        self.system_message = system_message
        self.capabilities = capabilities
        self.tools = tools or []

        # 创建AutoGen智能体
        self.agent = AssistantAgent(
            name=self.name,
            model_client=self.model_client,
            system_message=self.system_message,
            tools=self.tools
        )

        # 性能统计
        self.stats = {
            "requests_handled": 0,
            "success_rate": 0.0,
            "average_response_time": 0.0,
            "total_tokens_used": 0
        }

    @abstractmethod
    async def process_request(self, request: Dict[str, Any]) -> Dict[str, Any]:
        """处理请求的抽象方法"""
        pass

    async def handle_message(self, message: str, context: Optional[Dict] = None) -> str:
        """处理消息并返回响应"""
        try:
            import time
            start_time = time.time()

            # 更新统计
            self.stats["requests_handled"] += 1

            # 构建请求
            request = {
                "message": message,
                "context": context or {},
                "timestamp": start_time
            }

            # 处理请求
            result = await self.process_request(request)

            # 更新性能统计
            response_time = time.time() - start_time
            self._update_stats(response_time, success=True)

            return result.get("response", "处理完成，但没有返回内容")

        except Exception as e:
            self._update_stats(0, success=False)
            return f"处理请求时发生错误: {str(e)}"

    def _update_stats(self, response_time: float, success: bool):
        """更新性能统计"""
        if success:
            # 更新平均响应时间
            total_time = self.stats["average_response_time"] * (self.stats["requests_handled"] - 1)
            self.stats["average_response_time"] = (total_time + response_time) / self.stats["requests_handled"]

        # 更新成功率
        if self.stats["requests_handled"] > 0:
            success_count = int(self.stats["success_rate"] * (self.stats["requests_handled"] - 1))
            if success:
                success_count += 1
            self.stats["success_rate"] = success_count / self.stats["requests_handled"]

    def get_capabilities(self) -> Dict[str, Any]:
        """获取智能体能力信息"""
        return {
            "name": self.name,
            "capabilities": self.capabilities,
            "tools": [tool.__class__.__name__ if hasattr(tool, '__class__') else str(tool) for tool in self.tools],
            "stats": self.stats
        }

    def get_status(self) -> Dict[str, Any]:
        """获取智能体状态"""
        return {
            "name": self.name,
            "status": "active",
            "stats": self.stats,
            "model": self.model_client.model if hasattr(self.model_client, 'model') else "unknown"
        }

    async def reset(self):
        """重置智能体状态"""
        self.stats = {
            "requests_handled": 0,
            "success_rate": 0.0,
            "average_response_time": 0.0,
            "total_tokens_used": 0
        }

    def __str__(self):
        return f"{self.__class__.__name__}(name={self.name})"

    def __repr__(self):
        return self.__str__()

class AgentRegistry:
    """智能体注册表"""

    def __init__(self):
        self._agents: Dict[str, BaseAgent] = {}

    def register(self, agent: BaseAgent):
        """注册智能体"""
        self._agents[agent.name] = agent

    def get(self, name: str) -> Optional[BaseAgent]:
        """获取智能体"""
        return self._agents.get(name)

    def list_agents(self) -> List[str]:
        """列出所有智能体名称"""
        return list(self._agents.keys())

    def get_all_agents(self) -> Dict[str, BaseAgent]:
        """获取所有智能体"""
        return self._agents.copy()

    def get_stats(self) -> Dict[str, Dict]:
        """获取所有智能体的统计信息"""
        return {name: agent.get_status() for name, agent in self._agents.items()}

# 全局智能体注册表
agent_registry = AgentRegistry()

# 工具函数
async def create_agent_response(content: str, metadata: Optional[Dict] = None) -> Dict[str, Any]:
    """创建标准的智能体响应格式"""
    return {
        "response": content,
        "metadata": metadata or {},
        "timestamp": asyncio.get_event_loop().time(),
        "success": True
    }
