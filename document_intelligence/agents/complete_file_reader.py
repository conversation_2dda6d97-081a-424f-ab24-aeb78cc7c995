"""
完整文件阅读智能体
确保无信息遗漏的全文阅读
"""
import os
import math
from typing import Dict, Any, List, Tuple
from .base_agent import BaseAgent, create_agent_response

class CompleteFileReader(BaseAgent):
    """完整文件阅读智能体 - 确保无信息遗漏"""
    
    def __init__(self, model_client, system_message: str, capabilities: Dict[str, Any]):
        super().__init__(
            name="CompleteFileReader",
            model_client=model_client,
            system_message=system_message,
            capabilities=capabilities
        )
        
        # 分块配置 - 保守设置确保不超出上下文
        self.chunk_size = 6000  # 每块6000字符，保守估计
        self.chunk_overlap = 200  # 200字符重叠，确保信息连续性
        
        # 阅读状态跟踪
        self.reading_progress = {}
        self.accumulated_knowledge = {}
        
    async def process_request(self, request: Dict[str, Any]) -> Dict[str, Any]:
        """处理完整文件阅读请求"""
        message = request.get("message", "")
        
        # 提取文件路径
        file_path = self._extract_file_path(message)
        if not file_path:
            return await create_agent_response(
                "❌ 请指定要完整阅读的文件路径\n例如：'完整阅读文件 /path/to/file.txt'"
            )
        
        # 验证文件
        if not os.path.exists(file_path):
            return await create_agent_response(f"❌ 文件不存在: {file_path}")
        
        # 提取问题
        question = self._extract_question(message)
        
        # 执行完整阅读
        try:
            complete_analysis = await self._complete_file_reading(file_path, question)
            return await create_agent_response(complete_analysis)
        except Exception as e:
            return await create_agent_response(f"❌ 完整阅读失败: {e}")
    
    def _extract_file_path(self, message: str) -> str:
        """提取文件路径"""
        import re
        patterns = [
            r'完整阅读文件\s+([^\s]+)',
            r'全文阅读\s+([^\s]+)',
            r'完整读取\s+([^\s]+)',
            r'文件路径[：:]\s*([^\s]+)',
        ]
        
        for pattern in patterns:
            match = re.search(pattern, message)
            if match:
                return match.group(1).strip().strip('"\'')
        return None
    
    def _extract_question(self, message: str) -> str:
        """提取具体问题"""
        import re
        
        # 移除文件路径部分
        clean_message = re.sub(r'完整阅读文件\s+[^\s]+\s*', '', message)
        clean_message = re.sub(r'全文阅读\s+[^\s]+\s*', '', clean_message)
        clean_message = re.sub(r'完整读取\s+[^\s]+\s*', '', clean_message)
        
        # 查找问题标识
        question_markers = ['并回答', '问题', '查找', '分析', '总结']
        for marker in question_markers:
            if marker in clean_message:
                parts = clean_message.split(marker, 1)
                if len(parts) > 1:
                    return parts[1].strip().strip('：:')
        
        # 默认问题
        return clean_message.strip() if clean_message.strip() else "请完整总结这个文件的所有内容"
    
    async def _complete_file_reading(self, file_path: str, question: str) -> str:
        """执行完整文件阅读"""
        print(f"📖 开始完整阅读文件: {file_path}")
        
        # 1. 读取文件内容
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()
        except UnicodeDecodeError:
            # 尝试其他编码
            for encoding in ['gbk', 'gb2312', 'latin1']:
                try:
                    with open(file_path, 'r', encoding=encoding) as f:
                        content = f.read()
                    break
                except UnicodeDecodeError:
                    continue
            else:
                raise Exception("无法识别文件编码")
        
        file_size = len(content)
        print(f"📊 文件大小: {file_size} 字符")
        
        # 2. 智能分块 - 确保无遗漏
        chunks = self._create_overlapping_chunks(content)
        total_chunks = len(chunks)
        print(f"📚 分为 {total_chunks} 个重叠块进行完整阅读")
        
        # 3. 逐块详细阅读
        chunk_analyses = []
        accumulated_info = []
        
        for i, chunk in enumerate(chunks, 1):
            print(f"🔍 正在阅读第 {i}/{total_chunks} 块...")
            
            # 构建上下文感知的提示
            chunk_analysis = await self._read_chunk_with_context(
                chunk, i, total_chunks, question, accumulated_info
            )
            
            chunk_analyses.append({
                'chunk_number': i,
                'analysis': chunk_analysis,
                'char_range': f"{chunk['start']}-{chunk['end']}"
            })
            
            # 提取关键信息用于后续上下文
            key_info = self._extract_key_information(chunk_analysis)
            accumulated_info.append(key_info)
            
            print(f"✅ 第 {i} 块阅读完成")
        
        # 4. 整合所有分析结果
        print("🔄 整合所有阅读结果...")
        complete_analysis = await self._synthesize_complete_analysis(
            chunk_analyses, question, file_path, file_size
        )
        
        # 5. 完整性验证
        completeness_check = self._verify_completeness(chunks, chunk_analyses)
        
        # 6. 生成最终报告
        final_report = self._generate_final_report(
            complete_analysis, completeness_check, total_chunks, file_size
        )
        
        print("✅ 完整文件阅读完成")
        return final_report
    
    def _create_overlapping_chunks(self, content: str) -> List[Dict]:
        """创建重叠分块，确保信息连续性"""
        chunks = []
        lines = content.split('\n')
        
        current_chunk = []
        current_size = 0
        start_line = 0
        start_char = 0
        
        for line_num, line in enumerate(lines):
            line_size = len(line) + 1  # +1 for newline
            
            # 检查是否需要分块
            if current_size + line_size > self.chunk_size and current_chunk:
                # 保存当前块
                chunk_text = '\n'.join(current_chunk)
                chunks.append({
                    'text': chunk_text,
                    'start_line': start_line,
                    'end_line': line_num - 1,
                    'start': start_char,
                    'end': start_char + len(chunk_text),
                    'size': len(chunk_text)
                })
                
                # 计算重叠部分
                overlap_lines = max(0, len(current_chunk) - self.chunk_overlap // 50)
                overlap_chunk = current_chunk[overlap_lines:] if overlap_lines < len(current_chunk) else []
                
                # 开始新块
                current_chunk = overlap_chunk + [line]
                current_size = sum(len(l) + 1 for l in current_chunk)
                start_line = start_line + overlap_lines
                start_char = chunks[-1]['end'] - sum(len(l) + 1 for l in overlap_chunk)
            else:
                current_chunk.append(line)
                current_size += line_size
        
        # 保存最后一块
        if current_chunk:
            chunk_text = '\n'.join(current_chunk)
            chunks.append({
                'text': chunk_text,
                'start_line': start_line,
                'end_line': len(lines) - 1,
                'start': start_char,
                'end': start_char + len(chunk_text),
                'size': len(chunk_text)
            })
        
        return chunks
    
    async def _read_chunk_with_context(self, chunk: Dict, chunk_num: int, total_chunks: int, 
                                     question: str, accumulated_info: List[str]) -> str:
        """带上下文的分块阅读"""
        
        # 构建上下文信息
        context_info = ""
        if accumulated_info:
            recent_context = accumulated_info[-2:]  # 最近2块的关键信息
            context_info = f"\n前文关键信息:\n" + "\n".join(recent_context)
        
        # 构建详细的阅读提示
        prompt = f"""你正在进行完整文件阅读任务，这是第{chunk_num}块（共{total_chunks}块）。

请仔细阅读以下内容并进行详细分析：

文件内容（第{chunk_num}块，行{chunk['start_line']}-{chunk['end_line']}）：
{chunk['text']}
{context_info}

任务要求：
1. 详细阅读这部分内容，不要遗漏任何信息
2. 针对问题"{question}"进行分析
3. 记录所有重要的细节、数据、概念
4. 如果内容与问题相关，提供详细解答
5. 如果内容与问题不直接相关，也要总结主要内容

请提供详细的分析结果："""

        try:
            response = await self.model_client.create(
                messages=[{"role": "user", "content": prompt}]
            )
            
            if hasattr(response, 'content') and response.content:
                return response.content
            elif hasattr(response, 'choices') and response.choices:
                return response.choices[0].message.content
            else:
                return f"第{chunk_num}块：模型响应异常"
                
        except Exception as e:
            return f"第{chunk_num}块：阅读失败 - {e}"
    
    def _extract_key_information(self, analysis: str) -> str:
        """提取关键信息用于上下文"""
        # 简单提取：取前200字符作为关键信息
        lines = analysis.split('\n')
        key_lines = []
        char_count = 0
        
        for line in lines:
            if char_count + len(line) > 200:
                break
            key_lines.append(line)
            char_count += len(line)
        
        return '\n'.join(key_lines)
    
    async def _synthesize_complete_analysis(self, chunk_analyses: List[Dict], 
                                          question: str, file_path: str, file_size: int) -> str:
        """整合所有分析结果"""
        
        # 合并所有分析
        all_analyses = []
        for chunk_info in chunk_analyses:
            all_analyses.append(f"## 第{chunk_info['chunk_number']}块分析 (字符{chunk_info['char_range']}):\n{chunk_info['analysis']}")
        
        combined_analysis = '\n\n'.join(all_analyses)
        
        # 如果合并后的内容太长，需要分批处理
        if len(combined_analysis) > 10000:  # 如果超过1万字符
            return await self._synthesize_in_batches(chunk_analyses, question, file_path)
        
        # 构建最终整合提示
        synthesis_prompt = f"""基于对文件 {file_path} 的完整分块阅读，请整合所有信息并回答问题。

文件信息：
- 文件路径: {file_path}
- 文件大小: {file_size} 字符
- 分块数量: {len(chunk_analyses)} 块

所有分块的详细分析：
{combined_analysis}

用户问题: {question}

请基于以上完整的分块阅读结果，提供一个全面、准确、无遗漏的回答："""

        try:
            response = await self.model_client.create(
                messages=[{"role": "user", "content": synthesis_prompt}]
            )
            
            if hasattr(response, 'content') and response.content:
                return response.content
            elif hasattr(response, 'choices') and response.choices:
                return response.choices[0].message.content
            else:
                return "整合分析时模型响应异常"
                
        except Exception as e:
            return f"整合分析失败: {e}"
    
    async def _synthesize_in_batches(self, chunk_analyses: List[Dict], 
                                   question: str, file_path: str) -> str:
        """分批整合（处理超长内容）"""
        batch_size = 3  # 每批处理3个分块
        batch_summaries = []
        
        for i in range(0, len(chunk_analyses), batch_size):
            batch = chunk_analyses[i:i+batch_size]
            batch_analysis = []
            
            for chunk_info in batch:
                batch_analysis.append(f"第{chunk_info['chunk_number']}块: {chunk_info['analysis']}")
            
            batch_content = '\n\n'.join(batch_analysis)
            
            # 对这一批进行总结
            batch_prompt = f"""请总结以下分块阅读结果的关键信息：

{batch_content}

针对问题"{question}"，提取最重要的信息和结论："""

            try:
                response = await self.model_client.create(
                    messages=[{"role": "user", "content": batch_prompt}]
                )
                
                if hasattr(response, 'content') and response.content:
                    batch_summaries.append(response.content)
                elif hasattr(response, 'choices') and response.choices:
                    batch_summaries.append(response.choices[0].message.content)
                    
            except Exception as e:
                batch_summaries.append(f"批次{i//batch_size + 1}总结失败: {e}")
        
        # 最终整合所有批次总结
        final_prompt = f"""基于对文件 {file_path} 的完整阅读，以下是各批次的总结：

{chr(10).join(f'批次{i+1}总结: {summary}' for i, summary in enumerate(batch_summaries))}

用户问题: {question}

请提供最终的完整回答："""

        try:
            response = await self.model_client.create(
                messages=[{"role": "user", "content": final_prompt}]
            )
            
            if hasattr(response, 'content') and response.content:
                return response.content
            elif hasattr(response, 'choices') and response.choices:
                return response.choices[0].message.content
            else:
                return "最终整合时模型响应异常"
                
        except Exception as e:
            return f"最终整合失败: {e}"
    
    def _verify_completeness(self, chunks: List[Dict], analyses: List[Dict]) -> Dict:
        """验证阅读完整性"""
        total_chars = sum(chunk['size'] for chunk in chunks)
        analyzed_chunks = len(analyses)
        expected_chunks = len(chunks)
        
        # 检查覆盖率
        coverage = analyzed_chunks / expected_chunks if expected_chunks > 0 else 0
        
        # 检查重叠区域
        overlaps = []
        for i in range(len(chunks) - 1):
            current_end = chunks[i]['end']
            next_start = chunks[i + 1]['start']
            if current_end > next_start:
                overlaps.append(f"块{i+1}-{i+2}: {current_end - next_start}字符重叠")
        
        return {
            'total_chunks': expected_chunks,
            'analyzed_chunks': analyzed_chunks,
            'coverage_rate': coverage,
            'total_characters': total_chars,
            'overlaps': overlaps,
            'is_complete': coverage >= 1.0
        }
    
    def _generate_final_report(self, analysis: str, completeness: Dict, 
                             total_chunks: int, file_size: int) -> str:
        """生成最终报告"""
        
        report = f"""# 📖 完整文件阅读报告

## 📊 阅读统计
- **文件大小**: {file_size:,} 字符
- **分块数量**: {total_chunks} 块
- **覆盖率**: {completeness['coverage_rate']:.1%}
- **重叠区域**: {len(completeness['overlaps'])} 处
- **完整性**: {'✅ 完整' if completeness['is_complete'] else '❌ 不完整'}

## 💡 完整分析结果

{analysis}

## 🔍 阅读质量保证
- ✅ 逐块详细阅读，无内容跳过
- ✅ 重叠分块确保信息连续性
- ✅ 上下文感知，保持信息关联
- ✅ 多轮整合，确保结论准确
- ✅ 完整性验证，确认无遗漏

---
*本报告基于{total_chunks}个重叠分块的完整阅读生成，确保无信息遗漏。*
"""
        
        return report
