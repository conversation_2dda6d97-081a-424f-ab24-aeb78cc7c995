"""
文档阅读理解智能体
专门处理长文档的智能理解，解决上下文长度限制问题
"""
import os
import re
import json
import hashlib
from typing import Dict, Any, List, Optional, Tuple
import numpy as np
from sentence_transformers import SentenceTransformer
import faiss
from .base_agent import BaseAgent, create_agent_response

class DocumentReader(BaseAgent):
    """文档阅读理解智能体"""
    
    def __init__(self, model_client, system_message: str, capabilities: Dict[str, Any]):
        super().__init__(
            name="DocumentReader",
            model_client=model_client,
            system_message=system_message,
            capabilities=capabilities
        )
        
        # 文档处理配置
        self.chunk_size = 512  # 每个文档块的大小
        self.chunk_overlap = 50  # 块之间的重叠
        self.max_context_chunks = 5  # 最大上下文块数
        
        # 向量化模型
        self.embedding_model = None
        self.vector_index = None
        
        # 文档存储
        self.document_chunks = []
        self.chunk_metadata = []
        self.document_structure = {}
        
        # 对话历史和上下文
        self.conversation_history = []
        self.current_context = []
        
        # 初始化
        self._initialize_embedding_model()
        self._load_and_process_document()
    
    def _initialize_embedding_model(self):
        """初始化嵌入模型"""
        try:
            # 使用轻量级的中文嵌入模型
            self.embedding_model = SentenceTransformer('paraphrase-multilingual-MiniLM-L12-v2')
            print("✅ 嵌入模型初始化成功")
        except Exception as e:
            print(f"⚠️  嵌入模型初始化失败，使用简单文本匹配: {e}")
            self.embedding_model = None
    
    def _load_and_process_document(self):
        """加载和处理文档"""
        try:
            # 获取文档路径
            doc_path = os.path.join(
                os.path.dirname(os.path.dirname(os.path.abspath(__file__))),
                "data", "full.md"
            )
            
            if not os.path.exists(doc_path):
                doc_path = os.path.join(
                    os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))),
                    "full.md"
                )
            
            if os.path.exists(doc_path):
                with open(doc_path, 'r', encoding='utf-8') as f:
                    content = f.read()
                
                print(f"📄 文档加载成功，长度: {len(content)} 字符")
                
                # 处理文档
                self._process_document(content)
                self._build_vector_index()
                
            else:
                print("❌ 未找到文档文件")
                
        except Exception as e:
            print(f"❌ 文档处理失败: {e}")
    
    def _process_document(self, content: str):
        """处理文档内容"""
        # 1. 按章节分割
        sections = self._split_by_sections(content)
        
        # 2. 智能分块
        for section_title, section_content in sections.items():
            chunks = self._smart_chunk_text(section_content, section_title)
            self.document_chunks.extend(chunks)
        
        print(f"📊 文档分块完成，共 {len(self.document_chunks)} 个块")
    
    def _split_by_sections(self, content: str) -> Dict[str, str]:
        """按章节分割文档"""
        sections = {}
        lines = content.split('\n')
        current_section = "前言"
        current_content = []
        
        for line in lines:
            # 检测章节标题
            if re.match(r'^#+\s+', line):
                # 保存前一个章节
                if current_content:
                    sections[current_section] = '\n'.join(current_content)
                
                # 开始新章节
                current_section = re.sub(r'^#+\s+', '', line).strip()
                current_content = [line]
            else:
                current_content.append(line)
        
        # 保存最后一个章节
        if current_content:
            sections[current_section] = '\n'.join(current_content)
        
        return sections
    
    def _smart_chunk_text(self, text: str, section_title: str) -> List[Dict[str, Any]]:
        """智能文本分块"""
        chunks = []
        lines = text.split('\n')
        current_chunk = []
        current_size = 0
        chunk_id = len(self.document_chunks)
        
        for line in lines:
            line_size = len(line)
            
            # 如果添加这行会超过块大小，且当前块不为空
            if current_size + line_size > self.chunk_size and current_chunk:
                # 保存当前块
                chunk_text = '\n'.join(current_chunk)
                chunks.append({
                    'id': chunk_id,
                    'text': chunk_text,
                    'section': section_title,
                    'size': current_size,
                    'line_start': len(self.document_chunks) + len(chunks),
                    'keywords': self._extract_keywords(chunk_text)
                })
                
                # 开始新块，保留重叠
                overlap_lines = current_chunk[-self.chunk_overlap:] if len(current_chunk) > self.chunk_overlap else current_chunk
                current_chunk = overlap_lines + [line]
                current_size = sum(len(l) for l in current_chunk)
                chunk_id += 1
            else:
                current_chunk.append(line)
                current_size += line_size
        
        # 保存最后一个块
        if current_chunk:
            chunk_text = '\n'.join(current_chunk)
            chunks.append({
                'id': chunk_id,
                'text': chunk_text,
                'section': section_title,
                'size': current_size,
                'line_start': len(self.document_chunks) + len(chunks),
                'keywords': self._extract_keywords(chunk_text)
            })
        
        return chunks
    
    def _extract_keywords(self, text: str) -> List[str]:
        """提取关键词"""
        # 简单的关键词提取
        keywords = []
        
        # 寄存器名称
        register_pattern = r'\b([A-Z][A-Z0-9_]{2,})\b'
        registers = re.findall(register_pattern, text)
        keywords.extend(registers[:5])  # 最多5个寄存器
        
        # 技术术语
        tech_terms = ['ADC', 'GPIO', 'UART', 'MCPWM', 'DMA', 'PWM', 'SPI', 'I2C', 'CAN']
        for term in tech_terms:
            if term in text.upper():
                keywords.append(term)
        
        # 数值（地址、频率等）
        numbers = re.findall(r'0x[0-9A-Fa-f]+|\d+[kKmM]?[Hh]?[zZ]?', text)
        keywords.extend(numbers[:3])  # 最多3个数值
        
        return list(set(keywords))
    
    def _build_vector_index(self):
        """构建向量索引"""
        if not self.embedding_model or not self.document_chunks:
            print("⚠️  跳过向量索引构建")
            return
        
        try:
            # 提取所有文本
            texts = [chunk['text'] for chunk in self.document_chunks]
            
            # 生成嵌入向量
            print("🔄 生成文档嵌入向量...")
            embeddings = self.embedding_model.encode(texts, show_progress_bar=True)
            
            # 构建FAISS索引
            dimension = embeddings.shape[1]
            self.vector_index = faiss.IndexFlatIP(dimension)  # 内积相似度
            
            # 标准化向量
            faiss.normalize_L2(embeddings)
            self.vector_index.add(embeddings.astype('float32'))
            
            print(f"✅ 向量索引构建完成，维度: {dimension}")
            
        except Exception as e:
            print(f"❌ 向量索引构建失败: {e}")
            self.vector_index = None
    
    async def process_request(self, request: Dict[str, Any]) -> Dict[str, Any]:
        """处理文档理解请求"""
        message = request.get("message", "")
        context = request.get("context", {})
        
        # 分析查询类型
        query_type = self._analyze_query_type(message)
        
        if query_type == "specific_search":
            return await self._handle_specific_search(message, context)
        elif query_type == "conceptual_understanding":
            return await self._handle_conceptual_understanding(message, context)
        elif query_type == "comparative_analysis":
            return await self._handle_comparative_analysis(message, context)
        elif query_type == "summary_request":
            return await self._handle_summary_request(message, context)
        else:
            return await self._handle_general_reading(message, context)
    
    def _analyze_query_type(self, message: str) -> str:
        """分析查询类型"""
        message_lower = message.lower()
        
        # 具体搜索
        if any(keyword in message_lower for keyword in ["寄存器", "地址", "配置", "参数"]):
            return "specific_search"
        
        # 概念理解
        if any(keyword in message_lower for keyword in ["是什么", "原理", "工作方式", "如何"]):
            return "conceptual_understanding"
        
        # 对比分析
        if any(keyword in message_lower for keyword in ["比较", "区别", "差异", "对比"]):
            return "comparative_analysis"
        
        # 总结请求
        if any(keyword in message_lower for keyword in ["总结", "概述", "整体", "全部"]):
            return "summary_request"
        
        return "general_reading"
    
    async def _handle_specific_search(self, message: str, context: Dict) -> Dict[str, Any]:
        """处理具体搜索请求"""
        # 1. 检索相关文档块
        relevant_chunks = self._retrieve_relevant_chunks(message, top_k=3)
        
        if not relevant_chunks:
            return await create_agent_response(
                "未找到相关信息。请尝试使用更具体的关键词。"
            )
        
        # 2. 构建上下文
        context_text = self._build_context_from_chunks(relevant_chunks)
        
        # 3. 生成回答
        enhanced_prompt = f"""
基于以下文档内容回答问题：

文档内容：
{context_text}

用户问题：{message}

请基于文档内容提供准确、详细的回答。如果文档中没有相关信息，请明确说明。
"""
        
        # 这里应该调用LLM，暂时返回基于检索的回答
        response = self._generate_response_from_chunks(message, relevant_chunks)
        
        return await create_agent_response(
            response,
            {
                "query_type": "specific_search",
                "chunks_used": len(relevant_chunks),
                "sections": list(set(chunk['section'] for chunk in relevant_chunks))
            }
        )
    
    def _retrieve_relevant_chunks(self, query: str, top_k: int = 5) -> List[Dict[str, Any]]:
        """检索相关文档块"""
        if self.vector_index and self.embedding_model:
            return self._vector_search(query, top_k)
        else:
            return self._keyword_search(query, top_k)
    
    def _vector_search(self, query: str, top_k: int) -> List[Dict[str, Any]]:
        """向量搜索"""
        try:
            # 生成查询向量
            query_embedding = self.embedding_model.encode([query])
            faiss.normalize_L2(query_embedding)
            
            # 搜索
            scores, indices = self.vector_index.search(query_embedding.astype('float32'), top_k)
            
            # 返回结果
            results = []
            for i, (score, idx) in enumerate(zip(scores[0], indices[0])):
                if idx < len(self.document_chunks):
                    chunk = self.document_chunks[idx].copy()
                    chunk['relevance_score'] = float(score)
                    results.append(chunk)
            
            return results
            
        except Exception as e:
            print(f"向量搜索失败: {e}")
            return self._keyword_search(query, top_k)
    
    def _keyword_search(self, query: str, top_k: int) -> List[Dict[str, Any]]:
        """关键词搜索"""
        query_lower = query.lower()
        query_keywords = re.findall(r'\b\w+\b', query_lower)
        
        chunk_scores = []
        
        for chunk in self.document_chunks:
            text_lower = chunk['text'].lower()
            score = 0
            
            # 关键词匹配得分
            for keyword in query_keywords:
                if keyword in text_lower:
                    score += text_lower.count(keyword)
            
            # 章节标题匹配加分
            if any(keyword in chunk['section'].lower() for keyword in query_keywords):
                score += 5
            
            # 关键词列表匹配加分
            for chunk_keyword in chunk['keywords']:
                if chunk_keyword.lower() in query_lower:
                    score += 3
            
            if score > 0:
                chunk_copy = chunk.copy()
                chunk_copy['relevance_score'] = score
                chunk_scores.append(chunk_copy)
        
        # 按得分排序
        chunk_scores.sort(key=lambda x: x['relevance_score'], reverse=True)
        
        return chunk_scores[:top_k]
    
    def _build_context_from_chunks(self, chunks: List[Dict[str, Any]]) -> str:
        """从文档块构建上下文"""
        context_parts = []
        
        for i, chunk in enumerate(chunks, 1):
            context_parts.append(f"## 相关内容 {i} (来自: {chunk['section']})")
            context_parts.append(chunk['text'])
            context_parts.append("")  # 空行分隔
        
        return '\n'.join(context_parts)
    
    def _generate_response_from_chunks(self, query: str, chunks: List[Dict[str, Any]]) -> str:
        """基于文档块生成回答"""
        if not chunks:
            return "未找到相关信息。"
        
        response_parts = []
        response_parts.append(f"基于文档内容，找到以下相关信息：\n")
        
        for i, chunk in enumerate(chunks, 1):
            response_parts.append(f"### {i}. 来自章节：{chunk['section']}")
            response_parts.append(f"**相关度**: {chunk.get('relevance_score', 0):.2f}")
            
            # 提取关键信息
            text = chunk['text']
            if len(text) > 300:
                # 截取最相关的部分
                text = text[:300] + "..."
            
            response_parts.append(f"**内容摘要**: {text}")
            response_parts.append("")
        
        # 添加关键词信息
        all_keywords = []
        for chunk in chunks:
            all_keywords.extend(chunk.get('keywords', []))
        
        if all_keywords:
            unique_keywords = list(set(all_keywords))
            response_parts.append(f"**相关关键词**: {', '.join(unique_keywords[:10])}")
        
        return '\n'.join(response_parts)
    
    async def _handle_conceptual_understanding(self, message: str, context: Dict) -> Dict[str, Any]:
        """处理概念理解请求"""
        # 检索相关概念
        relevant_chunks = self._retrieve_relevant_chunks(message, top_k=5)
        
        # 构建概念解释
        response = self._build_conceptual_explanation(message, relevant_chunks)
        
        return await create_agent_response(
            response,
            {"query_type": "conceptual_understanding", "chunks_used": len(relevant_chunks)}
        )
    
    def _build_conceptual_explanation(self, query: str, chunks: List[Dict[str, Any]]) -> str:
        """构建概念解释"""
        if not chunks:
            return "未找到相关概念信息。"
        
        explanation = f"## 概念解释：{query}\n\n"
        
        # 按章节组织信息
        sections = {}
        for chunk in chunks:
            section = chunk['section']
            if section not in sections:
                sections[section] = []
            sections[section].append(chunk)
        
        for section, section_chunks in sections.items():
            explanation += f"### 从 {section} 章节：\n"
            
            for chunk in section_chunks:
                # 提取定义和关键信息
                text = chunk['text']
                key_sentences = self._extract_key_sentences(text, query)
                
                for sentence in key_sentences:
                    explanation += f"- {sentence}\n"
            
            explanation += "\n"
        
        return explanation
    
    def _extract_key_sentences(self, text: str, query: str) -> List[str]:
        """提取关键句子"""
        sentences = re.split(r'[。！？\n]', text)
        query_keywords = re.findall(r'\b\w+\b', query.lower())
        
        key_sentences = []
        for sentence in sentences:
            sentence = sentence.strip()
            if len(sentence) < 10:  # 跳过太短的句子
                continue
            
            # 检查是否包含查询关键词
            sentence_lower = sentence.lower()
            if any(keyword in sentence_lower for keyword in query_keywords):
                key_sentences.append(sentence)
        
        return key_sentences[:5]  # 最多返回5个关键句子
    
    async def _handle_summary_request(self, message: str, context: Dict) -> Dict[str, Any]:
        """处理总结请求"""
        # 获取文档结构概览
        summary = self._generate_document_summary()
        
        return await create_agent_response(
            summary,
            {"query_type": "summary_request", "total_chunks": len(self.document_chunks)}
        )
    
    def _generate_document_summary(self) -> str:
        """生成文档总结"""
        summary_parts = []
        summary_parts.append("# LKS32MC08x 用户手册总结\n")
        
        # 按章节统计
        section_stats = {}
        for chunk in self.document_chunks:
            section = chunk['section']
            if section not in section_stats:
                section_stats[section] = {
                    'chunks': 0,
                    'keywords': set(),
                    'size': 0
                }
            
            section_stats[section]['chunks'] += 1
            section_stats[section]['keywords'].update(chunk['keywords'])
            section_stats[section]['size'] += chunk['size']
        
        summary_parts.append(f"## 文档结构概览")
        summary_parts.append(f"- 总章节数: {len(section_stats)}")
        summary_parts.append(f"- 总文档块: {len(self.document_chunks)}")
        summary_parts.append("")
        
        summary_parts.append("## 主要章节")
        for section, stats in section_stats.items():
            if stats['chunks'] > 1:  # 只显示有实质内容的章节
                keywords_str = ', '.join(list(stats['keywords'])[:5])
                summary_parts.append(f"### {section}")
                summary_parts.append(f"- 内容块数: {stats['chunks']}")
                summary_parts.append(f"- 主要关键词: {keywords_str}")
                summary_parts.append("")
        
        return '\n'.join(summary_parts)
    
    async def _handle_general_reading(self, message: str, context: Dict) -> Dict[str, Any]:
        """处理一般阅读请求"""
        # 检索相关内容
        relevant_chunks = self._retrieve_relevant_chunks(message, top_k=3)
        
        # 生成回答
        response = self._generate_response_from_chunks(message, relevant_chunks)
        
        return await create_agent_response(
            response,
            {"query_type": "general_reading", "chunks_used": len(relevant_chunks)}
        )
    
    def get_reading_stats(self) -> Dict[str, Any]:
        """获取阅读统计"""
        return {
            "total_chunks": len(self.document_chunks),
            "total_sections": len(set(chunk['section'] for chunk in self.document_chunks)),
            "embedding_model_loaded": self.embedding_model is not None,
            "vector_index_built": self.vector_index is not None,
            "conversation_turns": len(self.conversation_history)
        }
