"""
智能体模块
包含所有专业化智能体的实现
"""

from .document_manager import DocumentManager
from .technical_expert import TechnicalExpert
from .code_engineer import CodeEngineer
from .system_architect import SystemArchitect
from .coordinator import Coordinator
from .simple_document_reader import SimpleDocumentReader

# DocumentReader需要额外依赖，暂时注释
# from .document_reader import DocumentReader

__all__ = [
    "DocumentManager",
    "TechnicalExpert",
    "CodeEngineer",
    "SystemArchitect",
    "Coordinator",
    "SimpleDocumentReader"
    # "DocumentReader"
]
