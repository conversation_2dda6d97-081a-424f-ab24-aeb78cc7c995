"""
代码工程师智能体
负责代码生成、优化和驱动开发
"""
import re
from typing import Dict, Any, List, Optional
from .base_agent import BaseAgent, create_agent_response

class CodeEngineer(BaseAgent):
    """代码工程师智能体"""
    
    def __init__(self, model_client, system_message: str, capabilities: Dict[str, Any]):
        super().__init__(
            name="CodeEngineer",
            model_client=model_client,
            system_message=system_message,
            capabilities=capabilities
        )
        
        # 代码模板库
        self.code_templates = {
            "gpio_init": self._get_gpio_init_template(),
            "adc_init": self._get_adc_init_template(),
            "uart_init": self._get_uart_init_template(),
            "mcpwm_init": self._get_mcpwm_init_template(),
            "dma_init": self._get_dma_init_template(),
            "interrupt_handler": self._get_interrupt_template()
        }
        
        # 寄存器定义
        self.register_definitions = {
            "GPIO": {
                "base_addresses": {
                    "GPIOA": "0x40010000",
                    "GPIOB": "0x40010400", 
                    "GPIOC": "0x40010800"
                },
                "registers": {
                    "PIE": "0x00",  # 输入使能
                    "POE": "0x04",  # 输出使能
                    "PDI": "0x08",  # 输入数据
                    "PDO": "0x0C",  # 输出数据
                    "PUE": "0x10",  # 上拉使能
                    "PODE": "0x14"  # 开漏使能
                }
            },
            "ADC": {
                "base_address": "0x40020000",
                "registers": {
                    "CFG": "0x00",
                    "TRIG": "0x04",
                    "IE": "0x08",
                    "IF": "0x0C"
                }
            }
        }
    
    async def process_request(self, request: Dict[str, Any]) -> Dict[str, Any]:
        """处理代码生成请求"""
        message = request.get("message", "")
        context = request.get("context", {})
        
        # 分析代码请求类型
        code_type = self._analyze_code_type(message)
        
        if code_type == "initialization":
            return await self._handle_initialization_code(message, context)
        elif code_type == "driver":
            return await self._handle_driver_code(message, context)
        elif code_type == "interrupt":
            return await self._handle_interrupt_code(message, context)
        elif code_type == "configuration":
            return await self._handle_configuration_code(message, context)
        else:
            return await self._handle_general_code_request(message, context)
    
    def _analyze_code_type(self, message: str) -> str:
        """分析代码请求类型"""
        message_lower = message.lower()
        
        if any(keyword in message_lower for keyword in ["初始化", "init", "配置", "setup"]):
            return "initialization"
        elif any(keyword in message_lower for keyword in ["驱动", "driver", "库", "lib"]):
            return "driver"
        elif any(keyword in message_lower for keyword in ["中断", "interrupt", "isr", "handler"]):
            return "interrupt"
        elif any(keyword in message_lower for keyword in ["寄存器", "register", "配置"]):
            return "configuration"
        else:
            return "general"
    
    async def _handle_initialization_code(self, message: str, context: Dict) -> Dict[str, Any]:
        """处理初始化代码生成"""
        modules = self._identify_modules(message)
        
        if not modules:
            return await create_agent_response(
                "请指定需要初始化的模块，例如：GPIO、ADC、UART、MCPWM等。"
            )
        
        generated_code = "// LKS32MC08x 模块初始化代码\n"
        generated_code += "#include \"lks32mc08x.h\"\n\n"
        
        for module in modules:
            if module in self.code_templates:
                template_key = f"{module}_init"
                if template_key in self.code_templates:
                    generated_code += self.code_templates[template_key]
                    generated_code += "\n"
        
        # 添加主函数示例
        generated_code += self._get_main_function_template(modules)
        
        return await create_agent_response(
            f"生成的初始化代码：\n\n```c\n{generated_code}\n```",
            {"modules": modules, "code_type": "initialization"}
        )
    
    async def _handle_driver_code(self, message: str, context: Dict) -> Dict[str, Any]:
        """处理驱动代码生成"""
        modules = self._identify_modules(message)
        
        if not modules:
            return await create_agent_response(
                "请指定需要生成驱动的模块。"
            )
        
        driver_code = ""
        for module in modules:
            driver_code += self._generate_driver_code(module)
            driver_code += "\n"
        
        return await create_agent_response(
            f"生成的驱动代码：\n\n```c\n{driver_code}\n```",
            {"modules": modules, "code_type": "driver"}
        )
    
    async def _handle_interrupt_code(self, message: str, context: Dict) -> Dict[str, Any]:
        """处理中断代码生成"""
        interrupt_code = self.code_templates["interrupt_handler"]
        
        return await create_agent_response(
            f"中断处理代码模板：\n\n```c\n{interrupt_code}\n```",
            {"code_type": "interrupt"}
        )
    
    async def _handle_configuration_code(self, message: str, context: Dict) -> Dict[str, Any]:
        """处理配置代码生成"""
        # 提取寄存器名称
        registers = self._extract_register_names(message)
        
        if not registers:
            return await create_agent_response(
                "请指定需要配置的寄存器名称。"
            )
        
        config_code = "// 寄存器配置代码\n"
        for reg in registers:
            config_code += f"// 配置 {reg} 寄存器\n"
            config_code += f"{reg} = 0x00000000; // 请根据需求设置具体值\n\n"
        
        return await create_agent_response(
            f"生成的配置代码：\n\n```c\n{config_code}\n```",
            {"registers": registers, "code_type": "configuration"}
        )
    
    async def _handle_general_code_request(self, message: str, context: Dict) -> Dict[str, Any]:
        """处理一般代码请求"""
        response = f"收到代码生成请求：{message}\n\n"
        response += "请提供更具体的需求，例如：\n"
        response += "- 需要初始化哪些模块\n"
        response += "- 需要实现什么功能\n"
        response += "- 有什么特殊要求\n"
        
        return await create_agent_response(response)
    
    def _identify_modules(self, message: str) -> List[str]:
        """识别消息中的模块"""
        modules = []
        message_upper = message.upper()
        
        module_patterns = {
            "gpio": ["GPIO", "引脚", "IO"],
            "adc": ["ADC", "模数转换", "采样"],
            "uart": ["UART", "串口"],
            "mcpwm": ["MCPWM", "PWM", "电机"],
            "dma": ["DMA", "直接内存"]
        }
        
        for module, keywords in module_patterns.items():
            if any(keyword in message_upper for keyword in keywords):
                modules.append(module)
        
        return modules
    
    def _extract_register_names(self, message: str) -> List[str]:
        """提取寄存器名称"""
        # 寄存器名称模式
        pattern = r'\b([A-Z][A-Z0-9_]{2,})\b'
        matches = re.findall(pattern, message.upper())
        
        # 过滤常见的非寄存器词汇
        exclude_words = {"GPIO", "ADC", "UART", "MCPWM", "DMA", "CPU", "MCU"}
        registers = [match for match in matches if match not in exclude_words]
        
        return registers
    
    def _get_gpio_init_template(self) -> str:
        """GPIO初始化模板"""
        return """
// GPIO初始化函数
void GPIO_Init(void)
{
    // 使能GPIO时钟
    SYS_CLKFEN |= SYS_CLKFEN_GPIOA | SYS_CLKFEN_GPIOB | SYS_CLKFEN_GPIOC;
    
    // 配置PA0为输出
    GPIOA_POE |= GPIO_Pin_0;  // 输出使能
    GPIOA_PDO &= ~GPIO_Pin_0; // 输出低电平
    
    // 配置PA1为输入，使能上拉
    GPIOA_PIE |= GPIO_Pin_1;  // 输入使能
    GPIOA_PUE |= GPIO_Pin_1;  // 上拉使能
}
"""
    
    def _get_adc_init_template(self) -> str:
        """ADC初始化模板"""
        return """
// ADC初始化函数
void ADC_Init(void)
{
    // 使能ADC时钟
    SYS_CLKFEN |= SYS_CLKFEN_ADC;
    
    // 配置ADC基准电压为2.4V
    SYS_AFE_REG1 |= SYS_AFE_REG1_VREF_SEL;
    
    // 配置ADC通道0
    ADC0_CHN0 = ADC_CHN_AIN0;  // 选择AIN0输入
    ADC0_CHNT0 = 0;            // 单通道模式
    
    // 配置ADC触发模式
    ADC0_TRIG = ADC_TRIG_SOFT; // 软件触发
    
    // 使能ADC
    ADC0_CFG |= ADC_CFG_EN;
}
"""
    
    def _get_uart_init_template(self) -> str:
        """UART初始化模板"""
        return """
// UART初始化函数 (波特率115200)
void UART_Init(void)
{
    // 使能UART时钟
    SYS_CLKFEN |= SYS_CLKFEN_UART0;
    
    // 配置GPIO为UART功能
    GPIOA_F3210 |= (GPIO_AF_UART0_TX << GPIO_Pin_2*4) | 
                   (GPIO_AF_UART0_RX << GPIO_Pin_3*4);
    
    // 计算波特率分频值 (假设系统时钟48MHz)
    // 分频值 = 48000000 / (16 * 115200) = 26.04 ≈ 26
    UART0_DIVH = 0;
    UART0_DIVL = 26;
    
    // 配置UART参数
    UART0_CTRL = UART_CTRL_TXEN | UART_CTRL_RXEN; // 使能发送和接收
}
"""
    
    def _get_mcpwm_init_template(self) -> str:
        """MCPWM初始化模板"""
        return """
// MCPWM初始化函数
void MCPWM_Init(void)
{
    // 使能MCPWM时钟
    SYS_CLKFEN |= SYS_CLKFEN_MCPWM;
    
    // 配置PWM频率 (假设20kHz)
    // 计数器周期 = 48MHz / 20kHz = 2400
    MCPWM_TH = 2400;
    
    // 配置通道0占空比 (50%)
    MCPWM_TH00 = 1200;
    MCPWM_TH01 = 1200;
    
    // 配置死区时间 (1μs)
    MCPWM_DTH00 = 48;  // 48个时钟周期 = 1μs
    MCPWM_DTH01 = 48;
    
    // 配置PWM模式为中心对齐
    MCPWM_IO01 |= MCPWM_IO_CENTER_ALIGN;
    
    // 使能PWM输出
    MCPWM_IO01 |= MCPWM_IO_CH0_EN | MCPWM_IO_CH1_EN;
}
"""
    
    def _get_dma_init_template(self) -> str:
        """DMA初始化模板"""
        return """
// DMA初始化函数
void DMA_Init(void)
{
    // 使能DMA时钟
    SYS_CLKFEN |= SYS_CLKFEN_DMA;
    
    // 配置DMA通道0 (ADC到内存)
    DMA_CCR0 = DMA_CCR_EN |           // 使能通道
               DMA_CCR_MINC |         // 内存地址递增
               DMA_CCR_PSIZE_16 |     // 外设数据宽度16位
               DMA_CCR_MSIZE_16 |     // 内存数据宽度16位
               DMA_CCR_DIR_P2M;       // 外设到内存
    
    // 设置传输数量
    DMA_CTMS0 = 100;  // 传输100个数据
    
    // 设置外设地址 (ADC数据寄存器)
    DMA_CPAR0 = (uint32_t)&ADC0_DAT0;
    
    // 设置内存地址
    extern uint16_t adc_buffer[100];
    DMA_CMAR0 = (uint32_t)adc_buffer;
}
"""
    
    def _get_interrupt_template(self) -> str:
        """中断处理模板"""
        return """
// 中断处理函数模板
void ADC_IRQHandler(void)
{
    // 检查中断标志
    if (ADC0_IF & ADC_IF_EOC)
    {
        // 清除中断标志
        ADC0_IF = ADC_IF_EOC;
        
        // 读取ADC数据
        uint16_t adc_value = ADC0_DAT0;
        
        // 处理ADC数据
        // TODO: 添加用户代码
    }
}

// 中断向量表配置
void NVIC_Configuration(void)
{
    // 设置中断优先级
    NVIC_SetPriority(ADC_IRQn, 1);
    
    // 使能中断
    NVIC_EnableIRQ(ADC_IRQn);
}
"""
    
    def _get_main_function_template(self, modules: List[str]) -> str:
        """主函数模板"""
        main_code = """
int main(void)
{
    // 系统初始化
    SystemInit();
    
"""
        
        # 添加模块初始化调用
        for module in modules:
            if module == "gpio":
                main_code += "    GPIO_Init();\n"
            elif module == "adc":
                main_code += "    ADC_Init();\n"
            elif module == "uart":
                main_code += "    UART_Init();\n"
            elif module == "mcpwm":
                main_code += "    MCPWM_Init();\n"
            elif module == "dma":
                main_code += "    DMA_Init();\n"
        
        main_code += """
    // 主循环
    while(1)
    {
        // TODO: 添加应用代码
        
        // 延时
        for(volatile int i = 0; i < 100000; i++);
    }
}
"""
        return main_code
    
    def _generate_driver_code(self, module: str) -> str:
        """生成驱动代码"""
        if module == "gpio":
            return self._generate_gpio_driver()
        elif module == "adc":
            return self._generate_adc_driver()
        elif module == "uart":
            return self._generate_uart_driver()
        else:
            return f"// {module.upper()} 驱动代码待实现\n"
    
    def _generate_gpio_driver(self) -> str:
        """生成GPIO驱动"""
        return """
// GPIO驱动函数
void GPIO_SetPin(GPIO_TypeDef* GPIOx, uint16_t GPIO_Pin)
{
    GPIOx->PDO |= GPIO_Pin;
}

void GPIO_ResetPin(GPIO_TypeDef* GPIOx, uint16_t GPIO_Pin)
{
    GPIOx->PDO &= ~GPIO_Pin;
}

uint8_t GPIO_ReadPin(GPIO_TypeDef* GPIOx, uint16_t GPIO_Pin)
{
    return (GPIOx->PDI & GPIO_Pin) ? 1 : 0;
}

void GPIO_TogglePin(GPIO_TypeDef* GPIOx, uint16_t GPIO_Pin)
{
    GPIOx->PDO ^= GPIO_Pin;
}
"""
    
    def _generate_adc_driver(self) -> str:
        """生成ADC驱动"""
        return """
// ADC驱动函数
void ADC_StartConversion(void)
{
    ADC0_SWT |= ADC_SWT_START;
}

uint16_t ADC_GetValue(uint8_t channel)
{
    if (channel < 20)
    {
        return *((volatile uint16_t*)(ADC0_BASE + 0x10 + channel * 4));
    }
    return 0;
}

uint8_t ADC_IsConversionComplete(void)
{
    return (ADC0_IF & ADC_IF_EOC) ? 1 : 0;
}
"""
    
    def _generate_uart_driver(self) -> str:
        """生成UART驱动"""
        return """
// UART驱动函数
void UART_SendByte(uint8_t data)
{
    while (!(UART0_STT & UART_STT_TXE));  // 等待发送缓冲区空
    UART0_BUFF = data;
}

uint8_t UART_ReceiveByte(void)
{
    while (!(UART0_STT & UART_STT_RXF));  // 等待接收数据
    return UART0_BUFF;
}

void UART_SendString(const char* str)
{
    while (*str)
    {
        UART_SendByte(*str++);
    }
}
"""
