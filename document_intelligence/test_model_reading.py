#!/usr/bin/env python3
"""
测试模型是否真正在阅读和理解文档
"""
import asyncio
import sys
import os

sys.path.append(os.path.dirname(os.path.abspath(__file__)))
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from config.models_config import get_agent_model_client
from config.agents_config import AGENT_SYSTEM_PROMPTS, AGENT_CAPABILITIES
from agents.simple_document_reader import SimpleDocumentReader

async def test_model_understanding():
    """测试模型是否真正理解文档"""
    print("🧠 测试模型文档理解能力")
    print("=" * 50)
    
    # 初始化智能体
    print("🔧 初始化文档阅读理解智能体...")
    try:
        model_client = get_agent_model_client("DocumentReader")
        system_message = AGENT_SYSTEM_PROMPTS["DocumentReader"]
        capabilities = AGENT_CAPABILITIES["DocumentReader"]
        
        doc_reader = SimpleDocumentReader(model_client, system_message, capabilities)
        print("✅ 初始化完成")
        
    except Exception as e:
        print(f"❌ 初始化失败: {e}")
        return
    
    # 设计需要模型理解和推理的问题
    understanding_tests = [
        {
            "category": "概念理解",
            "query": "请解释ADC模块的工作原理，包括它如何将模拟信号转换为数字信号",
            "expected": "模型应该基于文档内容解释ADC的工作原理"
        },
        {
            "category": "配置推理",
            "query": "如果我想配置GPIO PA0作为输出并控制LED，需要设置哪些寄存器？",
            "expected": "模型应该基于文档推理出具体的配置步骤"
        },
        {
            "category": "参数计算",
            "query": "如果系统时钟是48MHz，我想设置UART波特率为115200，分频值应该是多少？",
            "expected": "模型应该基于文档中的公式进行计算"
        },
        {
            "category": "故障分析",
            "query": "ADC采样值不准确可能是什么原因？应该如何排查？",
            "expected": "模型应该基于文档分析可能的原因"
        },
        {
            "category": "对比分析",
            "query": "比较MCPWM的中心对齐模式和边沿对齐模式的区别",
            "expected": "模型应该基于文档对比两种模式"
        }
    ]
    
    print(f"\n🧪 开始理解能力测试 ({len(understanding_tests)} 个用例):")
    
    for i, test in enumerate(understanding_tests, 1):
        print(f"\n📝 测试 {i}: {test['category']}")
        print(f"❓ 问题: {test['query']}")
        print(f"🎯 期望: {test['expected']}")
        print("-" * 60)
        
        try:
            # 显示检索过程
            print("🔍 第一步：检索相关文档...")
            chunks = doc_reader._search_relevant_chunks(test['query'], top_k=3)
            print(f"📊 找到 {len(chunks)} 个相关文档块:")
            for j, chunk in enumerate(chunks, 1):
                score = chunk.get('relevance_score', 0)
                chapter = chunk.get('chapter', '未知章节')
                print(f"  {j}. {chapter} (相关度: {score:.2f})")
            
            # 显示上下文构建
            print("\n📋 第二步：构建上下文...")
            context = doc_reader._build_context_from_chunks(chunks, test['query'])
            context_length = len(context)
            print(f"📏 上下文长度: {context_length} 字符")
            
            # 显示模型调用
            print("\n🤖 第三步：模型理解和生成...")
            start_time = asyncio.get_event_loop().time()
            response = await doc_reader.handle_message(test['query'])
            end_time = asyncio.get_event_loop().time()
            
            print(f"💡 模型回答:\n{response}")
            print(f"⏱️  总耗时: {end_time - start_time:.2f}秒")
            
            # 分析回答质量
            print(f"\n📊 回答分析:")
            print(f"  • 回答长度: {len(response)} 字符")
            
            # 检查是否包含文档信息
            if "基于" in response and "文档" in response:
                print("  • ✅ 明确基于文档内容")
            else:
                print("  • ⚠️  未明确说明基于文档")
            
            # 检查是否有推理
            reasoning_indicators = ["因为", "所以", "由于", "因此", "根据", "可以得出"]
            if any(indicator in response for indicator in reasoning_indicators):
                print("  • ✅ 包含推理过程")
            else:
                print("  • ⚠️  缺少推理过程")
            
            # 检查是否有具体信息
            if any(keyword in response for keyword in ["寄存器", "地址", "配置", "步骤"]):
                print("  • ✅ 包含具体技术信息")
            else:
                print("  • ⚠️  缺少具体技术信息")
            
        except Exception as e:
            print(f"❌ 测试失败: {e}")
        
        print("\n" + "=" * 60)
        
        # 添加延时
        await asyncio.sleep(1)

async def test_context_awareness():
    """测试上下文感知能力"""
    print("\n🔗 测试上下文感知能力")
    print("-" * 40)
    
    # 初始化
    model_client = get_agent_model_client("DocumentReader")
    system_message = AGENT_SYSTEM_PROMPTS["DocumentReader"]
    capabilities = AGENT_CAPABILITIES["DocumentReader"]
    doc_reader = SimpleDocumentReader(model_client, system_message, capabilities)
    
    # 多轮对话测试
    conversation = [
        "ADC模块有哪些主要功能？",
        "它支持多少个通道？",
        "如何配置ADC的触发模式？",
        "刚才提到的触发模式有什么区别？"
    ]
    
    print("🗣️  多轮对话测试:")
    
    for i, question in enumerate(conversation, 1):
        print(f"\n👤 用户 {i}: {question}")
        
        try:
            response = await doc_reader.handle_message(question)
            print(f"🤖 智能体: {response[:200]}..." if len(response) > 200 else f"🤖 智能体: {response}")
            
        except Exception as e:
            print(f"❌ 回答失败: {e}")
        
        await asyncio.sleep(0.5)

async def test_document_coverage():
    """测试文档覆盖范围"""
    print("\n📖 测试文档覆盖范围")
    print("-" * 40)
    
    # 初始化
    model_client = get_agent_model_client("DocumentReader")
    system_message = AGENT_SYSTEM_PROMPTS["DocumentReader"]
    capabilities = AGENT_CAPABILITIES["DocumentReader"]
    doc_reader = SimpleDocumentReader(model_client, system_message, capabilities)
    
    # 测试不同模块的覆盖
    modules = [
        "GPIO模块",
        "ADC模块", 
        "UART模块",
        "MCPWM模块",
        "DMA模块",
        "FLASH模块",
        "系统时钟",
        "中断控制"
    ]
    
    print("🔍 模块覆盖测试:")
    
    for module in modules:
        query = f"{module}的主要功能是什么？"
        print(f"\n📋 查询: {query}")
        
        try:
            chunks = doc_reader._search_relevant_chunks(query, top_k=1)
            if chunks:
                score = chunks[0].get('relevance_score', 0)
                chapter = chunks[0].get('chapter', '未知')
                print(f"  ✅ 找到相关内容 (章节: {chapter}, 相关度: {score:.2f})")
            else:
                print(f"  ❌ 未找到相关内容")
                
        except Exception as e:
            print(f"  ❌ 查询失败: {e}")

async def main():
    """主函数"""
    print("🧠 模型文档理解能力测试")
    print("选择测试模式:")
    print("1. 理解能力测试")
    print("2. 上下文感知测试")
    print("3. 文档覆盖测试")
    print("4. 全部测试")
    
    try:
        choice = input("请选择 (1/2/3/4): ").strip()
        
        if choice == "1":
            await test_model_understanding()
        elif choice == "2":
            await test_context_awareness()
        elif choice == "3":
            await test_document_coverage()
        elif choice == "4":
            await test_model_understanding()
            await test_context_awareness()
            await test_document_coverage()
        else:
            print("无效选择，运行理解能力测试")
            await test_model_understanding()
            
    except KeyboardInterrupt:
        print("\n👋 再见！")

if __name__ == "__main__":
    asyncio.run(main())
