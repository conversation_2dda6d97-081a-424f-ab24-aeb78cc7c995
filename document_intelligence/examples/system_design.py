#!/usr/bin/env python3
"""
系统设计示例
演示如何使用系统进行系统架构设计
"""
import asyncio
import sys
import os

# 添加父目录到路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))))

from main import DocumentIntelligenceSystem

async def system_design_demo():
    """系统设计演示"""
    print("🏗️  LKS32MC08x系统设计演示")
    print("=" * 50)
    
    # 初始化系统
    system = DocumentIntelligenceSystem()
    await system.initialize()
    
    # 系统设计需求列表
    design_requirements = [
        {
            "category": "电机控制系统",
            "requirement": "设计一个三相无刷电机控制系统，支持FOC算法",
            "complexity": "high"
        },
        {
            "category": "数据采集系统", 
            "requirement": "设计多通道数据采集系统，支持实时处理和存储",
            "complexity": "medium"
        },
        {
            "category": "通信网关",
            "requirement": "设计支持UART和CAN的通信网关系统",
            "complexity": "medium"
        },
        {
            "category": "电源管理系统",
            "requirement": "设计低功耗电源管理系统，支持多种工作模式",
            "complexity": "medium"
        },
        {
            "category": "工业控制器",
            "requirement": "设计工业控制器，集成传感器接口、执行器控制和通信功能",
            "complexity": "high"
        }
    ]
    
    # 逐个设计系统
    for i, req in enumerate(design_requirements, 1):
        print(f"\n📋 设计需求 {i}: {req['category']}")
        print(f"📝 {req['requirement']}")
        print(f"🎯 复杂度: {req['complexity']}")
        print("-" * 40)
        
        try:
            # 使用系统架构师设计系统
            response = await system.design_system(req['requirement'])
            print(f"🏗️  设计方案:\n{response}")
            
            # 如果是高复杂度项目，获取技术分析
            if req['complexity'] == 'high':
                print(f"\n🔬 技术分析:")
                tech_analysis = await system.analyze_technical(f"分析{req['requirement']}的技术难点和解决方案")
                print(tech_analysis)
            
        except Exception as e:
            print(f"❌ 系统设计失败: {e}")
        
        print("\n" + "=" * 50)
        
        # 添加延时
        await asyncio.sleep(1)

async def interactive_design():
    """交互式系统设计"""
    print("\n🎯 交互式系统设计")
    print("输入 'quit' 退出")
    print("-" * 30)
    
    system = DocumentIntelligenceSystem()
    await system.initialize()
    
    while True:
        try:
            requirement = input("\n📝 请描述系统需求: ").strip()
            
            if requirement.lower() in ['quit', 'exit', 'q']:
                break
            
            if not requirement:
                continue
            
            print("🏗️  正在设计系统架构...")
            response = await system.design_system(requirement)
            print(f"\n🏗️  设计方案:\n{response}")
            
            # 询问是否需要技术分析
            analyze = input("\n🔬 是否需要技术分析？(y/n): ").strip().lower()
            if analyze in ['y', 'yes']:
                tech_response = await system.analyze_technical(f"分析{requirement}的技术实现")
                print(f"\n🔬 技术分析:\n{tech_response}")
            
            # 询问是否需要代码框架
            code = input("\n💻 是否需要代码框架？(y/n): ").strip().lower()
            if code in ['y', 'yes']:
                code_response = await system.generate_code(f"为{requirement}生成代码框架")
                print(f"\n💻 代码框架:\n{code_response}")
            
        except KeyboardInterrupt:
            break
        except Exception as e:
            print(f"❌ 错误: {e}")
    
    print("👋 系统设计结束")

async def comprehensive_design_example():
    """综合设计示例"""
    print("\n🌟 综合系统设计示例")
    print("项目：智能电机驱动器")
    print("-" * 40)
    
    system = DocumentIntelligenceSystem()
    await system.initialize()
    
    project_phases = [
        {
            "phase": "需求分析",
            "task": "分析智能电机驱动器的功能需求和性能指标"
        },
        {
            "phase": "系统架构",
            "task": "设计智能电机驱动器的整体架构"
        },
        {
            "phase": "硬件设计",
            "task": "设计电机驱动器的硬件电路"
        },
        {
            "phase": "软件架构",
            "task": "设计电机控制软件的架构和算法"
        },
        {
            "phase": "通信接口",
            "task": "设计电机驱动器的通信接口和协议"
        },
        {
            "phase": "保护机制",
            "task": "设计电机驱动器的保护和故障处理机制"
        }
    ]
    
    print("📋 项目阶段:")
    for i, phase in enumerate(project_phases, 1):
        print(f"  {i}. {phase['phase']}: {phase['task']}")
    
    print("\n🚀 开始综合设计...")
    
    for i, phase in enumerate(project_phases, 1):
        print(f"\n📦 阶段 {i}: {phase['phase']}")
        print(f"📝 任务: {phase['task']}")
        print("-" * 30)
        
        try:
            if "架构" in phase['phase'] or "设计" in phase['phase']:
                response = await system.design_system(phase['task'])
            else:
                response = await system.analyze_technical(phase['task'])
            
            print(response)
            
        except Exception as e:
            print(f"❌ {phase['phase']}失败: {e}")
        
        # 短暂延时
        await asyncio.sleep(0.5)
    
    print("\n✅ 综合设计完成！")
    
    # 生成项目总结
    print("\n📊 项目总结:")
    summary_request = "总结智能电机驱动器项目的关键技术点和实施建议"
    summary = await system.analyze_technical(summary_request)
    print(summary)

async def main():
    """主函数"""
    print("🚀 LKS32MC08x系统设计工具")
    print("选择模式:")
    print("1. 演示模式 - 预设设计需求")
    print("2. 交互模式 - 自由描述需求")
    print("3. 综合模式 - 完整项目设计")
    
    try:
        choice = input("请选择 (1/2/3): ").strip()
        
        if choice == "1":
            await system_design_demo()
        elif choice == "2":
            await interactive_design()
        elif choice == "3":
            await comprehensive_design_example()
        else:
            print("无效选择，运行演示模式")
            await system_design_demo()
            
    except KeyboardInterrupt:
        print("\n👋 再见！")

if __name__ == "__main__":
    asyncio.run(main())
