"""
模型配置文件
配置不同智能体使用的模型和参数
"""
import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))))

from LM_Studio import get_lmstudio_client
from autogen_core.models import ModelInfo, ModelFamily

class ModelConfig:
    """模型配置类"""

    # 默认模型配置
    DEFAULT_MODEL = "qwen3-32b-mlx"

    # 不同任务的模型配置
    TASK_MODELS = {
        "document_analysis": {
            "model": "qwen3-32b-mlx",
            "temperature": 0.3,  # 文档分析需要更准确
            "max_tokens": 4096
        },
        "code_generation": {
            "model": "qwen3-32b-mlx",
            "temperature": 0.1,  # 代码生成需要更精确
            "max_tokens": 2048
        },
        "technical_qa": {
            "model": "qwen3-32b-mlx",
            "temperature": 0.5,  # 技术问答需要平衡准确性和创造性
            "max_tokens": 3072
        },
        "system_design": {
            "model": "qwen3-32b-mlx",
            "temperature": 0.7,  # 系统设计需要更多创造性
            "max_tokens": 4096
        },
        "coordination": {
            "model": "qwen3-32b-mlx",
            "temperature": 0.4,  # 协调任务需要平衡
            "max_tokens": 2048
        },
        "document_reading": {
            "model": "qwen3-32b-mlx",
            "temperature": 0.2,  # 文档理解需要准确性
            "max_tokens": 3072
        },
        "knowledge_graph": {
            "model": "qwen3-32b-mlx",
            "temperature": 0.1,  # 知识图谱构建需要高精度
            "max_tokens": 4096
        },
        "intelligent_query": {
            "model": "qwen3-32b-mlx",
            "temperature": 0.3,  # 智能分析需要一定创造性
            "max_tokens": 6144
        }
    }

    @classmethod
    def get_model_client(cls, task_type: str = "default"):
        """获取指定任务类型的模型客户端"""
        if task_type == "default" or task_type not in cls.TASK_MODELS:
            return get_lmstudio_client(cls.DEFAULT_MODEL)

        config = cls.TASK_MODELS[task_type]
        client = get_lmstudio_client(config["model"])

        # 更新默认参数
        client.default_params.update({
            "temperature": config["temperature"],
            "max_tokens": config["max_tokens"]
        })

        return client

    @classmethod
    def get_all_clients(cls):
        """获取所有任务类型的模型客户端"""
        clients = {}
        for task_type in cls.TASK_MODELS.keys():
            clients[task_type] = cls.get_model_client(task_type)
        return clients

# 智能体模型映射
AGENT_MODEL_MAPPING = {
    "DocumentManager": "document_analysis",
    "TechnicalExpert": "technical_qa",
    "CodeEngineer": "code_generation",
    "SystemArchitect": "system_design",
    "Coordinator": "coordination",
    "DocumentReader": "document_reading",
    "KnowledgeGraphBuilder": "knowledge_graph",
    "IntelligentKGQuery": "intelligent_query"
}

def get_agent_model_client(agent_name: str):
    """根据智能体名称获取对应的模型客户端"""
    task_type = AGENT_MODEL_MAPPING.get(agent_name, "default")
    return ModelConfig.get_model_client(task_type)

# 模型能力配置
MODEL_CAPABILITIES = {
    "qwen3-32b-mlx": {
        "max_context_length": 32768,
        "supports_function_calling": True,
        "supports_vision": False,
        "supports_code_generation": True,
        "supports_chinese": True,
        "supports_technical_docs": True
    }
}

def get_model_capabilities(model_name: str):
    """获取模型能力信息"""
    return MODEL_CAPABILITIES.get(model_name, {})

# 性能优化配置
PERFORMANCE_CONFIG = {
    "batch_size": 1,  # LM Studio通常不支持批处理
    "concurrent_requests": 3,  # 并发请求数
    "timeout": 60,  # 请求超时时间（秒）
    "retry_attempts": 3,  # 重试次数
    "cache_enabled": True,  # 启用缓存
    "cache_ttl": 3600  # 缓存生存时间（秒）
}

# 安全配置
SAFETY_CONFIG = {
    "content_filter": True,  # 内容过滤
    "max_output_length": 8192,  # 最大输出长度
    "rate_limit": {
        "requests_per_minute": 60,
        "tokens_per_minute": 100000
    }
}

# 调试配置
DEBUG_CONFIG = {
    "log_requests": True,  # 记录请求
    "log_responses": True,  # 记录响应
    "save_conversations": True,  # 保存对话
    "verbose": False  # 详细输出
}
