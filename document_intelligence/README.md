# LKS32MC08x 智能文档理解系统

基于AutoGen的多智能体文档理解系统，专门用于理解和处理LKS32MC08x微控制器用户手册。

## 🎯 系统特性

### 核心功能
- **智能文档解析**：深度理解微控制器手册内容
- **多智能体协作**：专业化智能体分工合作
- **智能问答**：回答技术问题和提供开发指导
- **代码生成**：自动生成配置代码和示例
- **技术支持**：故障排除和优化建议

### 智能体架构
1. **文档管理员 (DocumentManager)**：文档解析、索引、检索
2. **技术专家 (TechnicalExpert)**：深度技术分析和解答
3. **代码工程师 (CodeEngineer)**：代码生成和优化
4. **系统架构师 (SystemArchitect)**：系统设计和集成方案
5. **协调员 (Coordinator)**：任务分配和结果整合

## 🚀 快速开始

### 环境要求
- Python 3.8+
- AutoGen 0.6.1+
- LM Studio (qwen3-32b-mlx模型)

### 安装依赖
```bash
conda activate autogen
cd document_intelligence
pip install -r requirements.txt
```

### 运行示例
```bash
# 启动智能文档助手
python main.py

# 运行特定场景测试
python examples/technical_qa.py
python examples/code_generation.py
python examples/system_design.py
```

## 📁 项目结构

```
document_intelligence/
├── README.md                 # 项目说明
├── requirements.txt          # 依赖包
├── main.py                  # 主程序入口
├── config/
│   ├── agents_config.py     # 智能体配置
│   └── models_config.py     # 模型配置
├── agents/
│   ├── __init__.py
│   ├── document_manager.py  # 文档管理员
│   ├── technical_expert.py  # 技术专家
│   ├── code_engineer.py     # 代码工程师
│   ├── system_architect.py  # 系统架构师
│   └── coordinator.py       # 协调员
├── tools/
│   ├── __init__.py
│   ├── document_parser.py   # 文档解析工具
│   ├── code_generator.py    # 代码生成工具
│   └── knowledge_base.py    # 知识库工具
├── examples/
│   ├── technical_qa.py      # 技术问答示例
│   ├── code_generation.py   # 代码生成示例
│   └── system_design.py     # 系统设计示例
├── data/
│   ├── full.md              # 原始文档
│   ├── parsed/              # 解析后的数据
│   └── knowledge_base/      # 知识库
└── tests/
    ├── test_agents.py       # 智能体测试
    └── test_tools.py        # 工具测试
```

## 🔧 使用场景

### 1. 技术问答
```python
# 询问ADC配置
response = await system.ask("如何配置ADC进行多通道采样？")

# 询问寄存器功能
response = await system.ask("SYS_AFE_REG1寄存器的作用是什么？")
```

### 2. 代码生成
```python
# 生成GPIO配置代码
code = await system.generate_code("配置PA0为输出，PA1为输入")

# 生成UART初始化代码
code = await system.generate_code("初始化UART0，波特率115200")
```

### 3. 系统设计
```python
# 设计电机控制系统
design = await system.design_system("三相无刷电机控制系统")

# 设计ADC采样系统
design = await system.design_system("多通道ADC数据采集系统")
```

## 📊 性能特点

- **高精度理解**：基于qwen3-32b-mlx模型的深度理解
- **快速响应**：本地模型，无网络延迟
- **专业分工**：多智能体协作，专业化处理
- **可扩展性**：模块化设计，易于扩展新功能

## 🛠️ 开发指南

### 添加新智能体
1. 在`agents/`目录创建新的智能体文件
2. 继承`BaseAgent`类
3. 实现必要的方法
4. 在配置文件中注册

### 添加新工具
1. 在`tools/`目录创建工具文件
2. 实现工具接口
3. 在智能体中注册工具

### 自定义配置
修改`config/`目录下的配置文件来自定义系统行为。

## 📝 许可证

本项目基于MIT许可证开源。
