#!/usr/bin/env python3
"""
快速知识图谱测试
"""
import asyncio
import sys
import os
import json

sys.path.append(os.path.dirname(os.path.abspath(__file__)))
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from config.models_config import get_agent_model_client
from config.agents_config import AGENT_SYSTEM_PROMPTS, AGENT_CAPABILITIES
from agents.knowledge_graph_builder import KnowledgeGraphBuilder

async def quick_test():
    """快速测试知识图谱功能"""
    print("⚡ 快速知识图谱测试")
    print("=" * 40)
    
    # 初始化智能体
    print("🔧 初始化智能体...")
    model_client = get_agent_model_client("KnowledgeGraphBuilder")
    system_message = AGENT_SYSTEM_PROMPTS["KnowledgeGraphBuilder"]
    capabilities = AGENT_CAPABILITIES["KnowledgeGraphBuilder"]
    
    kg_builder = KnowledgeGraphBuilder(model_client, system_message, capabilities)
    print("✅ 初始化完成")
    
    # 创建测试数据
    print("\n📄 创建测试数据...")
    test_content = create_test_content()
    
    # 手动添加一些实体和关系进行测试
    print("🔨 手动构建测试图谱...")
    
    # 添加测试实体
    kg_builder._add_entity("register_sys_afe_reg1", "SYS_AFE_REG1", "register", {
        "description": "模拟配置寄存器1",
        "page": 23
    })
    
    kg_builder._add_entity("module_adc", "ADC", "module", {
        "description": "模数转换器模块",
        "page": 11
    })
    
    kg_builder._add_entity("function_conversion", "模数转换", "function", {
        "description": "将模拟信号转换为数字信号",
        "page": 11
    })
    
    kg_builder._add_entity("parameter_resolution", "分辨率", "parameter", {
        "description": "ADC转换精度",
        "page": 11
    })
    
    kg_builder._add_entity("address_0x40010000", "0x40010000", "address", {
        "description": "ADC基地址",
        "page": 11
    })
    
    # 添加测试关系
    kg_builder._add_relation("register_sys_afe_reg1", "module_adc", "configures", {
        "description": "寄存器配置ADC模块"
    })
    
    kg_builder._add_relation("module_adc", "function_conversion", "implements", {
        "description": "ADC模块实现模数转换功能"
    })
    
    kg_builder._add_relation("function_conversion", "parameter_resolution", "has_parameter", {
        "description": "转换功能具有分辨率参数"
    })
    
    kg_builder._add_relation("module_adc", "address_0x40010000", "located_at", {
        "description": "ADC模块位于指定地址"
    })
    
    print(f"✅ 测试图谱构建完成")
    print(f"   • 实体数: {kg_builder.knowledge_graph.number_of_nodes()}")
    print(f"   • 关系数: {kg_builder.knowledge_graph.number_of_edges()}")
    
    # 测试分析功能
    print("\n🔍 测试图谱分析...")
    analysis = await kg_builder._analyze_knowledge_graph()
    print("📊 分析结果:")
    print(analysis[:500] + "..." if len(analysis) > 500 else analysis)
    
    # 测试导出功能
    print("\n💾 测试导出功能...")
    
    # 导出JSON
    json_result = await kg_builder._export_knowledge_graph('json')
    print(f"📄 JSON导出: {json_result}")
    
    # 导出HTML
    html_result = await kg_builder._export_knowledge_graph('html')
    print(f"🌐 HTML导出: {html_result}")
    
    # 导出PNG
    png_result = await kg_builder._export_knowledge_graph('png')
    print(f"🖼️  PNG导出: {png_result}")
    
    # 检查输出文件
    print("\n📁 检查输出文件...")
    output_dir = kg_builder.output_dir
    if os.path.exists(output_dir):
        files = os.listdir(output_dir)
        print(f"输出目录: {output_dir}")
        for file in files:
            file_path = os.path.join(output_dir, file)
            file_size = os.path.getsize(file_path)
            print(f"  • {file}: {file_size:,} 字节")
    
    print("\n🎉 快速测试完成！")
    return kg_builder

def create_test_content():
    """创建测试用的content_list数据"""
    test_content = [
        {
            "type": "text",
            "text": "5 系统控制及时钟复位",
            "text_level": 1,
            "page_idx": 15
        },
        {
            "type": "text", 
            "text": "5.3.5 SYS_AFE_REG1 模拟配置寄存器1",
            "text_level": 2,
            "page_idx": 23
        },
        {
            "type": "text",
            "text": "SYS_AFE_REG1寄存器用于配置ADC模块的基本参数，包括采样频率、分辨率等。该寄存器位于地址0x40010000。",
            "page_idx": 23
        },
        {
            "type": "text",
            "text": "4.5 ADC 模数转换器",
            "text_level": 2,
            "page_idx": 11
        },
        {
            "type": "text",
            "text": "ADC模块提供12位分辨率的模数转换功能，支持多通道采样。转换速度可达1MSPS。",
            "page_idx": 11
        }
    ]
    return test_content

async def test_ai_extraction():
    """测试AI实体关系提取"""
    print("\n🤖 测试AI实体关系提取")
    print("-" * 40)
    
    # 初始化智能体
    model_client = get_agent_model_client("KnowledgeGraphBuilder")
    system_message = AGENT_SYSTEM_PROMPTS["KnowledgeGraphBuilder"]
    capabilities = AGENT_CAPABILITIES["KnowledgeGraphBuilder"]
    
    kg_builder = KnowledgeGraphBuilder(model_client, system_message, capabilities)
    
    # 测试文本
    test_text = """
    SYS_AFE_REG1 模拟配置寄存器1
    
    该寄存器位于地址0x40010000，用于配置ADC模块的基本参数。
    ADC模块提供12位分辨率的模数转换功能，支持8个输入通道。
    转换速度可达1MSPS，具有DMA传输支持。
    
    寄存器位域定义：
    - bit[7:0]: ADC_EN，ADC使能控制
    - bit[15:8]: SAMPLE_RATE，采样率配置
    - bit[23:16]: RESOLUTION，分辨率设置
    """
    
    print(f"📝 测试文本:\n{test_text}")
    print("\n🔍 开始AI提取...")
    
    try:
        extracted = await kg_builder._extract_entities_and_relations(test_text, 23)
        
        print(f"\n📊 提取结果:")
        print(f"  • 实体数: {len(extracted['entities'])}")
        print(f"  • 关系数: {len(extracted['relations'])}")
        
        print(f"\n🏷️  提取的实体:")
        for entity in extracted['entities']:
            print(f"  • {entity['name']} ({entity['type']}): {entity.get('description', '')}")
        
        print(f"\n🔗 提取的关系:")
        for relation in extracted['relations']:
            print(f"  • {relation['source']} --[{relation['type']}]--> {relation['target']}")
        
    except Exception as e:
        print(f"❌ AI提取失败: {e}")
        print("🔧 使用规则提取...")
        extracted = kg_builder._rule_based_extraction(test_text, 23)
        
        print(f"\n📊 规则提取结果:")
        print(f"  • 实体数: {len(extracted['entities'])}")
        print(f"  • 关系数: {len(extracted['relations'])}")
        
        print(f"\n🏷️  提取的实体:")
        for entity in extracted['entities']:
            print(f"  • {entity['name']} ({entity['type']})")

async def main():
    """主函数"""
    print("⚡ 知识图谱快速测试工具")
    print("选择测试:")
    print("1. 快速功能测试")
    print("2. AI提取测试")
    print("3. 全部测试")
    
    try:
        choice = input("请选择 (1/2/3): ").strip()
        
        if choice == "1":
            await quick_test()
        elif choice == "2":
            await test_ai_extraction()
        elif choice == "3":
            await quick_test()
            await test_ai_extraction()
        else:
            print("无效选择，运行快速测试")
            await quick_test()
            
    except KeyboardInterrupt:
        print("\n👋 再见！")

if __name__ == "__main__":
    asyncio.run(main())
