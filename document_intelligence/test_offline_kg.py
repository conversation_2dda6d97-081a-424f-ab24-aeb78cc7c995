#!/usr/bin/env python3
"""
测试离线知识图谱构建智能体
"""
import asyncio
import sys
import os
import time

sys.path.append(os.path.dirname(os.path.abspath(__file__)))
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from config.models_config import get_agent_model_client
from config.agents_config import AGENT_SYSTEM_PROMPTS, AGENT_CAPABILITIES
from agents.offline_kg_builder import OfflineKGBuilder

async def test_offline_kg_performance():
    """测试离线知识图谱性能"""
    print("🚀 离线知识图谱性能测试")
    print("=" * 50)
    
    # 初始化智能体
    print("🔧 初始化离线知识图谱构建智能体...")
    try:
        model_client = get_agent_model_client("KnowledgeGraphBuilder")
        system_message = AGENT_SYSTEM_PROMPTS["KnowledgeGraphBuilder"]
        capabilities = AGENT_CAPABILITIES["KnowledgeGraphBuilder"]
        
        offline_kg = OfflineKGBuilder(model_client, system_message, capabilities)
        print("✅ 初始化完成")
        
    except Exception as e:
        print(f"❌ 初始化失败: {e}")
        return
    
    # 性能测试用例
    test_cases = [
        {
            "category": "构建知识图谱",
            "request": "构建知识图谱",
            "description": "测试离线构建性能"
        },
        {
            "category": "图谱分析",
            "request": "分析知识图谱",
            "description": "测试分析功能"
        },
        {
            "category": "实体查询",
            "request": "查询实体 ADC",
            "description": "测试离线查询功能"
        },
        {
            "category": "导出HTML",
            "request": "导出知识图谱 html",
            "description": "测试HTML导出"
        },
        {
            "category": "导出JSON",
            "request": "导出知识图谱 json",
            "description": "测试JSON导出"
        }
    ]
    
    print(f"\n🧪 开始性能测试 ({len(test_cases)} 个用例):")
    
    total_start_time = time.time()
    
    for i, test_case in enumerate(test_cases, 1):
        print(f"\n📝 测试 {i}: {test_case['category']}")
        print(f"📋 请求: {test_case['request']}")
        print(f"📄 描述: {test_case['description']}")
        print("-" * 60)
        
        try:
            start_time = time.time()
            response = await offline_kg.handle_message(test_case['request'])
            end_time = time.time()
            
            print(f"🚀 处理结果:")
            # 显示结果摘要
            lines = response.split('\n')
            summary_lines = []
            for line in lines:
                if any(keyword in line for keyword in ['✅', '📊', '节点数', '边数', '处理时间', '性能', '导出', '找到']):
                    summary_lines.append(line)
            
            if summary_lines:
                for line in summary_lines[:8]:  # 显示前8行关键信息
                    print(f"  {line}")
            else:
                print(f"  {response[:300]}...")
            
            print(f"\n⏱️  处理时间: {end_time - start_time:.2f}秒")
            
            # 性能指标
            if i == 1:  # 第一个测试记录基准性能
                if "节点数" in response:
                    import re
                    nodes_match = re.search(r'节点数.*?(\d+)', response)
                    if nodes_match:
                        nodes = int(nodes_match.group(1))
                        processing_speed = nodes / (end_time - start_time) if (end_time - start_time) > 0 else 0
                        print(f"📈 处理速度: {processing_speed:.1f} 节点/秒")
            
        except Exception as e:
            print(f"❌ 测试失败: {e}")
        
        print("\n" + "=" * 60)
        
        # 添加延时
        await asyncio.sleep(0.5)
    
    total_time = time.time() - total_start_time
    print(f"\n🎉 性能测试完成！总耗时: {total_time:.2f}秒")

async def test_database_operations():
    """测试数据库操作"""
    print("\n💾 数据库操作测试")
    print("-" * 40)
    
    # 初始化
    model_client = get_agent_model_client("KnowledgeGraphBuilder")
    system_message = AGENT_SYSTEM_PROMPTS["KnowledgeGraphBuilder"]
    capabilities = AGENT_CAPABILITIES["KnowledgeGraphBuilder"]
    offline_kg = OfflineKGBuilder(model_client, system_message, capabilities)
    
    # 测试数据库操作
    operations = [
        ("清空图谱", "清空图谱"),
        ("构建小规模图谱", "构建知识图谱"),
        ("查询寄存器", "查询实体 寄存器"),
        ("查询模块", "查询实体 模块"),
        ("分析图谱", "分析知识图谱"),
        ("导出SQL", "导出知识图谱 sql")
    ]
    
    for operation_name, request in operations:
        print(f"\n🔧 {operation_name}...")
        try:
            start_time = time.time()
            result = await offline_kg.handle_message(request)
            end_time = time.time()
            
            # 提取关键信息
            lines = result.split('\n')
            key_info = []
            for line in lines:
                if any(keyword in line for keyword in ['✅', '❌', '📊', '找到', '节点', '边', '导出']):
                    key_info.append(line.strip())
            
            if key_info:
                print(f"  📊 结果: {key_info[0]}")
                if len(key_info) > 1:
                    print(f"  📈 详情: {key_info[1]}")
            else:
                print(f"  📊 结果: {result[:100]}...")
            
            print(f"  ⏱️  耗时: {end_time - start_time:.3f}秒")
            
        except Exception as e:
            print(f"  ❌ 操作失败: {e}")

async def test_query_performance():
    """测试查询性能"""
    print("\n🔍 查询性能测试")
    print("-" * 40)
    
    # 初始化
    model_client = get_agent_model_client("KnowledgeGraphBuilder")
    system_message = AGENT_SYSTEM_PROMPTS["KnowledgeGraphBuilder"]
    capabilities = AGENT_CAPABILITIES["KnowledgeGraphBuilder"]
    offline_kg = OfflineKGBuilder(model_client, system_message, capabilities)
    
    # 先构建图谱
    print("📊 构建测试图谱...")
    await offline_kg.handle_message("构建知识图谱")
    
    # 测试不同类型的查询
    queries = [
        ("精确查询", "ADC"),
        ("模糊查询", "寄存器"),
        ("类型查询", "module"),
        ("中文查询", "模数转换"),
        ("地址查询", "0x"),
        ("不存在查询", "xyz123")
    ]
    
    print(f"\n🔍 测试 {len(queries)} 种查询类型:")
    
    total_query_time = 0
    successful_queries = 0
    
    for query_type, query in queries:
        print(f"\n🔎 {query_type}: '{query}'")
        try:
            start_time = time.time()
            result = await offline_kg.handle_message(f"查询实体 {query}")
            end_time = time.time()
            
            query_time = end_time - start_time
            total_query_time += query_time
            
            # 统计结果
            if "找到" in result:
                import re
                match = re.search(r'找到\s+(\d+)\s+个', result)
                if match:
                    count = int(match.group(1))
                    print(f"  📊 找到 {count} 个实体")
                    successful_queries += 1
                else:
                    print(f"  📊 查询成功")
                    successful_queries += 1
            else:
                print(f"  📊 未找到结果")
            
            print(f"  ⏱️  查询时间: {query_time:.3f}秒")
            
        except Exception as e:
            print(f"  ❌ 查询失败: {e}")
    
    # 查询性能总结
    avg_query_time = total_query_time / len(queries) if queries else 0
    success_rate = successful_queries / len(queries) * 100 if queries else 0
    
    print(f"\n📈 查询性能总结:")
    print(f"  • 平均查询时间: {avg_query_time:.3f}秒")
    print(f"  • 成功率: {success_rate:.1f}%")
    print(f"  • 查询吞吐量: {1/avg_query_time:.1f} 查询/秒" if avg_query_time > 0 else "  • 查询吞吐量: N/A")

async def interactive_offline_kg():
    """交互式离线知识图谱操作"""
    print("\n🎯 交互式离线知识图谱操作")
    print("支持的操作:")
    print("- 构建知识图谱")
    print("- 分析知识图谱")
    print("- 查询实体 <内容>")
    print("- 导出知识图谱 [格式]")
    print("- 清空图谱")
    print("输入 'quit' 退出")
    print("-" * 50)
    
    # 初始化
    try:
        model_client = get_agent_model_client("KnowledgeGraphBuilder")
        system_message = AGENT_SYSTEM_PROMPTS["KnowledgeGraphBuilder"]
        capabilities = AGENT_CAPABILITIES["KnowledgeGraphBuilder"]
        offline_kg = OfflineKGBuilder(model_client, system_message, capabilities)
    except Exception as e:
        print(f"❌ 初始化失败: {e}")
        return
    
    while True:
        try:
            request = input("\n🚀 请输入操作: ").strip()
            
            if request.lower() in ['quit', 'exit', 'q']:
                break
            
            if not request:
                continue
            
            print("⚡ 正在处理...")
            start_time = time.time()
            response = await offline_kg.handle_message(request)
            end_time = time.time()
            
            print(f"\n📋 处理结果:\n{response}")
            print(f"\n⏱️  处理时间: {end_time - start_time:.2f}秒")
            
        except KeyboardInterrupt:
            break
        except Exception as e:
            print(f"❌ 错误: {e}")
    
    print("👋 离线知识图谱测试结束")

async def main():
    """主函数"""
    print("🚀 离线知识图谱构建智能体测试工具")
    print("选择测试模式:")
    print("1. 性能测试")
    print("2. 数据库操作测试")
    print("3. 查询性能测试")
    print("4. 交互式测试")
    print("5. 全部测试")
    
    try:
        choice = input("请选择 (1/2/3/4/5): ").strip()
        
        if choice == "1":
            await test_offline_kg_performance()
        elif choice == "2":
            await test_database_operations()
        elif choice == "3":
            await test_query_performance()
        elif choice == "4":
            await interactive_offline_kg()
        elif choice == "5":
            await test_offline_kg_performance()
            await test_database_operations()
            await test_query_performance()
        else:
            print("无效选择，运行性能测试")
            await test_offline_kg_performance()
            
    except KeyboardInterrupt:
        print("\n👋 再见！")

if __name__ == "__main__":
    asyncio.run(main())
