# 🧠 知识图谱构建智能体使用指南

## 🚀 功能概述

知识图谱构建智能体可以将Mineru输出的技术文档转换为结构化的知识图谱，支持实体识别、关系建模、图谱分析和多格式可视化。

## ✨ 核心特性

### 🔍 智能实体识别
- **寄存器**: SYS_AFE_REG1、GPIO_CFG等
- **模块**: ADC、UART、PWM、DMA等
- **功能**: 模数转换、时钟控制、数据传输等
- **参数**: 波特率、分辨率、频率等
- **地址**: 0x40010000等内存地址
- **位域**: bit[7:0]等寄存器位定义

### 🔗 语义关系建模
- **包含关系**: 模块包含寄存器
- **控制关系**: 寄存器控制功能
- **配置关系**: 参数配置模块
- **依赖关系**: 功能依赖硬件
- **位置关系**: 模块位于地址

### 📊 图谱分析
- **统计分析**: 实体数量、关系分布、图谱密度
- **中心性分析**: 度中心性、介数中心性
- **社区检测**: 功能模块聚类
- **连通性分析**: 图谱结构完整性

### 🎨 多格式导出
- **交互式HTML**: 可缩放、可点击的网络图
- **JSON数据**: 结构化数据格式
- **PNG图片**: 高分辨率静态图像
- **GEXF格式**: Gephi可读的图谱文件

## 🛠️ 安装依赖

```bash
conda activate autogen
pip install networkx matplotlib pyvis rdflib
```

## 📖 使用方法

### 方式1: 命令行使用

```bash
cd /Users/<USER>/Public/AutoGen/document_intelligence

# 快速测试
python quick_kg_test.py

# 完整测试
python test_knowledge_graph.py
```

### 方式2: 代码中调用

```python
import asyncio
from config.models_config import get_agent_model_client
from config.agents_config import AGENT_SYSTEM_PROMPTS, AGENT_CAPABILITIES
from agents.knowledge_graph_builder import KnowledgeGraphBuilder

async def build_knowledge_graph():
    # 初始化智能体
    model_client = get_agent_model_client("KnowledgeGraphBuilder")
    system_message = AGENT_SYSTEM_PROMPTS["KnowledgeGraphBuilder"]
    capabilities = AGENT_CAPABILITIES["KnowledgeGraphBuilder"]
    
    kg_builder = KnowledgeGraphBuilder(model_client, system_message, capabilities)
    
    # 构建知识图谱
    result = await kg_builder.handle_message(
        "构建知识图谱 路径: /Users/<USER>/Public/AutoGen/monkey_ocr_project/mineru_output"
    )
    print(result)
    
    # 分析图谱
    analysis = await kg_builder.handle_message("分析知识图谱")
    print(analysis)
    
    # 导出可视化
    export_result = await kg_builder.handle_message("导出知识图谱 html")
    print(export_result)

asyncio.run(build_knowledge_graph())
```

### 方式3: 集成到主系统

```python
from main import DocumentIntelligenceSystem

async def main():
    system = DocumentIntelligenceSystem()
    
    # 构建知识图谱
    result = await system.ask("构建知识图谱")
    print(result)

asyncio.run(main())
```

## 🎯 支持的命令

### 构建知识图谱
```
构建知识图谱 [路径: /path/to/mineru/output]
build knowledge graph [path: /path/to/mineru/output]
```

### 分析知识图谱
```
分析知识图谱
analyze knowledge graph
```

### 导出知识图谱
```
导出知识图谱 html
导出知识图谱 json
导出知识图谱 png
导出知识图谱 gexf
```

## 📊 测试结果示例

### ✅ 成功案例

```
⚡ 快速知识图谱测试
========================================
🔧 初始化智能体...
✅ 初始化完成

🔨 手动构建测试图谱...
✅ 测试图谱构建完成
   • 实体数: 5
   • 关系数: 4

📊 分析结果:
# 🔍 知识图谱分析报告

## 📊 基本统计
- **节点数**: 5
- **边数**: 4
- **密度**: 0.2000

### 实体类型分布
- register: 1个
- module: 1个
- function: 1个
- parameter: 1个
- address: 1个

### 关系类型分布
- configures: 1个
- implements: 1个
- located_at: 1个
- has_parameter: 1个

### 中心性分析
- ADC: 0.750 (度中心性最高)
- 模数转换: 0.500
- SYS_AFE_REG1: 0.250

💾 导出结果:
✅ JSON格式图谱已导出: knowledge_graphs/knowledge_graph.json
✅ 交互式HTML图谱已导出: knowledge_graphs/knowledge_graph.html
✅ PNG图片已导出: knowledge_graphs/knowledge_graph.png
```

### 🤖 AI实体提取示例

```
📝 测试文本:
SYS_AFE_REG1 模拟配置寄存器1
该寄存器位于地址0x40010000，用于配置ADC模块的基本参数。

📊 提取结果:
  • 实体数: 10
  • 关系数: 6

🏷️  提取的实体:
  • SYS_AFE_REG1 (寄存器名称): 模拟配置寄存器1
  • ADC (模块名称): 提供12位分辨率的模数转换功能
  • 模数转换 (功能描述): ADC模块的核心功能
  • 0x40010000 (地址值): 寄存器物理地址
  • bit[7:0] (位域): ADC使能控制位

🔗 提取的关系:
  • register_SYS_AFE_REG1 --[配置关系]--> module_ADC
  • module_ADC --[实现关系]--> function_模数转换
  • register_SYS_AFE_REG1 --[位于关系]--> address_0x40010000
```

## 📁 输出文件

知识图谱构建完成后，会在 `knowledge_graphs/` 目录下生成以下文件：

```
knowledge_graphs/
├── knowledge_graph.html    # 交互式可视化 (5.5KB)
├── knowledge_graph.json    # 结构化数据 (1.6KB)
├── knowledge_graph.png     # 静态图片 (270KB)
└── knowledge_graph.gexf    # Gephi格式 (可选)
```

### 📄 JSON数据结构

```json
{
  "nodes": [
    {
      "id": "register_sys_afe_reg1",
      "name": "SYS_AFE_REG1",
      "type": "register",
      "description": "模拟配置寄存器1",
      "page": 23
    }
  ],
  "edges": [
    {
      "source": "register_sys_afe_reg1",
      "target": "module_adc",
      "type": "configures",
      "description": "寄存器配置ADC模块"
    }
  ]
}
```

## 🔧 配置选项

### 实体类型定义
```python
entity_types = {
    'register': '寄存器',
    'module': '模块',
    'function': '功能',
    'parameter': '参数',
    'address': '地址',
    'bit_field': '位域',
    'chapter': '章节',
    'concept': '概念'
}
```

### 关系类型定义
```python
relation_types = {
    'contains': '包含',
    'belongs_to': '属于',
    'controls': '控制',
    'configures': '配置',
    'implements': '实现',
    'references': '引用',
    'depends_on': '依赖于',
    'related_to': '相关于'
}
```

## ⚠️ 注意事项

### 1. 文件大小限制
- 大型Mineru输出文件(>1MB)处理时间较长
- 建议先用小文件测试功能

### 2. 中文字体问题
- PNG导出可能出现中文字体警告
- 不影响功能，可忽略警告信息

### 3. 模型依赖
- AI实体提取依赖LM Studio模型
- 如果AI提取失败，会自动降级到规则提取

### 4. 内存使用
- 大型图谱可能占用较多内存
- 建议分批处理大文件

## 🚀 扩展功能

### 1. 自定义实体类型
```python
kg_builder.entity_types['custom_type'] = '自定义类型'
```

### 2. 自定义关系类型
```python
kg_builder.relation_types['custom_relation'] = '自定义关系'
```

### 3. 批量处理
```python
for mineru_path in mineru_paths:
    await kg_builder.handle_message(f"构建知识图谱 路径: {mineru_path}")
```

## 📞 故障排除

### 常见问题

1. **初始化失败**
   ```
   ❌ 初始化失败: 模型客户端连接失败
   ```
   解决: 检查LM Studio是否运行，模型是否加载

2. **文件路径错误**
   ```
   ❌ Mineru输出路径不存在
   ```
   解决: 确认Mineru输出路径正确

3. **导出失败**
   ```
   ❌ 导出失败: 权限不足
   ```
   解决: 检查输出目录权限

4. **AI提取失败**
   ```
   ⚠️ AI提取失败，使用规则提取
   ```
   解决: 正常现象，会自动降级到规则提取

## 🎉 成功指标

- ✅ 智能体初始化成功
- ✅ 实体识别准确率 > 80%
- ✅ 关系建模合理性
- ✅ 图谱结构完整性
- ✅ 可视化文件生成成功
- ✅ 分析报告详细完整

这个知识图谱构建智能体为您的Mineru输出提供了完整的知识图谱解决方案！🧠✨
