# 🚀 离线知识图谱智能体使用指南

## 📖 快速开始

### 1. 基本使用

```bash
cd /Users/<USER>/Public/AutoGen/document_intelligence
conda activate autogen
python test_offline_kg.py
```

选择测试模式：
- **1. 性能测试** - 完整功能演示
- **2. 数据库操作测试** - 数据库功能测试
- **3. 查询性能测试** - 查询速度测试
- **4. 交互式测试** - 实时问答（推荐）
- **5. 全部测试** - 运行所有测试

### 2. 交互式使用（推荐）

选择 `4. 交互式测试`，然后可以使用以下命令：

#### 🔨 构建知识图谱
```
构建知识图谱
```
- 自动处理Mineru输出文件
- 提取实体和关系
- 存储到SQLite数据库

#### 🔍 查询实体
```
查询实体 ADC
查询实体 温度
查询实体 寄存器
查询实体 GPIO
```

#### 📊 分析图谱
```
分析知识图谱
```
- 显示统计信息
- 实体类型分布
- 质量指标

#### 💾 导出图谱
```
导出知识图谱 html
导出知识图谱 json
导出知识图谱 sql
```

## 🎯 实际使用示例

### 示例1: 查找ADC相关信息

**输入：**
```
查询实体 ADC
```

**输出：**
```
# 🔍 实体查询结果: 'ADC'

找到 18 个相关实体:

## 1. ADC
- **类型**: module
- **匹配方式**: 精确匹配
- **置信度**: 0.900
- **页面**: 1

## 2. ADC0
- **类型**: register
- **匹配方式**: 名称匹配
- **置信度**: 0.900
- **页面**: 26

## 3. ADCLKSEL
- **类型**: bit_field
- **匹配方式**: 名称匹配
- **置信度**: 0.900
- **页面**: 48
```

### 示例2: 查找温度传感器

**输入：**
```
查询实体 温度
```

**输出：**
```
找到 4 个相关实体:

## 1. 数模转换器
- **类型**: module
- **匹配方式**: 上下文匹配
- **置信度**: 0.900
- **页面**: 1
- **上下文**: TMP 温度传感器 . DAC 数模转换器
```

### 示例3: 分析图谱质量

**输入：**
```
分析知识图谱
```

**输出：**
```
## 📊 基本统计
- **节点数**: 239
- **边数**: 0
- **图谱密度**: 0.0000
- **页面覆盖**: 87页

## 🏷️ 实体类型分布
- **register**: 96个
- **address**: 41个
- **bit_field**: 35个
- **parameter**: 25个
- **function**: 22个
- **module**: 20个

## 📈 质量指标
- **实体置信度**: 平均 0.900
- **页面覆盖**: 87页
```

## 🎨 可视化查看

构建完成后，可以查看生成的可视化文件：

### HTML交互式图谱
```
open offline_knowledge_graphs/offline_knowledge_graph.html
```
- 可缩放、可拖拽的网络图
- 鼠标悬停显示详细信息
- 不同颜色代表不同实体类型

### JSON数据文件
```
cat offline_knowledge_graphs/offline_knowledge_graph.json
```
- 结构化数据格式
- 便于二次开发
- 包含完整的元数据

## 📊 性能指标

### ⚡ 处理速度
- **构建时间**: 1.12秒
- **处理文本**: 1937条
- **提取实体**: 239个
- **处理速度**: 173.6 实体/秒

### 🔍 查询性能
- **查询时间**: 0.001秒
- **查询吞吐量**: 1000 查询/秒
- **支持类型**: 精确匹配、模糊匹配、上下文匹配

### 💾 存储效率
- **数据库大小**: 84KB
- **存储效率**: 2.8 条目/KB
- **页面覆盖**: 87页

## 🔧 高级用法

### 1. 代码中调用

```python
import asyncio
from config.models_config import get_agent_model_client
from config.agents_config import AGENT_SYSTEM_PROMPTS, AGENT_CAPABILITIES
from agents.offline_kg_builder import OfflineKGBuilder

async def use_kg():
    # 初始化
    model_client = get_agent_model_client("KnowledgeGraphBuilder")
    system_message = AGENT_SYSTEM_PROMPTS["KnowledgeGraphBuilder"]
    capabilities = AGENT_CAPABILITIES["KnowledgeGraphBuilder"]
    
    kg = OfflineKGBuilder(model_client, system_message, capabilities)
    
    # 构建图谱
    result = await kg.handle_message("构建知识图谱")
    print(result)
    
    # 查询实体
    result = await kg.handle_message("查询实体 ADC")
    print(result)

asyncio.run(use_kg())
```

### 2. 自定义Mineru路径

```python
# 指定自定义路径
result = await kg.handle_message("构建知识图谱 路径: /your/custom/path")
```

### 3. 批量查询

```python
queries = ["ADC", "GPIO", "UART", "PWM", "DMA"]
for query in queries:
    result = await kg.handle_message(f"查询实体 {query}")
    print(f"查询 {query}: 找到实体数量")
```

## 🎯 支持的实体类型

- **register** - 寄存器 (如: ADC0, GPIO_CFG)
- **module** - 模块 (如: ADC, UART, PWM)
- **function** - 功能 (如: 模数转换, 数据传输)
- **parameter** - 参数 (如: 12MHz, 3.3V, 12bit)
- **address** - 地址 (如: 0x40010000)
- **bit_field** - 位域 (如: bit[7:0], ADCLKSEL[1:0])

## 🎨 查询技巧

### 1. 精确查询
```
查询实体 ADC          # 查找名为"ADC"的实体
```

### 2. 模糊查询
```
查询实体 寄存器        # 查找包含"寄存器"的实体
```

### 3. 类型查询
```
查询实体 module       # 查找所有模块类型实体
```

### 4. 上下文查询
```
查询实体 温度传感器    # 在上下文中查找相关实体
```

## 📁 输出文件说明

### 数据库文件
- `offline_kg.db` - SQLite数据库，包含所有实体和关系

### 可视化文件
- `offline_knowledge_graph.html` - 交互式网络图
- `offline_knowledge_graph.json` - JSON格式数据
- `knowledge_graph_backup.sql` - SQL备份文件

## 🔍 故障排除

### 常见问题

1. **初始化失败**
   ```
   ❌ 初始化失败: 模型客户端连接失败
   ```
   **解决**: 检查LM Studio是否运行

2. **文件路径错误**
   ```
   ❌ Mineru输出路径不存在
   ```
   **解决**: 确认路径 `/Users/<USER>/Public/AutoGen/monkey_ocr_project/mineru_output` 存在

3. **查询无结果**
   ```
   ❌ 未找到与'xxx'相关的实体
   ```
   **解决**: 先运行"构建知识图谱"，或尝试其他查询词

## 🎉 成功指标

- ✅ 构建时间 < 2秒
- ✅ 查询时间 < 0.01秒  
- ✅ 实体数量 > 200个
- ✅ 置信度 > 0.8
- ✅ 页面覆盖 > 80页

这个离线知识图谱智能体为您提供了高效、准确的技术文档知识提取和查询能力！🧠✨
