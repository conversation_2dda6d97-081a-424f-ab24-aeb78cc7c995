BEGIN TRANSACTION;
CREATE TABLE entities (
                id TEXT PRIMARY KEY,
                name TEXT NOT NULL,
                type TEXT NOT NULL,
                confidence REAL,
                page INTEGER,
                context TEXT,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            );
INSERT INTO "entities" VALUES('module_adc','ADC','module',0.9,1,'统 . 104.4 BGP 基准电压源 . . 104.5 ADC 模数转换器 . 114.6 OPA 运算放大器 . 114','2025-07-03 14:49:46');
INSERT INTO "entities" VALUES('module_模数转换器','模数转换器','module',0.9,1,'104.4 BGP 基准电压源 . . 104.5 ADC 模数转换器 . 114.6 OPA 运算放大器 . 114.7 CMP','2025-07-03 14:49:46');
INSERT INTO "entities" VALUES('module_数模转换器','数模转换器','module',0.9,1,'. 124.8 TMP 温度传感器 . 124.9 DAC 数模转换器 . 13  
5 系统控制及时钟复位 .. .15','2025-07-03 14:49:46');
INSERT INTO "entities" VALUES('module_定时器','定时器','module',0.9,1,'器 .3.3 异常和中断 . . 43.4 SYSTICK 定时器.... 53.4.1 SYST_CSR 控制和状态寄存器.','2025-07-03 14:49:46');
INSERT INTO "entities" VALUES('module_比较器','比较器','module',0.9,1,'. 114.6 OPA 运算放大器 . 114.7 CMP 比较器 . 124.8 TMP 温度传感器 . 124.9 DAC','2025-07-03 14:49:46');
INSERT INTO "entities" VALUES('parameter_电压','电压','parameter',0.9,1,'.4.3 CLOCK 时钟系统 . 104.4 BGP 基准电压源 . . 104.5 ADC 模数转换器 . 114.6 ','2025-07-03 14:49:46');
INSERT INTO "entities" VALUES('function_定时','定时','function',0.9,1,'器 .3.3 异常和中断 . . 43.4 SYSTICK 定时器.... 53.4.1 SYST_CSR 控制和状态寄存器','2025-07-03 14:49:46');
INSERT INTO "entities" VALUES('function_模数转换','模数转换','function',0.9,1,'104.4 BGP 基准电压源 . . 104.5 ADC 模数转换器 . 114.6 OPA 运算放大器 . 114.7 CM','2025-07-03 14:49:46');
INSERT INTO "entities" VALUES('function_放大','放大','function',0.9,1,'104.5 ADC 模数转换器 . 114.6 OPA 运算放大器 . 114.7 CMP 比较器 . 124.8 TMP ','2025-07-03 14:49:46');
INSERT INTO "entities" VALUES('function_比较','比较','function',0.9,1,'. 114.6 OPA 运算放大器 . 114.7 CMP 比较器 . 124.8 TMP 温度传感器 . 124.9 DA','2025-07-03 14:49:46');
INSERT INTO "entities" VALUES('function_数模转换','数模转换','function',0.9,1,'. 124.8 TMP 温度传感器 . 124.9 DAC 数模转换器 . 13  
5 系统控制及时钟复位 .. .15','2025-07-03 14:49:46');
INSERT INTO "entities" VALUES('function_控制','控制','function',0.9,1,'RM®CORTEXTM-M0 核心 ...3.2 NVIC 控制器 .3.3 异常和中断 . . 43.4 SYSTICK ','2025-07-03 14:49:46');
INSERT INTO "entities" VALUES('function_中断','中断','function',0.9,1,'M0 核心 ...3.2 NVIC 控制器 .3.3 异常和中断 . . 43.4 SYSTICK 定时器.... 53.4','2025-07-03 14:49:46');
INSERT INTO "entities" VALUES('function_复位','复位','function',0.9,1,'4.9 DAC 数模转换器 . 13  
5 系统控制及时钟复位 .. .15','2025-07-03 14:49:46');
INSERT INTO "entities" VALUES('function_保护','保护','function',0.9,2,'存 35  
5.3.23 SYS_WR_PROTECT 写保护寄存器. 35  
5.3.24 SYS_AFE_DAC_A','2025-07-03 14:49:46');
INSERT INTO "entities" VALUES('function_配置','配置','function',0.9,2,' ...22  
5.3.4 SYS_AFE_REG0 模拟配置寄存器0 .... 22  
5.3.5 SYS_AFE_R','2025-07-03 14:49:46');
INSERT INTO "entities" VALUES('module_flash','FLASH','module',0.9,3,'6 FLASH. .38','2025-07-03 14:49:46');
INSERT INTO "entities" VALUES('register_flash_cfg','FLASH_CFG','register',0.9,3,' . 45  
6.3.1 地址分配 45  
6.3.2 FLASH_CFG 配置寄存器（推荐先读回，按或/与方式修改） 45  
6.3.3 FL','2025-07-03 14:49:46');
INSERT INTO "entities" VALUES('register_dma','DMA','register',0.9,4,'1 地址分配 .. 56  
7.6.2 DMA_CTRL DMA 控制寄存器. 56  
7.6.3 DMA_IF DMA中断标志寄存器','2025-07-03 14:49:46');
INSERT INTO "entities" VALUES('module_dma','DMA','module',0.9,4,'1 地址分配 .. 56  
7.6.2 DMA_CTRL DMA 控制寄存器. 56  
7.6.3 DMA_IF DMA中','2025-07-03 14:49:46');
INSERT INTO "entities" VALUES('module_gpio','GPIO','module',0.9,4,'8 GPIO... .62','2025-07-03 14:49:46');
INSERT INTO "entities" VALUES('register_crc','CRC','register',0.9,6,'9.4.2.2 CRC_CR CRC 控制寄存器 83','2025-07-03 14:49:46');
INSERT INTO "entities" VALUES('parameter_11','11','parameter',0.9,6,'.2.10.4 ADC0_AMC_B1 17  
10.2.11 通道0阈值寄存器 . 117  
10.2.11.1 ADC0_','2025-07-03 14:49:46');
INSERT INTO "entities" VALUES('register_timer0','Timer0','register',0.9,9,'34  
11.3.4.1 UTIMER_UNT0_CFG Timer0 配置寄存器 .. .....134  
11.3.4.2 UTIMER','2025-07-03 14:49:46');
INSERT INTO "entities" VALUES('register_timer1','Timer1','register',0.9,9,'34  
11.3.4.2 UTIMER_UNT1_CFG Timer1 配置寄存器 .. ...136  
11.3.4.3 UTIMER_U','2025-07-03 14:49:46');
INSERT INTO "entities" VALUES('register_timer2','Timer2','register',0.9,9,'36  
11.3.4.3 UTIMER_UNT2_CFG Timer2 配置寄存器 . ..137  
11.3.4.4 UTIMER_UNT','2025-07-03 14:49:46');
INSERT INTO "entities" VALUES('register_timer3','Timer3','register',0.9,9,'37  
11.3.4.4 UTIMER_UNT3_CFG Timer3 配置寄存器 .. ..138  
11.3.4.5 UTIMER_UN','2025-07-03 14:49:46');
INSERT INTO "entities" VALUES('module_timer','Timer','module',0.9,9,'138  
11.3.4.5 UTIMER_UNT0_TH Timer 0 门限寄存器 .. ...139  
11.3.4.6 ','2025-07-03 14:49:46');
INSERT INTO "entities" VALUES('module_计数器','计数器','module',0.9,9,'
11.2.3 模式.. . 126  
11.2.3.1 计数器 .. .126  
11.2.3.2 比较模式. .127','2025-07-03 14:49:46');
INSERT INTO "entities" VALUES('function_滤波','滤波','function',0.9,9,'.2 寄存器模块. ..124  
11.1.1.3 IO 滤波模块.. .....125  
11.1.1.4 通用定时器','2025-07-03 14:49:46');
INSERT INTO "entities" VALUES('function_计数','计数','function',0.9,9,'
11.2.3 模式.. . 126  
11.2.3.1 计数器 .. .126  
11.2.3.2 比较模式. .12','2025-07-03 14:49:46');
INSERT INTO "entities" VALUES('register_encoder0','Encoder0','register',0.9,10,'******** UTIMER_ECD0_CFG  Encoder0 配置寄存器 .149  
11.3.5.2 UTIMER_ECD0_T','2025-07-03 14:49:46');
INSERT INTO "entities" VALUES('function_使能','使能','function',0.9,11,'11.3.7.1 UTIMER_RE DMA 请求使能寄存器 153','2025-07-03 14:49:46');
INSERT INTO "entities" VALUES('module_mcpwm','MCPWM','module',0.9,11,'13 MCPWM . 161','2025-07-03 14:49:46');
INSERT INTO "entities" VALUES('module_counter','Counter','module',0.9,11,'13.1 概述 . .161  
13.1.1 Base Counter 模块. 162  
13.1.2 Fail 信号处理.. ','2025-07-03 14:49:46');
INSERT INTO "entities" VALUES('function_设置','设置','function',0.9,11,'. ..168  
13.1.4.6 MCPWM IO 极性设置 . ..168  
13.1.4.7 MCPWM IO 自','2025-07-03 14:49:46');
INSERT INTO "entities" VALUES('module_uart','UART','module',0.9,13,'14 UART . 192','2025-07-03 14:49:46');
INSERT INTO "entities" VALUES('parameter_波特率','波特率','parameter',0.9,13,'192  
14.2.2 接收. 192  
14.2.3 波特率配置 . 192  
14.2.4 收发端口互换(TX/RX','2025-07-03 14:49:46');
INSERT INTO "entities" VALUES('register_uartx','UARTx','register',0.9,13,' 地址分 194   
14.3.2 UARTx_CTRL UARTx 控制寄存器.. 194   
14.3.3 UARTx_DIVH UA','2025-07-03 14:49:46');
INSERT INTO "entities" VALUES('parameter_10','10','parameter',0.9,14,' 
15.3.9.3 伪代码 .. .215  
15.3.10 MACI (reserved) . 215  
15.3.10.','2025-07-03 14:49:46');
INSERT INTO "entities" VALUES('module_i2c','I2C','module',0.9,18,'16 I2C . 227','2025-07-03 14:49:46');
INSERT INTO "entities" VALUES('module_spi','SPI','module',0.9,19,'I2C0_BCR DMA 传输控制寄存器 242  
17 SPI.. 243  
17.1 概述 . .243  
17.2','2025-07-03 14:49:46');
INSERT INTO "entities" VALUES('register_spi','SPI','register',0.9,20,'.1 地址分配. 251  
17.4.2 SPI_CFG SPI 控制寄存器. 251  
17.4.3 SPI_IE SPI 中断寄存','2025-07-03 14:49:46');
INSERT INTO "entities" VALUES('function_数据传输','数据传输','function',0.9,20,'存器. 254  
17.4.7 SPI_SIZE SPI 数据传输长度寄存器. 254','2025-07-03 14:49:46');
INSERT INTO "entities" VALUES('module_can','CAN','module',0.9,21,'19 CAN. 264','2025-07-03 14:49:46');
INSERT INTO "entities" VALUES('register_fifo','FIFO','register',0.9,21,'8 CAN_ RFIFO0\~CAN_RFIFO31 RX FIFO 寄存器 ..288  
19.2.3.2.19 CAN_TFIFO','2025-07-03 14:49:46');
INSERT INTO "entities" VALUES('function_恢复','恢复','function',0.9,21,'.......276  
19.2.2.15 离线状态与离线恢复 . ..276  
19.2.3 寄存器 .. .. 27','2025-07-03 14:49:46');
INSERT INTO "entities" VALUES('register_sif_cfg','SIF_CFG','register',0.9,23,'0.4.2 寄存器说明 .. 292  
20.4.2.1 SIF_CFG 配置寄存器. 292  
20.4.2.2 SIF_FREQ 波特率寄','2025-07-03 14:49:46');
INSERT INTO "entities" VALUES('module_看门狗','看门狗','module',0.9,23,'21 看门狗 .. 294','2025-07-03 14:49:46');
INSERT INTO "entities" VALUES('register_vtor','VTOR','register',0.9,26,'ctor 地址分配表 ... 42  
表 6-3 IAP VTOR 寄存器描述 . 44  
表 6-4 FLASH 控制器模块寄存器','2025-07-03 14:49:46');
INSERT INTO "entities" VALUES('register_gpiox','GPIOx','register',0.9,26,'寄存器 DMA_CMARx .. . 60  
表 8-1 GPIOx 寄存器列表 . . 64  
表8-2 GPIO 中断/唤醒/配置','2025-07-03 14:49:46');
INSERT INTO "entities" VALUES('register_adc0','ADC0','register',0.9,26,'输出数字量数制转换 ... ... 90  
表 10-5 ADC0 寄存器列表 .. ......... 93  
表 10-6 采样','2025-07-03 14:49:46');
INSERT INTO "entities" VALUES('register_dsp','DSP','register',0.9,29,'5-2 DSP 地址空间 .. .202  
表 15-3 DSP 寄存器列表 .. ...203  
表 15-4 DSP 状态控制','2025-07-03 14:49:46');
INSERT INTO "entities" VALUES('register_i2c','I2C','register',0.9,29,'SP 平方根寄存器 .......210  
表 16-1 I2C 寄存器地址分配表 . .238  
表 16-2 地址寄存器 I2','2025-07-03 14:49:46');
INSERT INTO "entities" VALUES('register_can','CAN','register',0.9,29,' 头信息 . ..........273  
表 19-7 CAN 寄存器地址分配 .... .....276  
表 19-8 模式','2025-07-03 14:49:46');
INSERT INTO "entities" VALUES('register_utimer','UTIMER','register',0.9,29,'用定时器配置寄存器地址分配 . .131  
表 11-5 UTIMER 配置寄存器 UTIMER_CFG .. .133  
表 11-6 滤','2025-07-03 14:49:46');
INSERT INTO "entities" VALUES('register_mcpwm_th00','MCPWM_TH00','register',0.9,29,'存在影子寄存器的寄存器 ... .171  
表 13-5 MCPWM_TH00 配置寄存器..... ...171  
表 13-6 MCPWM_TH','2025-07-03 14:49:46');
INSERT INTO "entities" VALUES('register_mcpwm_th01','MCPWM_TH01','register',0.9,29,'00 配置寄存器..... ...171  
表 13-6 MCPWM_TH01 配置寄存器........ ........172  
表 13-7 ','2025-07-03 14:49:46');
INSERT INTO "entities" VALUES('register_mcpwm_th10','MCPWM_TH10','register',0.9,29,'........ ........172  
表 13-7 MCPWM_TH10 配置寄存器...... ....172  
表 13-8 MCPWM_','2025-07-03 14:49:46');
INSERT INTO "entities" VALUES('register_mcpwm_th11','MCPWM_TH11','register',0.9,29,' 配置寄存器...... ....172  
表 13-8 MCPWM_TH11 配置寄存器..... .....173  
表 13-9 MCPWM_','2025-07-03 14:49:46');
INSERT INTO "entities" VALUES('register_mcpwm_th20','MCPWM_TH20','register',0.9,29,' 配置寄存器..... .....173  
表 13-9 MCPWM_TH20 配置寄存器... .173  
表 13-10 MCPWM_TH21 ','2025-07-03 14:49:46');
INSERT INTO "entities" VALUES('register_mcpwm_th21','MCPWM_TH21','register',0.9,29,'_TH20 配置寄存器... .173  
表 13-10 MCPWM_TH21 配置寄存器 ... .174  
表 13-11 MCPWM_TH30','2025-07-03 14:49:46');
INSERT INTO "entities" VALUES('register_mcpwm_th30','MCPWM_TH30','register',0.9,29,'TH21 配置寄存器 ... .174  
表 13-11 MCPWM_TH30 配置寄存器 .. .174  
表 13-12 MCPWM_TH31 ','2025-07-03 14:49:46');
INSERT INTO "entities" VALUES('register_mcpwm_th31','MCPWM_TH31','register',0.9,29,'_TH30 配置寄存器 .. .174  
表 13-12 MCPWM_TH31 配置寄存器 .. .174  
表 13-13 MCPWM_TMR0 ','2025-07-03 14:49:46');
INSERT INTO "entities" VALUES('register_mcpwm_tmr0','MCPWM_TMR0','register',0.9,29,'_TH31 配置寄存器 .. .174  
表 13-13 MCPWM_TMR0 配置寄存器 .. .175  
表 13-14 MCPWM_TMR1 ','2025-07-03 14:49:46');
INSERT INTO "entities" VALUES('register_mcpwm_tmr1','MCPWM_TMR1','register',0.9,29,'_TMR0 配置寄存器 .. .175  
表 13-14 MCPWM_TMR1 配置寄存器 ... .......175  
表 13-15 MCPW','2025-07-03 14:49:46');
INSERT INTO "entities" VALUES('register_mcpwm_tmr2','MCPWM_TMR2','register',0.9,29,'置寄存器 ... .......175  
表 13-15 MCPWM_TMR2 配置寄存器 ... .................176  
表 ','2025-07-03 14:49:46');
INSERT INTO "entities" VALUES('register_mcpwm_tmr3','MCPWM_TMR3','register',0.9,29,'................176  
表 13-16 MCPWM_TMR3 配置寄存器 ... ....176  
表 13-17 MCPWM_T','2025-07-03 14:49:46');
INSERT INTO "entities" VALUES('register_mcpwm_th','MCPWM_TH','register',0.9,29,'3 配置寄存器 ... ....176  
表 13-17 MCPWM_TH 配置寄存器 ..  
表 13-18 MCPWM_UPDATE 配置寄','2025-07-03 14:49:46');
INSERT INTO "entities" VALUES('register_mcpwm_update','MCPWM_UPDATE','register',0.9,29,'7 MCPWM_TH 配置寄存器 ..  
表 13-18 MCPWM_UPDATE 配置寄存器 .. .177  
表 13-19 MCPWM_IE 配置','2025-07-03 14:49:46');
INSERT INTO "entities" VALUES('register_mcpwm_ie','MCPWM_IE','register',0.9,29,'PDATE 配置寄存器 .. .177  
表 13-19 MCPWM_IE 配置寄存器 ... ......178  
表 13-20 MCPWM','2025-07-03 14:49:46');
INSERT INTO "entities" VALUES('register_mcpwm_if','MCPWM_IF','register',0.9,29,'配置寄存器 ... ......178  
表 13-20 MCPWM_IF 配置寄存器 . 179  
表 13-21 MCPWM_EIE 配置寄','2025-07-03 14:49:46');
INSERT INTO "entities" VALUES('register_mcpwm_eie','MCPWM_EIE','register',0.9,29,'CPWM_IF 配置寄存器 . 179  
表 13-21 MCPWM_EIE 配置寄存器 . .180  
表 13-22 MCPWM_EIF 配置','2025-07-03 14:49:46');
INSERT INTO "entities" VALUES('register_mcpwm_eif','MCPWM_EIF','register',0.9,29,'WM_EIE 配置寄存器 . .180  
表 13-22 MCPWM_EIF 配置寄存器 . .181  
表 13-23 MCPWM_RE 配置寄','2025-07-03 14:49:46');
INSERT INTO "entities" VALUES('register_mcpwm_re','MCPWM_RE','register',0.9,29,'WM_EIF 配置寄存器 . .181  
表 13-23 MCPWM_RE 配置寄存器 .. ...182  
表 13-24 MCPWM_PP ','2025-07-03 14:49:46');
INSERT INTO "entities" VALUES('register_mcpwm_pp','MCPWM_PP','register',0.9,29,'_RE 配置寄存器 .. ...182  
表 13-24 MCPWM_PP 配置寄存器 ....... .....................','2025-07-03 14:49:46');
INSERT INTO "entities" VALUES('register_mcpwm_io01','MCPWM_IO01','register',0.9,29,'................182  
表 13-25 MCPWM_IO01 配置寄存器 ..... .....183  
表 13-26 MCPW','2025-07-03 14:49:46');
INSERT INTO "entities" VALUES('register_mcpwm_io23','MCPWM_IO23','register',0.9,29,'置寄存器 ..... .....183  
表 13-26 MCPWM_IO23 配置寄存器 .. .........184  
表 13-27 MCP','2025-07-03 14:49:46');
INSERT INTO "entities" VALUES('register_mcpwm_sdcfg','MCPWM_SDCFG','register',0.9,29,'寄存器 .. .........184  
表 13-27 MCPWM_SDCFG 配置寄存器... ...185  
表 13-28 MCPWM_TCL','2025-07-03 14:49:46');
INSERT INTO "entities" VALUES('register_mcpwm_tclk','MCPWM_TCLK','register',0.9,29,'CFG 配置寄存器... ...185  
表 13-28 MCPWM_TCLK 配置寄存器 . .......185  
表 13-29 MCPWM_','2025-07-03 14:49:46');
INSERT INTO "entities" VALUES('register_mcpwm_fail','MCPWM_FAIL','register',0.9,29,' 配置寄存器 . .......185  
表 13-29 MCPWM_FAIL 配置寄存器 . .186  
表 13-30 MCPWM_PRT 配置','2025-07-03 14:49:46');
INSERT INTO "entities" VALUES('register_mcpwm_prt','MCPWM_PRT','register',0.9,29,'M_FAIL 配置寄存器 . .186  
表 13-30 MCPWM_PRT 配置寄存器 .. ...187  
表 13-31 MCPWM_CNT','2025-07-03 14:49:46');
INSERT INTO "entities" VALUES('register_mcpwm_cnt','MCPWM_CNT','register',0.9,29,'PRT 配置寄存器 .. ...187  
表 13-31 MCPWM_CNT 配置寄存器.. .188  
表 13-32 MCPWM_DTH00 ','2025-07-03 14:49:46');
INSERT INTO "entities" VALUES('register_mcpwm_dth00','MCPWM_DTH00','register',0.9,29,'WM_CNT 配置寄存器.. .188  
表 13-32 MCPWM_DTH00 配置寄存器 . .188  
表 13-33 MCPWM_DTH01 ','2025-07-03 14:49:46');
INSERT INTO "entities" VALUES('register_mcpwm_dth01','MCPWM_DTH01','register',0.9,29,'_DTH00 配置寄存器 . .188  
表 13-33 MCPWM_DTH01 配置寄存器 .189  
表 13-34 MCPWM_DTH10 配置','2025-07-03 14:49:46');
INSERT INTO "entities" VALUES('register_mcpwm_dth10','MCPWM_DTH10','register',0.9,29,'WM_DTH01 配置寄存器 .189  
表 13-34 MCPWM_DTH10 配置寄存器 . .189  
表 13-35 MCPWM_DTH11 ','2025-07-03 14:49:46');
INSERT INTO "entities" VALUES('register_mcpwm_dth11','MCPWM_DTH11','register',0.9,29,'_DTH10 配置寄存器 . .189  
表 13-35 MCPWM_DTH11 配置寄存器 . .190  
表 13-36 MCPWM_DTH20 ','2025-07-03 14:49:46');
INSERT INTO "entities" VALUES('register_mcpwm_dth20','MCPWM_DTH20','register',0.9,29,'_DTH11 配置寄存器 . .190  
表 13-36 MCPWM_DTH20 配置寄存器 ... ...190  
表 13-37 MCPWM_DT','2025-07-03 14:49:46');
INSERT INTO "entities" VALUES('register_mcpwm_dth21','MCPWM_DTH21','register',0.9,29,'20 配置寄存器 ... ...190  
表 13-37 MCPWM_DTH21 配置寄存器 ....... .........190  
表 13-3','2025-07-03 14:49:46');
INSERT INTO "entities" VALUES('register_mcpwm_dth30','MCPWM_DTH30','register',0.9,29,'...... .........190  
表 13-38 MCPWM_DTH30 配置寄存器 .... ...191  
表 13-39 MCPWM_D','2025-07-03 14:49:46');
INSERT INTO "entities" VALUES('register_mcpwm_dth31','MCPWM_DTH31','register',0.9,29,'0 配置寄存器 .... ...191  
表 13-39 MCPWM_DTH31 配置寄存器 ......... ........191  
表 14-','2025-07-03 14:49:46');
INSERT INTO "entities" VALUES('register_spi_div','SPI_DIV','register',0.9,29,'I_IE 中断寄存器 .. ...252  
表 17-4 SPI_DIV 控制寄存器.. .253  
表 17-5 SPI_TX_DATA 数','2025-07-03 14:49:46');
INSERT INTO "entities" VALUES('register_volatile','Volatile','register',0.9,39,'t，即上电复位，芯片系统上电时产生的复位信号NVR：Non-Volatile Register，flash 中区别于 main 区域之外的一块存储区域','2025-07-03 14:49:46');
INSERT INTO "entities" VALUES('module_wdt','WDT','module',0.9,39,'er，数模转换器  
BGP：Bandgap，带隙基准  
WDT：Watch dog，看门狗  
LSI：Low Speed','2025-07-03 14:49:46');
INSERT INTO "entities" VALUES('parameter_32','32','parameter',0.9,39,'SI：Low Speed Internal Clock，即 32kHZ RC 时钟  
HSI：High Speed Intern','2025-07-03 14:49:46');
INSERT INTO "entities" VALUES('parameter_96','96','parameter',0.9,39,' 
PLL：Phase Lock Loop Clock，即 96MHz 锁相环时钟，通常用作系统高速时钟POR：Power-On ','2025-07-03 14:49:46');
INSERT INTO "entities" VALUES('parameter_16','16','parameter',0.9,39,'字：32 位数据/指令。  
半字：16 位数据/指令。  
字节：8 位数据。  
双字：64 位数据','2025-07-03 14:49:46');
INSERT INTO "entities" VALUES('parameter_64','64','parameter',0.9,39,'字：16 位数据/指令。  
字节：8 位数据。  
双字：64 位数据。  
ADC：Analog-Digital Conve','2025-07-03 14:49:46');
INSERT INTO "entities" VALUES('function_暂停','暂停','function',0.9,41,'级与当前的优先级进行比较，如果新的优先级更高，当前的任务会被暂停，处理器进行核心寄存器入栈保持，然后开始处理新的异常程序，这','2025-07-03 14:49:46');
INSERT INTO "entities" VALUES('function_停止','停止','function',0.9,42,'异常，会引起程序控制的变化。在异常发生时，处理器停止当前的任务，转而执行异常处理程序，异常处理完成后，会继续执行','2025-07-03 14:49:46');
INSERT INTO "entities" VALUES('parameter_频率','频率','parameter',0.9,43,'可编程设置频率的RTOS 定时器(例如 $\lvert 1 0 0 \ma','2025-07-03 14:49:46');
INSERT INTO "entities" VALUES('address_0x00000000','0x00000000','address',0.9,43,'地址：0xE000_E010  
复位值：0x00000000','2025-07-03 14:49:46');
INSERT INTO "entities" VALUES('parameter_精度','精度','parameter',0.9,46,'1 . 2 \mathrm { V } 0 . 8 \%$ 精度电压基准源','2025-07-03 14:49:46');
INSERT INTO "entities" VALUES('parameter_12','12','parameter',0.9,46,'集成 1 路同步双采样的 12bit SAR ADC，采样及转换速率 3Msps。最多 20 通','2025-07-03 14:49:46');
INSERT INTO "entities" VALUES('parameter_20','20','parameter',0.9,46,'2bit SAR ADC，采样及转换速率 3Msps。最多 20 通道  
集成4 路运算放大器，可设置为 PGA 模式  
集成','2025-07-03 14:49:46');
INSERT INTO "entities" VALUES('function_监测','监测','function',0.9,47,'POR 模块监测AVDD 的电压，在AVDD 电压低于3.0V 时（例如上电','2025-07-03 14:49:46');
INSERT INTO "entities" VALUES('bit_field_pvdsel','PVDSEL','bit_field',0.9,48,'PVDSEL[1:0]/ PD_PDT 的说明见模拟寄存器 SYS_AFE_REG','2025-07-03 14:49:46');
INSERT INTO "entities" VALUES('parameter_电流','电流','parameter',0.9,48,' 关闭），RC 时钟需要BGP 电压基准源模块提供基准电压和电流，因此开启RC 时钟需要先开启BGP 模块（ $\mathr','2025-07-03 14:49:46');
INSERT INTO "entities" VALUES('module_pwm','PWM','module',0.9,48,'给 CPU、ADC 等模块提供更高速的工作时钟。CPU 和 PWM 模块的最高时钟为96MHz，ADC 模块最高时钟 $4 8','2025-07-03 14:49:46');
INSERT INTO "entities" VALUES('bit_field_adclksel','ADCLKSEL','bit_field',0.9,48,'\mathrm { { M H z } }$ ，通过寄存器 ADCLKSEL[1:0]可设置不同的 ADC工作频率。','2025-07-03 14:49:46');
INSERT INTO "entities" VALUES('parameter_15','15','parameter',0.9,48,'入一个晶体，且在OSC_IN/OSC_OUT 引脚各接一个 15pF 电容到地，设置 XTALPDN $_ { = 1 }$ 即','2025-07-03 14:49:46');
INSERT INTO "entities" VALUES('bit_field_res_opax','RES_OPAx','bit_field',0.9,49,'rm { R } } _ { 1 }$ 的阻值可通过寄存器 RES_OPAx[1:0]设置，以实现不同的放大倍数。','2025-07-03 14:49:46');
INSERT INTO "entities" VALUES('parameter_85','85','parameter',0.9,51,'寄存器 DAC_GAIN<1:0>设置为1.2V/3V/4.85V','2025-07-03 14:49:46');
INSERT INTO "entities" VALUES('parameter_50','50','parameter',0.9,51,'5 \mathrm { k } \Omega$ 的负载电阻和50pF 的负载电容。','2025-07-03 14:49:46');
INSERT INTO "entities" VALUES('register_sys_afe_dac_amc','SYS_AFE_DAC_AMC','register',0.9,51,'YS_AFE_DAC 填入值（理想值对应数字量）。a 来自 SYS_AFE_DAC_AMC 寄存器，b 来自SYS_AFE_DAC_DC。硬件根据 SYS_A','2025-07-03 14:49:46');
INSERT INTO "entities" VALUES('address_0x00000340','0x00000340','address',0.9,51,' 0 0 3 3 0$ ，为 3V 量程的 a 参数，地址 0x00000340，为 3V 量程的 b 参数。','2025-07-03 14:49:46');
INSERT INTO "entities" VALUES('address_0x00000334','0x00000334','address',0.9,51,'地址 0x00000334，为 1.2V 量程的 a 参数，地址 0x00000344','2025-07-03 14:49:46');
INSERT INTO "entities" VALUES('address_0x00000344','0x00000344','address',0.9,51,'0x00000334，为 1.2V 量程的 a 参数，地址 0x00000344，为 1.2V 量程的 b 参数。','2025-07-03 14:49:46');
INSERT INTO "entities" VALUES('address_0x00000338','0x00000338','address',0.9,51,'地址 0x00000338，为 4.85V 量程的 a 参数，地址 0x0000034','2025-07-03 14:49:46');
INSERT INTO "entities" VALUES('address_0x00000348','0x00000348','address',0.9,51,'x00000338，为 4.85V 量程的 a 参数，地址 0x00000348，为 4.85V 量程的 b 参数。','2025-07-03 14:49:46');
INSERT INTO "entities" VALUES('register_sys_clk_cfg','SYS_CLK_CFG','register',0.9,54,'MCLK 是系统主时钟。可以通过 SYS_CLK_CFG 寄存器 CLK_DIV 位域控制进行 $\mathrm { n }','2025-07-03 14:49:46');
INSERT INTO "entities" VALUES('register_sys_clk_fen','SYS_CLK_FEN','register',0.9,54,'MCLK 时钟经过 SYS_CLK_FEN 寄存器控制的开关之后供给外设。I2C 时钟由 SYS_CLK_DI','2025-07-03 14:49:46');
INSERT INTO "entities" VALUES('register_sys_clk_div0','SYS_CLK_DIV0','register',0.9,54,'LK_FEN 寄存器控制的开关之后供给外设。I2C 时钟由 SYS_CLK_DIV0 寄存器控制可以进一步分频，UART 时钟由SYS_CLK_DIV2','2025-07-03 14:49:46');
INSERT INTO "entities" VALUES('parameter_48','48','parameter',0.9,54,'的 $2 / 4 / 8$ 分频后送至 ADC（典型工作频率48MHz），即 ACLK。','2025-07-03 14:49:46');
INSERT INTO "entities" VALUES('register_sys_clk_slp','SYS_CLK_SLP','register',0.9,55,'向 SYS_CLK_SLP 寄存器写入 0xDEAD 可以令芯片准备进入休眠状态，之后立刻执行','2025-07-03 14:49:46');
INSERT INTO "entities" VALUES('address_0xdead','0xDEAD','address',0.9,55,'向 SYS_CLK_SLP 寄存器写入 0xDEAD 可以令芯片准备进入休眠状态，之后立刻执行__WFI()宏指','2025-07-03 14:49:46');
INSERT INTO "entities" VALUES('bit_field_p0','P0','bit_field',0.9,55,'仅有 P0[1:0]、P1[1:0]四个 IO 可以作为外部唤醒 IO 使用，可','2025-07-03 14:49:46');
INSERT INTO "entities" VALUES('bit_field_p1','P1','bit_field',0.9,55,'仅有 P0[1:0]、P1[1:0]四个 IO 可以作为外部唤醒 IO 使用，可以配置独立的使能','2025-07-03 14:49:46');
INSERT INTO "entities" VALUES('register_sys_rst_src','SYS_RST_SRC','register',0.9,57,'SYS_RST_SRC 寄存器用于保存硬件复位事件，当某个硬件复位发生后，SYS_RST_','2025-07-03 14:49:46');
INSERT INTO "entities" VALUES('register_sys_clr_rst','SYS_CLR_RST','register',0.9,57,'_RST_SRC 寄存器本身无法被复位信号复位，只能通过向 SYS_CLR_RST 寄存器写入 0xCA40清空记录，复位记录可以方便地了解是否发生以','2025-07-03 14:49:46');
INSERT INTO "entities" VALUES('bit_field_sys_clk_cfg','SYS_CLK_CFG','bit_field',0.9,69,'当使用4MHz HSI 时钟作为系统主时钟时，SYS_CLK_CFG[7:0]的分频系数无效，最终输出的系统时钟频率即为4MHz。','2025-07-03 14:49:46');
INSERT INTO "entities" VALUES('address_0xffffffff','0xFFFFFFFF','address',0.9,76,'Flash 数据防窃取（最后一个 word 须写入非 0xFFFFFFFF 的任意值）','2025-07-03 14:49:46');
INSERT INTO "entities" VALUES('register_flash','FLASH','register',0.9,77,'
FLASH 的读取加速操作，以提升芯片整体运行效率。  
FLASH 控制寄存器的访问。','2025-07-03 14:49:46');
INSERT INTO "entities" VALUES('parameter_24','24','parameter',0.9,79,'配置 FLASH_CFG.TBS 的值，以实现 48MHz/24MHz和 12MHz 的计数值（其它频率暂不支持）。最终保证计数值','2025-07-03 14:49:46');
INSERT INTO "entities" VALUES('register_flash_wdata','FLASH_WDATA','register',0.9,79,'G.ADR_INC，开启地址自动递增模式，后续只需要反复写 FLASH_WDATA 寄存器即可，FLASH_ADDR 每次写入一次数据会自动增加 $0','2025-07-03 14:49:46');
INSERT INTO "entities" VALUES('address_0x7654dcba','0x7654DCBA','address',0.9,81,'ASH_ADDR 的值将失效。FLASH_ERASE 写入 0x7654DCBA 触发擦除操作。','2025-07-03 14:49:46');
INSERT INTO "entities" VALUES('register_flash_protect','FLASH_PROTECT','register',0.9,81,'，执行最后一个 WORD 的编程，写入非全 1 的值，读取 FLASH_PROTECT 寄存器，即触发一次加密状态更新，完成加密（读取FLASH_PROT','2025-07-03 14:49:46');
INSERT INTO "entities" VALUES('module_ram','RAM','module',0.9,83,'如果需要将flash 全部擦除，则需要将在线升级函数放置在 RAM 中，如果需要使用中断则新的中断向量入口地址也需要位于RAM','2025-07-03 14:49:46');
INSERT INTO "entities" VALUES('address_0x00000060','0x00000060','address',0.9,83,'地址：0x4000_0400  
复位值：0x00000060','2025-07-03 14:49:46');
INSERT INTO "entities" VALUES('function_禁用','禁用','function',0.9,90,'A 模块可以通过设置 DMA_CTRL.EN 位为 0 来被禁用（要求关闭 DMA使能前先关闭4 个通道对应的使能 DMA_','2025-07-03 14:49:46');
INSERT INTO "entities" VALUES('bit_field_req_en','REQ_EN','bit_field',0.9,96,' _ { 1 } }$ 以通道 0 为例，DMA_CCR0.REQ_EN[2:0]分别为 Timer1、Timer0、ADC0 的 DMA 请','2025-07-03 14:49:46');
INSERT INTO "entities" VALUES('register_dma_ctmsx','DMA_CTMSx','register',0.9,97,'DMA_CTMSx 寄存器只有在通道禁用，即 DMA_CCRx.EN $= 0$ 之后','2025-07-03 14:49:46');
INSERT INTO "entities" VALUES('function_启动','启动','function',0.9,97,'通常在开启DMA 之前需要将触发 DMA 启动的外设接收中断标志位清零，防止之前留下的中断标志位成为DMA','2025-07-03 14:49:46');
INSERT INTO "entities" VALUES('bit_field_peri_addr','PERI_ADDR','bit_field',0.9,98,'时，即配置为以 32bit 为单位搬运外设数据。CPARx.PERI_ADDR[1:0]值无效，外设地址会以4 为单位递增。','2025-07-03 14:49:46');
INSERT INTO "entities" VALUES('register_dma_cparx','DMA_CPARx','register',0.9,98,'注意：DMA_CPARx 寄存器只有在通道禁用，即 DMA_CCRx.EN $= 0$ 之后','2025-07-03 14:49:46');
INSERT INTO "entities" VALUES('register_sys','SYS','register',0.9,98,' \mathrm { x } 4 0 0 0 0$ (对应 SYS 寄存器)或 $0 \mathrm { x } 4 0 0 1 ^ ','2025-07-03 14:49:46');
INSERT INTO "entities" VALUES('parameter_17','17','parameter',0.9,98,'设间的数据搬运，因此 DMA_CPAR 只存储外设地址的低 17 位，高 15 位恒为 $0 \mathrm { x } 2 0','2025-07-03 14:49:46');
INSERT INTO "entities" VALUES('bit_field_mem_addr','MEM_ADDR','bit_field',0.9,99,'时，即配置为以 32bit 为单位搬运内存数据。CMARx.MEM_ADDR[1:0]值无效，内存地址会以4 为单位递增。','2025-07-03 14:49:46');
INSERT INTO "entities" VALUES('register_dma_cmarx','DMA_CMARx','register',0.9,99,'注意：DMA_CMARx 寄存器只有在通道禁用，即 DMA_CCRx.EN=0 之后才可以写','2025-07-03 14:49:46');
INSERT INTO "entities" VALUES('parameter_13','13','parameter',0.9,99,'内存与外设间的数据搬运，因此DMA_CMAR 只存储地址的低13 位，对应SRAM 8kB地址空间。高19 位恒为 $0 \ma','2025-07-03 14:49:46');
INSERT INTO "entities" VALUES('parameter_19','19','parameter',0.9,99,'R 只存储地址的低13 位，对应SRAM 8kB地址空间。高19 位恒为 $0 \mathrm { x } 1 0 0 0 0$','2025-07-03 14:49:46');
INSERT INTO "entities" VALUES('address_0x40012000','0x40012000','address',0.9,102,'GPIO 0 模块在芯片中的基地址是 0x40012000。  
GPIO 1 模块在芯片中的基地址是 0x40012','2025-07-03 14:49:46');
INSERT INTO "entities" VALUES('address_0x40012040','0x40012040','address',0.9,102,'0012000。  
GPIO 1 模块在芯片中的基地址是 0x40012040。  
GPIO 2 模块在芯片中的基地址是 0x40012','2025-07-03 14:49:46');
INSERT INTO "entities" VALUES('address_0x40012080','0x40012080','address',0.9,102,'0012040。  
GPIO 2 模块在芯片中的基地址是 0x40012080。  
GPIO 3 模块在芯片中的基地址是 0x40012','2025-07-03 14:49:46');
INSERT INTO "entities" VALUES('address_0x400120c0','0x400120C0','address',0.9,102,'0012080。  
GPIO 3 模块在芯片中的基地址是 0x400120C0。  
GPIO 0/1/2/3 的寄存器定义完全相同，仅基','2025-07-03 14:49:46');
INSERT INTO "entities" VALUES('address_0x40012100','0x40012100','address',0.9,102,'GPIO 中断/唤醒/配置锁定模块的基地址是 0x40012100，寄存器列表如下：','2025-07-03 14:49:46');
INSERT INTO "entities" VALUES('address_0x4001','0x4001','address',0.9,105,'地址分别是：0x4001_2010，0x4001_2050，0x4001-2090，0x4001_20D0  
复位值：0x0','2025-07-03 14:49:46');
INSERT INTO "entities" VALUES('address_0x3233','0x3233','address',0.9,116,'SS or FAIL   
if(GPIO0_PIE != 0x3233)FAIL;   
if(GPIO1_PIE $\ ! = ','2025-07-03 14:49:46');
INSERT INTO "entities" VALUES('address_0x5ac4','0x5AC4','address',0.9,116,'// write any value other than 0x5AC4 to enable lock protect GPIO0_','2025-07-03 14:49:46');
INSERT INTO "entities" VALUES('address_0x0100','0x0100','address',0.9,116,' 0 0 0 0$ ; if(GPIO0_LCKR  != 0x0100)FAIL;   
if(GPIO1_LCKR != 0xF','2025-07-03 14:49:46');
INSERT INTO "entities" VALUES('address_0xffff','0xFFFF','address',0.9,116,'100)FAIL;   
if(GPIO1_LCKR != 0xFFFF)FAIL;   
if(GPIO2_LCKR $\math','2025-07-03 14:49:46');
INSERT INTO "entities" VALUES('address_0x0000','0x0000','address',0.9,116,'\$ )FAIL;   
if(GPIO1_LCKR != 0x0000)FAIL;   
if(GPIO2_LCKR != 0x0','2025-07-03 14:49:46');
INSERT INTO "entities" VALUES('address_0x0080','0x0080','address',0.9,117,' { \tau } = \mathbf { \tau }$ 0x0080; // 使能 P0[7]输入  
NVIC_EnableI','2025-07-03 14:49:46');
INSERT INTO "entities" VALUES('function_通信','通信','function',0.9,118,'码（Cyclic Redundancy Check）：是数据通信领域中最常用的一种查错校验码，其特征是信息字段和校验字段的长','2025-07-03 14:49:46');
INSERT INTO "entities" VALUES('register_crc_dr','CRC_DR','register',0.9,121,'CRC_DR 寄存器既用于放入待校验数据，也用于返回校验结果。写入 CRC_DR','2025-07-03 14:49:46');
INSERT INTO "entities" VALUES('register_crc_init','CRC_INIT','register',0.9,122,'需要注意的是，向 CRC_CR.RESET 写入 1 会将 CRC_INIT 寄存器复位为 0xFFFFFFFF。','2025-07-03 14:49:46');
INSERT INTO "entities" VALUES('bit_field_adc_chn0','ADC_CHN0','bit_field',0.9,124,'1 9$ 任选，若ADC_CHN0[4:0] $= 0$ ，ADC_CHN0[12:8] $^ { = 3 }$ ，','2025-07-03 14:49:46');
INSERT INTO "entities" VALUES('bit_field_adc_amc','ADC_AMC','bit_field',0.9,129,' 10bit 无符号定点数，ADC_AMC[9]为整数部分，ADC_AMC[8:0]为小数部分。可以表示数值在1 附近的定点数。','2025-07-03 14:49:46');
INSERT INTO "entities" VALUES('bit_field_adcclksel','ADCCLKSEL','bit_field',0.9,130,'和 PLL 模块。通过配置寄存器 SYS_AFE_REG7.ADCCLKSEL[5:4]设置 ADC 工作频率，00 为 48MHz，10 为 12','2025-07-03 14:49:46');
INSERT INTO "entities" VALUES('register_data_align','DATA_ALIGN','register',0.9,130,'出格式可配置为左对齐或者右对齐，配置的是 ADC0_CFG.DATA_ALIGN 寄存器，0 为左对齐，1 为右对齐。','2025-07-03 14:49:46');
INSERT INTO "entities" VALUES('bit_field_trg_mode','TRG_MODE','bit_field',0.9,130,' 段 采 样 模 式 ， 配 置 的 是ADC0_TRIG.TRG_MODE[13:12]寄存器，00 为单段采样模式，01 为两段采样模式，11 为','2025-07-03 14:49:46');
INSERT INTO "entities" VALUES('bit_field_single_tcnt','SINGLE_TCNT','bit_field',0.9,130,'以设置触发一次采样所需的事件数，配置的是ADC0_TRIG.SINGLE_TCNT[11:8]寄存器，设置范围是 $_ { 0 \sim 1 5 , 0 ','2025-07-03 14:49:46');
INSERT INTO "entities" VALUES('register_adc0_chnt1','ADC0_CHNT1','register',0.9,130,'段采样模式下，采样的通道个数，配置的是ADC0_CHNT0、ADC0_CHNT1 寄存器，设置范围是 $_ { 1 \sim 2 0 }$ ，1 代','2025-07-03 14:49:46');
INSERT INTO "entities" VALUES('register_adc0_chnx','ADC0_CHNx','register',0.9,146,'以单段触发采样8 个采样为例，ADC0_CHNx 寄存器里设置的第 0/1 个采样是同步采样的，2/3是同步采样的，','2025-07-03 14:49:46');
INSERT INTO "entities" VALUES('address_0x5aa5','0x5AA5','address',0.9,152,'注意，软件触发采集寄存器为只写寄存器，且只有写入数据为 0x5AA5 时产生软件触发事件，一次总线的写入产生一次软件触发，数据写','2025-07-03 14:49:46');
INSERT INTO "entities" VALUES('bit_field_adcamc','ADCAMC','bit_field',0.9,155,'为 10bit 无符号定点数，ADCAMC[9]为整数部分，ADCAMC[8:0]为小数部分。所存值为 1 左右。','2025-07-03 14:49:46');
INSERT INTO "entities" VALUES('parameter_54','54','parameter',0.9,155,'表 10-54 通道 0 阈值寄存器 ADC0_DAT0_TH','2025-07-03 14:49:46');
INSERT INTO "entities" VALUES('bit_field_adc_chnt0','ADC_CHNT0','bit_field',0.9,158,'TADC[0]或软件触发发生后，先进行 ADC_CHNT0[4:0]次采样，完成后进入空闲状态并等待下一个触发信号的到来；TAD','2025-07-03 14:49:46');
INSERT INTO "entities" VALUES('register_adc_swt','ADC_SWT','register',0.9,159,'需要使用软件触发采样，需要确保硬件触发已经关闭。然后通过向 ADC_SWT 寄存器写入 0x5AA5 以产生一次软件触发。','2025-07-03 14:49:46');
INSERT INTO "entities" VALUES('bit_field_adc_chnt1','ADC_CHNT1','bit_field',0.9,159,'DC_CHNT0[4:0]、ADC_CHNT0[12:8]、ADC_CHNT1[4:0]、ADC_CHNT1[12:8]。','2025-07-03 14:49:46');
INSERT INTO "entities" VALUES('address_0x0004','0x0004','address',0.9,160,'; //关闭 ADC 采样触发  
ADC0_CFG |= 0x0004; //复位ADC 接口电路状态机//此处进行的 ADC 采','2025-07-03 14:49:46');
INSERT INTO "entities" VALUES('address_0x0005','0x0005','address',0.9,160,'样通道和通道数的修改仅为示例  
ADC0_CHNT0 = 0x0005 //修改ADC 单段采样通道数为5  
ADC0_CHN0','2025-07-03 14:49:46');
INSERT INTO "entities" VALUES('address_0x0305','0x0305','address',0.9,160,'修改ADC 单段采样通道数为5  
ADC0_CHN0 = 0x0305; //修改 ADC 第 0/1 次采样通道为模拟通道 5 ','2025-07-03 14:49:46');
INSERT INTO "entities" VALUES('address_0x0604','0x0604','address',0.9,160,' { \tau } = \mathbf { \tau }$ 0x0604; //修改ADC 第2/3 次采样通道为模拟通道 4 和6','2025-07-03 14:49:46');
INSERT INTO "entities" VALUES('register_utimer_reg','utimer_reg','register',0.9,162,'utimer_reg 寄存器模块，实现','2025-07-03 14:49:46');
INSERT INTO "entities" VALUES('register_utimer_untx_cmp','UTIMER_UNTx_CMP','register',0.9,166,'沿，发生捕获事件（即输入信号电平变化）时，定时器计数值存入 UTIMER_UNTx_CMP 寄存器，并产生捕获中断。计数器回零时，仍然会产生回零中断。','2025-07-03 14:49:46');
INSERT INTO "entities" VALUES('register_encoderx','EncoderX','register',0.9,187,'******** UTIMER_ECDx_CFG  EncoderX 配置寄存器UTIMER_ECD0_CFG 地址：0x4001_1880','2025-07-03 14:49:46');
INSERT INTO "entities" VALUES('register_clk_div','CLK_DIV','register',0.9,193,'HALL模块工作频率可调。通过配置HALL_CFG.CLK_DIV寄存器，可以选择系统主时钟的1/2/4/8分频作为HALL 模块工','2025-07-03 14:49:46');
INSERT INTO "entities" VALUES('bit_field_fil_data','FIL_DATA','bit_field',0.9,194,'通过访问 HALL_INFO.FIL_DATA[2:0]可以捕捉滤波后的 HALL 信号；HALL_INFO.RAW','2025-07-03 14:49:46');
INSERT INTO "entities" VALUES('bit_field_raw_data','RAW_DATA','bit_field',0.9,194,':0]可以捕捉滤波后的 HALL 信号；HALL_INFO.RAW_DATA[2:0]则是滤波前原始HALL 输入信号，详见12.3.3。','2025-07-03 14:49:46');
INSERT INTO "entities" VALUES('parameter_42','42','parameter',0.9,194,'6 { = } 1 . 4 0 s$ 的时间宽度，达到10.42ns 的时间分辨率。','2025-07-03 14:49:46');
INSERT INTO "entities" VALUES('parameter_分辨率','分辨率','parameter',0.9,194,'1 . 4 0 s$ 的时间宽度，达到10.42ns 的时间分辨率。','2025-07-03 14:49:46');
INSERT INTO "entities" VALUES('bit_field_mcpwm_bkin','MCPWM_BKIN','bit_field',0.9,201,'M，即 FAIL0 和 FAIL1，分别可以来自芯片 IO MCPWM_BKIN[1:0]或芯片内部比较器的输出 CMP[1:0]。','2025-07-03 14:49:46');
INSERT INTO "entities" VALUES('bit_field_cmp','CMP','bit_field',0.9,201,'IO MCPWM_BKIN[1:0]或芯片内部比较器的输出 CMP[1:0]。','2025-07-03 14:49:46');
INSERT INTO "entities" VALUES('bit_field_io_flt_clkdiv','IO_FLT_CLKDIV','bit_field',0.9,201,'MCPWM_BKIN[1:0]则使用 MCPWM_TCLK.IO_FLT_CLKDIV[3:0]作为第二级的分频系数；如果 Fail 信号来自芯片内部比较器','2025-07-03 14:49:46');
INSERT INTO "entities" VALUES('bit_field_cmp_flt_clkdiv','CMP_FLT_CLKDIV','bit_field',0.9,201,' 信号来自芯片内部比较器输出，则使用 MCPWM_TCLK.CMP_FLT_CLKDIV[3:0]作为第二级的分频系数，如图 13-5 所示。','2025-07-03 14:49:46');
INSERT INTO "entities" VALUES('register_chxp_default','CHxP_DEFAULT','register',0.9,202,'FAIL.CHxN_DEFAULT 和MCPWM_FAIL.CHxP_DEFAULT 寄存器所指定的故障缺省值，此时 MCPWM_FAIL.CHxN_D','2025-07-03 14:49:46');
INSERT INTO "entities" VALUES('bit_field_fail','FAIL','bit_field',0.9,207,'当芯片调试中，CPU Halt 时，PWM 停止输出，输出 FAIL[15:8]的值。','2025-07-03 14:49:46');
INSERT INTO "entities" VALUES('address_0x0c','0x0C','address',0.9,234,'ART 的 Tx_buffer 和 Rx_buffer 共享地址 0x0C 地址。其中，Tx_buffer 是只写的，Rx_buffe','2025-07-03 14:49:47');
INSERT INTO "entities" VALUES('register_dsp_cos','DSP_COS','register',0.9,242,' 为输入，计算并输出 sin/cos 结果到DSP_SIN/DSP_COS 寄存器；计算 arctan 时以坐标 DSP_X/DSP_Y 为输','2025-07-03 14:49:47');
INSERT INTO "entities" VALUES('register_dsp_mod','DSP_MOD','register',0.9,242,'{ 2 } \right)$ 到 DSP_ARCTAN 和 DSP_MOD 寄存器。','2025-07-03 14:49:47');
INSERT INTO "entities" VALUES('address_0x5555','0x5555','address',0.9,261,'R5 0x5555 # Assign 0x5555 to R5   
R1 2','2025-07-03 14:49:47');
INSERT INTO "entities" VALUES('address_0x01000100','0x01000100','address',0.9,262,'以如下 DATA MEM 内容为例，第一行数据 0x01000100 对应 0x0 地址，第二行数据 0x30005000对应 ','2025-07-03 14:49:47');
INSERT INTO "entities" VALUES('address_0x0003fff8','0x0003FFF8','address',0.9,262,'行数据 0x30005000对应 0x1 地址，第三行数据 0x0003FFF8 对应 $0 \mathrm { x } 2$ 地址。','2025-07-03 14:49:47');
INSERT INTO "entities" VALUES('address_0xffffe000','0xFFFFE000','address',0.9,262,' R3 的 32bit 数据写入 0x3 地址并覆盖掉数据 0xFFFFE000。','2025-07-03 14:49:47');
INSERT INTO "entities" VALUES('address_0x30005000','0x30005000','address',0.9,262,'式进行计算。以如下 DATA MEM 内容为例，第二行数据 0x30005000 对应的 CPU 寻址地址为 0x4001_4804，第三行','2025-07-03 14:49:47');
INSERT INTO "entities" VALUES('address_0x00100010','0x00100010','address',0.9,262,'0x00100010   
0x30005000   
0x0003FFF8  ','2025-07-03 14:49:47');
INSERT INTO "entities" VALUES('address_0x30004000','0x30004000','address',0.9,262,' 
0x0003FFF8   
0xFFFFE000   
0x30004000   
0x7FFFFFFF   
0x7FFFFFFF  ','2025-07-03 14:49:47');
INSERT INTO "entities" VALUES('address_0x7fffffff','0x7FFFFFFF','address',0.9,262,' 
0xFFFFE000   
0x30004000   
0x7FFFFFFF   
0x7FFFFFFF   
0xF0000003  ','2025-07-03 14:49:47');
INSERT INTO "entities" VALUES('address_0xf0000003','0xF0000003','address',0.9,262,' 
0x7FFFFFFF   
0x7FFFFFFF   
0xF0000003   
0x00007FFF # 8   
0x000080','2025-07-03 14:49:47');
INSERT INTO "entities" VALUES('address_0x00007fff','0x00007FFF','address',0.9,262,' 
0x7FFFFFFF   
0xF0000003   
0x00007FFF # 8   
0x00008000   
0x800000','2025-07-03 14:49:47');
INSERT INTO "entities" VALUES('address_0x00008000','0x00008000','address',0.9,262,'F0000003   
0x00007FFF # 8   
0x00008000   
0x80000000   
0x00000000','2025-07-03 14:49:47');
INSERT INTO "entities" VALUES('address_0x80000000','0x80000000','address',0.9,262,'00007FFF # 8   
0x00008000   
0x80000000   
0x00000000','2025-07-03 14:49:47');
INSERT INTO "entities" VALUES('register_i2c_data','I2C_DATA','register',0.9,269,'地址匹配后，从发送器将字节从 I2C_DATA 寄存器经由内部移位寄存器发送到 SDA 线上。在I2C_DATA ','2025-07-03 14:49:47');
INSERT INTO "entities" VALUES('register_i2c_mscr','I2C_MSCR','register',0.9,270,'2C 接口执行主模式传输之前，需要判断总线是否空闲。可读取 I2C_MSCR 寄存器的 BIT3，查','2025-07-03 14:49:47');
INSERT INTO "entities" VALUES('register_i2c_scr','I2C_SCR','register',0.9,278,'一般，进入中断后，需读取 I2C_SCR 寄存器，获得当前 I2C 总线状态及当前传输处于什么阶段；然后，对','2025-07-03 14:49:47');
INSERT INTO "entities" VALUES('bit_field_cfg','CFG','bit_field',0.9,284,'CFG[7:6]配置为2，半双工发送模式有效。此时，本接口只能发送数据。GP','2025-07-03 14:49:47');
INSERT INTO "entities" VALUES('register_tx_data','TX_DATA','register',0.9,288,'配置完毕。注意 SIZE 只能配置为 1。  
CPU 对 TX_DATA 寄存器执行写操作，触发 SPI 接口进入发送流程，发送的数据来自 ','2025-07-03 14:49:47');
INSERT INTO "entities" VALUES('bit_field_baud','BAUD','bit_field',0.9,289,'SPI 接口时钟通过对系统时钟分频获得，分频系数来自 BAUD[5:0]。分频范围是 $1 \sim 1 2 8$ ，对应的 BAU','2025-07-03 14:49:47');
INSERT INTO "entities" VALUES('bit_field_spi_cfg','SPI_CFG','bit_field',0.9,290,'SPI_CFG[3:2]对应的通讯波形极性和相位可参考 17.3.2.4。','2025-07-03 14:49:47');
INSERT INTO "entities" VALUES('bit_field_fil_clk_div16','FIL_CLK_DIV16','bit_field',0.9,295,' 表示打开，0 表示关闭；通过配置寄存器 CMP_TCLK.FIL_CLK_DIV16[7:4]可以配置滤波时钟，数值设置范围是 $_ { 0 \sim 1','2025-07-03 14:49:47');
INSERT INTO "entities" VALUES('bit_field_it_cm','IT_CM','bit_field',0.9,295,' 可 通 过 配 置 寄 存 器 SYS_AFE_REG1.IT_CM[1:0] 设 置 为$0 . 1 5 \mathrm { u S }','2025-07-03 14:49:47');
INSERT INTO "entities" VALUES('bit_field_cmpx_selp','CMPx_SELP','bit_field',0.9,295,'可 以 通 过 配 置 寄 存 器SYS_AFE_REG3.CMPx_SELP[2:0]进行设置，负端有 4 种信号来源可选择，可以通过配置寄存器 ','2025-07-03 14:49:47');
INSERT INTO "entities" VALUES('bit_field_cmpx_seln','CMPx_SELN','bit_field',0.9,295,'号来源可选择，可以通过配置寄存器 SYS_AFE_REG3.CMPx_SELN[1:0]进行设置。','2025-07-03 14:49:47');
INSERT INTO "entities" VALUES('register_cmp_cfg','CMP_CFG','register',0.9,299,'控制信号。但比较器自身的中断信号产生于开窗控制无关，仅仅受 CMP_CFG 寄存器影响。','2025-07-03 14:49:47');
INSERT INTO "entities" VALUES('bit_field_blcwin','BLCWIN','bit_field',0.9,301,'通常 CMP_ BLCWIN[3:0]或 CMP_ BLCWIN[7:4]中有 1bit 为 1，','2025-07-03 14:49:47');
INSERT INTO "entities" VALUES('parameter_29','29','parameter',0.9,302,' 11 位 ID 格式；2.0B 包含了 11 位ID 和 29 位 ID。','2025-07-03 14:49:47');
INSERT INTO "entities" VALUES('register_can_eir','CAN_EIR','register',0.9,304,'CAN 接口包含中断事件比较多，在CAN_IR 和 CAN_EIR 寄存器描述中有相应说明。根据实际使用情况，开启对应的中断事件使能开','2025-07-03 14:49:47');
INSERT INTO "entities" VALUES('register_btr0','BTR0','register',0.9,305,'成。CAN_BTR0 主要是配置TQ 参数（TQ 的计算见 BTR0 寄存器说明），CAN_BTR1 主要处理1-bit 数据的采样点、','2025-07-03 14:49:47');
INSERT INTO "entities" VALUES('register_mask','MASK','register',0.9,307,'CAN_ACR 列出了一个特定的 ID，CAN_AMR 为 MASK 寄存器，标识 CAN_ACR 中对应位数据同接收到的ID 对应位数','2025-07-03 14:49:47');
INSERT INTO "entities" VALUES('bit_field_acr1','ACR1','bit_field',0.9,308,'用到，对应的 AMR 需配置为全 1。例如滤波 ID1 的 ACR1[3:0]和 ACR[3:0]。  
RTR 是否需要匹配，结合实际情','2025-07-03 14:49:47');
INSERT INTO "entities" VALUES('bit_field_acr','ACR','bit_field',0.9,308,'需配置为全 1。例如滤波 ID1 的 ACR1[3:0]和 ACR[3:0]。  
RTR 是否需要匹配，结合实际情况配置AMR。','2025-07-03 14:49:47');
INSERT INTO "entities" VALUES('bit_field_acr3','ACR3','bit_field',0.9,308,'此时， ACR3[1:0]没有用到，对应的 AMR 需配置为全 1。','2025-07-03 14:49:47');
INSERT INTO "entities" VALUES('register_can_amc','CAN_AMC','register',0.9,312,'大数据量为8 个字节。RX FIFO 中有几个有效帧，通过 CAN_AMC 寄存器可知。通过读取 CAN_TXRX 寄存器可以获得最先被接收到','2025-07-03 14:49:47');
INSERT INTO "entities" VALUES('register_can_txrx','CAN_TXRX','register',0.9,312,'中有几个有效帧，通过 CAN_AMC 寄存器可知。通过读取 CAN_TXRX 寄存器可以获得最先被接收到的数据帧。若 RX FIFO 满了，抛弃','2025-07-03 14:49:47');
INSERT INTO "entities" VALUES('register_can_ewlr','CAN_EWLR','register',0.9,314,'N 总线错误，且在CAN 控制器进入被动错误状态之前被触发。CAN_EWLR 寄存器存储相应阈值，此寄存器的配置必须处于复位模式下。CAN_EW','2025-07-03 14:49:47');
INSERT INTO "entities" VALUES('register_can_ir','CAN_IR','register',0.9,318,'CAN_IR 寄存器，为读清除寄存器。只有 BIT0--RFIFO_N_EMPT','2025-07-03 14:49:47');
INSERT INTO "entities" VALUES('register_sys_wdt_clr','SYS_WDT_CLR','register',0.9,333,'PROTECT 写入 0xCAFE，开启 WatchDog SYS_WDT_CLR 寄存器写入。','2025-07-03 14:49:47');
INSERT INTO "entities" VALUES('address_0xcafe','0xCAFE','address',0.9,333,'看门狗清零前，需要向 SYS_WR_PROTECT 写入 0xCAFE，开启 WatchDog SYS_WDT_CLR 寄存器写入','2025-07-03 14:49:47');
CREATE TABLE relations (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                source_id TEXT NOT NULL,
                target_id TEXT NOT NULL,
                relation_type TEXT NOT NULL,
                confidence REAL,
                page INTEGER,
                context TEXT,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (source_id) REFERENCES entities (id),
                FOREIGN KEY (target_id) REFERENCES entities (id)
            );
CREATE INDEX idx_entity_type ON entities (type);
CREATE INDEX idx_entity_name ON entities (name);
CREATE INDEX idx_relation_type ON relations (relation_type);
DELETE FROM "sqlite_sequence";
COMMIT;
