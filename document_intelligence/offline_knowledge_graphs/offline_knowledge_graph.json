{"metadata": {"nodes": 239, "edges": 0, "density": 0, "export_time": 1751675002.56161, "database_size": 86016}, "nodes": [{"id": "module_adc", "name": "ADC", "type": "module", "confidence": 0.9, "page": 1, "context": "统 . 104.4 BGP 基准电压源 . . 104.5 ADC 模数转换器 . 114.6 OPA 运算放大器 . 114", "degree": 0}, {"id": "module_模数转换器", "name": "模数转换器", "type": "module", "confidence": 0.9, "page": 1, "context": "104.4 BGP 基准电压源 . . 104.5 ADC 模数转换器 . 114.6 OPA 运算放大器 . 114.7 CMP", "degree": 0}, {"id": "module_数模转换器", "name": "数模转换器", "type": "module", "confidence": 0.9, "page": 1, "context": ". 124.8 TMP 温度传感器 . 124.9 DAC 数模转换器 . 13  \n5 系统控制及时钟复位 .. .15", "degree": 0}, {"id": "module_定时器", "name": "定时器", "type": "module", "confidence": 0.9, "page": 1, "context": "器 .3.3 异常和中断 . . 43.4 SYSTICK 定时器.... 53.4.1 SYST_CSR 控制和状态寄存器.", "degree": 0}, {"id": "module_比较器", "name": "比较器", "type": "module", "confidence": 0.9, "page": 1, "context": ". 114.6 OPA 运算放大器 . 114.7 CMP 比较器 . 124.8 TMP 温度传感器 . 124.9 DAC", "degree": 0}, {"id": "parameter_电压", "name": "电压", "type": "parameter", "confidence": 0.9, "page": 1, "context": ".4.3 CLOCK 时钟系统 . 104.4 BGP 基准电压源 . . 104.5 ADC 模数转换器 . 114.6 ", "degree": 0}, {"id": "function_定时", "name": "定时", "type": "function", "confidence": 0.9, "page": 1, "context": "器 .3.3 异常和中断 . . 43.4 SYSTICK 定时器.... 53.4.1 SYST_CSR 控制和状态寄存器", "degree": 0}, {"id": "function_模数转换", "name": "模数转换", "type": "function", "confidence": 0.9, "page": 1, "context": "104.4 BGP 基准电压源 . . 104.5 ADC 模数转换器 . 114.6 OPA 运算放大器 . 114.7 CM", "degree": 0}, {"id": "function_放大", "name": "放大", "type": "function", "confidence": 0.9, "page": 1, "context": "104.5 ADC 模数转换器 . 114.6 OPA 运算放大器 . 114.7 CMP 比较器 . 124.8 TMP ", "degree": 0}, {"id": "function_比较", "name": "比较", "type": "function", "confidence": 0.9, "page": 1, "context": ". 114.6 OPA 运算放大器 . 114.7 CMP 比较器 . 124.8 TMP 温度传感器 . 124.9 DA", "degree": 0}, {"id": "function_数模转换", "name": "数模转换", "type": "function", "confidence": 0.9, "page": 1, "context": ". 124.8 TMP 温度传感器 . 124.9 DAC 数模转换器 . 13  \n5 系统控制及时钟复位 .. .15", "degree": 0}, {"id": "function_控制", "name": "控制", "type": "function", "confidence": 0.9, "page": 1, "context": "RM®CORTEXTM-M0 核心 ...3.2 NVIC 控制器 .3.3 异常和中断 . . 43.4 SYSTICK ", "degree": 0}, {"id": "function_中断", "name": "中断", "type": "function", "confidence": 0.9, "page": 1, "context": "M0 核心 ...3.2 NVIC 控制器 .3.3 异常和中断 . . 43.4 SYSTICK 定时器.... 53.4", "degree": 0}, {"id": "function_复位", "name": "复位", "type": "function", "confidence": 0.9, "page": 1, "context": "4.9 DAC 数模转换器 . 13  \n5 系统控制及时钟复位 .. .15", "degree": 0}, {"id": "function_保护", "name": "保护", "type": "function", "confidence": 0.9, "page": 2, "context": "存 35  \n5.3.23 SYS_WR_PROTECT 写保护寄存器. 35  \n5.3.24 SYS_AFE_DAC_A", "degree": 0}, {"id": "function_配置", "name": "配置", "type": "function", "confidence": 0.9, "page": 2, "context": " ...22  \n5.3.4 SYS_AFE_REG0 模拟配置寄存器0 .... 22  \n5.3.5 SYS_AFE_R", "degree": 0}, {"id": "module_flash", "name": "FLASH", "type": "module", "confidence": 0.9, "page": 3, "context": "6 FLASH. .38", "degree": 0}, {"id": "register_flash_cfg", "name": "FLASH_CFG", "type": "register", "confidence": 0.9, "page": 3, "context": " . 45  \n6.3.1 地址分配 45  \n6.3.2 FLASH_CFG 配置寄存器（推荐先读回，按或/与方式修改） 45  \n6.3.3 FL", "degree": 0}, {"id": "register_dma", "name": "DMA", "type": "register", "confidence": 0.9, "page": 4, "context": "1 地址分配 .. 56  \n7.6.2 DMA_CTRL DMA 控制寄存器. 56  \n7.6.3 DMA_IF DMA中断标志寄存器", "degree": 0}, {"id": "module_dma", "name": "DMA", "type": "module", "confidence": 0.9, "page": 4, "context": "1 地址分配 .. 56  \n7.6.2 DMA_CTRL DMA 控制寄存器. 56  \n7.6.3 DMA_IF DMA中", "degree": 0}, {"id": "module_gpio", "name": "GPIO", "type": "module", "confidence": 0.9, "page": 4, "context": "8 GPIO... .62", "degree": 0}, {"id": "register_crc", "name": "CRC", "type": "register", "confidence": 0.9, "page": 6, "context": "9.4.2.2 CRC_CR CRC 控制寄存器 83", "degree": 0}, {"id": "parameter_11", "name": "11", "type": "parameter", "confidence": 0.9, "page": 6, "context": ".2.10.4 ADC0_AMC_B1 17  \n10.2.11 通道0阈值寄存器 . 117  \n10.2.11.1 ADC0_", "degree": 0}, {"id": "register_timer0", "name": "Timer0", "type": "register", "confidence": 0.9, "page": 9, "context": "34  \n11.3.4.1 UTIMER_UNT0_CFG Timer0 配置寄存器 .. .....134  \n11.3.4.2 UTIMER", "degree": 0}, {"id": "register_timer1", "name": "Timer1", "type": "register", "confidence": 0.9, "page": 9, "context": "34  \n11.3.4.2 UTIMER_UNT1_CFG Timer1 配置寄存器 .. ...136  \n11.3.4.3 UTIMER_U", "degree": 0}, {"id": "register_timer2", "name": "Timer2", "type": "register", "confidence": 0.9, "page": 9, "context": "36  \n11.3.4.3 UTIMER_UNT2_CFG Timer2 配置寄存器 . ..137  \n11.3.4.4 UTIMER_UNT", "degree": 0}, {"id": "register_timer3", "name": "Timer3", "type": "register", "confidence": 0.9, "page": 9, "context": "37  \n11.3.4.4 UTIMER_UNT3_CFG Timer3 配置寄存器 .. ..138  \n11.3.4.5 UTIMER_UN", "degree": 0}, {"id": "module_timer", "name": "Timer", "type": "module", "confidence": 0.9, "page": 9, "context": "138  \n11.3.4.5 UTIMER_UNT0_TH Timer 0 门限寄存器 .. ...139  \n11.3.4.6 ", "degree": 0}, {"id": "module_计数器", "name": "计数器", "type": "module", "confidence": 0.9, "page": 9, "context": "\n11.2.3 模式.. . 126  \n11.2.3.1 计数器 .. .126  \n11.2.3.2 比较模式. .127", "degree": 0}, {"id": "function_滤波", "name": "滤波", "type": "function", "confidence": 0.9, "page": 9, "context": ".2 寄存器模块. ..124  \n11.1.1.3 IO 滤波模块.. .....125  \n11.1.1.4 通用定时器", "degree": 0}, {"id": "function_计数", "name": "计数", "type": "function", "confidence": 0.9, "page": 9, "context": "\n11.2.3 模式.. . 126  \n11.2.3.1 计数器 .. .126  \n11.2.3.2 比较模式. .12", "degree": 0}, {"id": "register_encoder0", "name": "Encoder0", "type": "register", "confidence": 0.9, "page": 10, "context": "******** UTIMER_ECD0_CFG  Encoder0 配置寄存器 .149  \n11.3.5.2 UTIMER_ECD0_T", "degree": 0}, {"id": "function_使能", "name": "使能", "type": "function", "confidence": 0.9, "page": 11, "context": "11.3.7.1 UTIMER_RE DMA 请求使能寄存器 153", "degree": 0}, {"id": "module_mcpwm", "name": "MCPWM", "type": "module", "confidence": 0.9, "page": 11, "context": "13 MCPWM . 161", "degree": 0}, {"id": "module_counter", "name": "Counter", "type": "module", "confidence": 0.9, "page": 11, "context": "13.1 概述 . .161  \n13.1.1 Base Counter 模块. 162  \n13.1.2 Fail 信号处理.. ", "degree": 0}, {"id": "function_设置", "name": "设置", "type": "function", "confidence": 0.9, "page": 11, "context": ". ..168  \n13.1.4.6 MCPWM IO 极性设置 . ..168  \n13.1.4.7 MCPWM IO 自", "degree": 0}, {"id": "module_uart", "name": "UART", "type": "module", "confidence": 0.9, "page": 13, "context": "14 UART . 192", "degree": 0}, {"id": "parameter_波特率", "name": "波特率", "type": "parameter", "confidence": 0.9, "page": 13, "context": "192  \n14.2.2 接收. 192  \n14.2.3 波特率配置 . 192  \n14.2.4 收发端口互换(TX/RX", "degree": 0}, {"id": "register_uartx", "name": "UARTx", "type": "register", "confidence": 0.9, "page": 13, "context": " 地址分 194   \n14.3.2 UARTx_CTRL UARTx 控制寄存器.. 194   \n14.3.3 UARTx_DIVH UA", "degree": 0}, {"id": "parameter_10", "name": "10", "type": "parameter", "confidence": 0.9, "page": 14, "context": " \n15.3.9.3 伪代码 .. .215  \n15.3.10 MACI (reserved) . 215  \n15.3.10.", "degree": 0}, {"id": "module_i2c", "name": "I2C", "type": "module", "confidence": 0.9, "page": 18, "context": "16 I2C . 227", "degree": 0}, {"id": "module_spi", "name": "SPI", "type": "module", "confidence": 0.9, "page": 19, "context": "I2C0_BCR DMA 传输控制寄存器 242  \n17 SPI.. 243  \n17.1 概述 . .243  \n17.2", "degree": 0}, {"id": "register_spi", "name": "SPI", "type": "register", "confidence": 0.9, "page": 20, "context": ".1 地址分配. 251  \n17.4.2 SPI_CFG SPI 控制寄存器. 251  \n17.4.3 SPI_IE SPI 中断寄存", "degree": 0}, {"id": "function_数据传输", "name": "数据传输", "type": "function", "confidence": 0.9, "page": 20, "context": "存器. 254  \n17.4.7 SPI_SIZE SPI 数据传输长度寄存器. 254", "degree": 0}, {"id": "module_can", "name": "CAN", "type": "module", "confidence": 0.9, "page": 21, "context": "19 CAN. 264", "degree": 0}, {"id": "register_fifo", "name": "FIFO", "type": "register", "confidence": 0.9, "page": 21, "context": "8 CAN_ RFIFO0\\~CAN_RFIFO31 RX FIFO 寄存器 ..288  \n19.2.3.2.19 CAN_TFIFO", "degree": 0}, {"id": "function_恢复", "name": "恢复", "type": "function", "confidence": 0.9, "page": 21, "context": ".......276  \n19.2.2.15 离线状态与离线恢复 . ..276  \n19.2.3 寄存器 .. .. 27", "degree": 0}, {"id": "register_sif_cfg", "name": "SIF_CFG", "type": "register", "confidence": 0.9, "page": 23, "context": "0.4.2 寄存器说明 .. 292  \n20.4.2.1 SIF_CFG 配置寄存器. 292  \n20.4.2.2 SIF_FREQ 波特率寄", "degree": 0}, {"id": "module_看门狗", "name": "看门狗", "type": "module", "confidence": 0.9, "page": 23, "context": "21 看门狗 .. 294", "degree": 0}, {"id": "register_vtor", "name": "VTOR", "type": "register", "confidence": 0.9, "page": 26, "context": "ctor 地址分配表 ... 42  \n表 6-3 IAP VTOR 寄存器描述 . 44  \n表 6-4 FLASH 控制器模块寄存器", "degree": 0}, {"id": "register_gpiox", "name": "GPIOx", "type": "register", "confidence": 0.9, "page": 26, "context": "寄存器 DMA_CMARx .. . 60  \n表 8-1 GPIOx 寄存器列表 . . 64  \n表8-2 GPIO 中断/唤醒/配置", "degree": 0}, {"id": "register_adc0", "name": "ADC0", "type": "register", "confidence": 0.9, "page": 26, "context": "输出数字量数制转换 ... ... 90  \n表 10-5 ADC0 寄存器列表 .. ......... 93  \n表 10-6 采样", "degree": 0}, {"id": "register_dsp", "name": "DSP", "type": "register", "confidence": 0.9, "page": 29, "context": "5-2 DSP 地址空间 .. .202  \n表 15-3 DSP 寄存器列表 .. ...203  \n表 15-4 DSP 状态控制", "degree": 0}, {"id": "register_i2c", "name": "I2C", "type": "register", "confidence": 0.9, "page": 29, "context": "SP 平方根寄存器 .......210  \n表 16-1 I2C 寄存器地址分配表 . .238  \n表 16-2 地址寄存器 I2", "degree": 0}, {"id": "register_can", "name": "CAN", "type": "register", "confidence": 0.9, "page": 29, "context": " 头信息 . ..........273  \n表 19-7 CAN 寄存器地址分配 .... .....276  \n表 19-8 模式", "degree": 0}, {"id": "register_utimer", "name": "UTIMER", "type": "register", "confidence": 0.9, "page": 29, "context": "用定时器配置寄存器地址分配 . .131  \n表 11-5 UTIMER 配置寄存器 UTIMER_CFG .. .133  \n表 11-6 滤", "degree": 0}, {"id": "register_mcpwm_th00", "name": "MCPWM_TH00", "type": "register", "confidence": 0.9, "page": 29, "context": "存在影子寄存器的寄存器 ... .171  \n表 13-5 MCPWM_TH00 配置寄存器..... ...171  \n表 13-6 MCPWM_TH", "degree": 0}, {"id": "register_mcpwm_th01", "name": "MCPWM_TH01", "type": "register", "confidence": 0.9, "page": 29, "context": "00 配置寄存器..... ...171  \n表 13-6 MCPWM_TH01 配置寄存器........ ........172  \n表 13-7 ", "degree": 0}, {"id": "register_mcpwm_th10", "name": "MCPWM_TH10", "type": "register", "confidence": 0.9, "page": 29, "context": "........ ........172  \n表 13-7 MCPWM_TH10 配置寄存器...... ....172  \n表 13-8 MCPWM_", "degree": 0}, {"id": "register_mcpwm_th11", "name": "MCPWM_TH11", "type": "register", "confidence": 0.9, "page": 29, "context": " 配置寄存器...... ....172  \n表 13-8 MCPWM_TH11 配置寄存器..... .....173  \n表 13-9 MCPWM_", "degree": 0}, {"id": "register_mcpwm_th20", "name": "MCPWM_TH20", "type": "register", "confidence": 0.9, "page": 29, "context": " 配置寄存器..... .....173  \n表 13-9 MCPWM_TH20 配置寄存器... .173  \n表 13-10 MCPWM_TH21 ", "degree": 0}, {"id": "register_mcpwm_th21", "name": "MCPWM_TH21", "type": "register", "confidence": 0.9, "page": 29, "context": "_TH20 配置寄存器... .173  \n表 13-10 MCPWM_TH21 配置寄存器 ... .174  \n表 13-11 MCPWM_TH30", "degree": 0}, {"id": "register_mcpwm_th30", "name": "MCPWM_TH30", "type": "register", "confidence": 0.9, "page": 29, "context": "TH21 配置寄存器 ... .174  \n表 13-11 MCPWM_TH30 配置寄存器 .. .174  \n表 13-12 MCPWM_TH31 ", "degree": 0}, {"id": "register_mcpwm_th31", "name": "MCPWM_TH31", "type": "register", "confidence": 0.9, "page": 29, "context": "_TH30 配置寄存器 .. .174  \n表 13-12 MCPWM_TH31 配置寄存器 .. .174  \n表 13-13 MCPWM_TMR0 ", "degree": 0}, {"id": "register_mcpwm_tmr0", "name": "MCPWM_TMR0", "type": "register", "confidence": 0.9, "page": 29, "context": "_TH31 配置寄存器 .. .174  \n表 13-13 MCPWM_TMR0 配置寄存器 .. .175  \n表 13-14 MCPWM_TMR1 ", "degree": 0}, {"id": "register_mcpwm_tmr1", "name": "MCPWM_TMR1", "type": "register", "confidence": 0.9, "page": 29, "context": "_TMR0 配置寄存器 .. .175  \n表 13-14 MCPWM_TMR1 配置寄存器 ... .......175  \n表 13-15 MCPW", "degree": 0}, {"id": "register_mcpwm_tmr2", "name": "MCPWM_TMR2", "type": "register", "confidence": 0.9, "page": 29, "context": "置寄存器 ... .......175  \n表 13-15 MCPWM_TMR2 配置寄存器 ... .................176  \n表 ", "degree": 0}, {"id": "register_mcpwm_tmr3", "name": "MCPWM_TMR3", "type": "register", "confidence": 0.9, "page": 29, "context": "................176  \n表 13-16 MCPWM_TMR3 配置寄存器 ... ....176  \n表 13-17 MCPWM_T", "degree": 0}, {"id": "register_mcpwm_th", "name": "MCPWM_TH", "type": "register", "confidence": 0.9, "page": 29, "context": "3 配置寄存器 ... ....176  \n表 13-17 MCPWM_TH 配置寄存器 ..  \n表 13-18 MCPWM_UPDATE 配置寄", "degree": 0}, {"id": "register_mcpwm_update", "name": "MCPWM_UPDATE", "type": "register", "confidence": 0.9, "page": 29, "context": "7 MCPWM_TH 配置寄存器 ..  \n表 13-18 MCPWM_UPDATE 配置寄存器 .. .177  \n表 13-19 MCPWM_IE 配置", "degree": 0}, {"id": "register_mcpwm_ie", "name": "MCPWM_IE", "type": "register", "confidence": 0.9, "page": 29, "context": "PDATE 配置寄存器 .. .177  \n表 13-19 MCPWM_IE 配置寄存器 ... ......178  \n表 13-20 MCPWM", "degree": 0}, {"id": "register_mcpwm_if", "name": "MCPWM_IF", "type": "register", "confidence": 0.9, "page": 29, "context": "配置寄存器 ... ......178  \n表 13-20 MCPWM_IF 配置寄存器 . 179  \n表 13-21 MCPWM_EIE 配置寄", "degree": 0}, {"id": "register_mcpwm_eie", "name": "MCPWM_EIE", "type": "register", "confidence": 0.9, "page": 29, "context": "CPWM_IF 配置寄存器 . 179  \n表 13-21 MCPWM_EIE 配置寄存器 . .180  \n表 13-22 MCPWM_EIF 配置", "degree": 0}, {"id": "register_mcpwm_eif", "name": "MCPWM_EIF", "type": "register", "confidence": 0.9, "page": 29, "context": "WM_EIE 配置寄存器 . .180  \n表 13-22 MCPWM_EIF 配置寄存器 . .181  \n表 13-23 MCPWM_RE 配置寄", "degree": 0}, {"id": "register_mcpwm_re", "name": "MCPWM_RE", "type": "register", "confidence": 0.9, "page": 29, "context": "WM_EIF 配置寄存器 . .181  \n表 13-23 MCPWM_RE 配置寄存器 .. ...182  \n表 13-24 MCPWM_PP ", "degree": 0}, {"id": "register_mcpwm_pp", "name": "MCPWM_PP", "type": "register", "confidence": 0.9, "page": 29, "context": "_RE 配置寄存器 .. ...182  \n表 13-24 MCPWM_PP 配置寄存器 ....... .....................", "degree": 0}, {"id": "register_mcpwm_io01", "name": "MCPWM_IO01", "type": "register", "confidence": 0.9, "page": 29, "context": "................182  \n表 13-25 MCPWM_IO01 配置寄存器 ..... .....183  \n表 13-26 MCPW", "degree": 0}, {"id": "register_mcpwm_io23", "name": "MCPWM_IO23", "type": "register", "confidence": 0.9, "page": 29, "context": "置寄存器 ..... .....183  \n表 13-26 MCPWM_IO23 配置寄存器 .. .........184  \n表 13-27 MCP", "degree": 0}, {"id": "register_mcpwm_sdcfg", "name": "MCPWM_SDCFG", "type": "register", "confidence": 0.9, "page": 29, "context": "寄存器 .. .........184  \n表 13-27 MCPWM_SDCFG 配置寄存器... ...185  \n表 13-28 MCPWM_TCL", "degree": 0}, {"id": "register_mcpwm_tclk", "name": "MCPWM_TCLK", "type": "register", "confidence": 0.9, "page": 29, "context": "CFG 配置寄存器... ...185  \n表 13-28 MCPWM_TCLK 配置寄存器 . .......185  \n表 13-29 MCPWM_", "degree": 0}, {"id": "register_mcpwm_fail", "name": "MCPWM_FAIL", "type": "register", "confidence": 0.9, "page": 29, "context": " 配置寄存器 . .......185  \n表 13-29 MCPWM_FAIL 配置寄存器 . .186  \n表 13-30 MCPWM_PRT 配置", "degree": 0}, {"id": "register_mcpwm_prt", "name": "MCPWM_PRT", "type": "register", "confidence": 0.9, "page": 29, "context": "M_FAIL 配置寄存器 . .186  \n表 13-30 MCPWM_PRT 配置寄存器 .. ...187  \n表 13-31 MCPWM_CNT", "degree": 0}, {"id": "register_mcpwm_cnt", "name": "MCPWM_CNT", "type": "register", "confidence": 0.9, "page": 29, "context": "PRT 配置寄存器 .. ...187  \n表 13-31 MCPWM_CNT 配置寄存器.. .188  \n表 13-32 MCPWM_DTH00 ", "degree": 0}, {"id": "register_mcpwm_dth00", "name": "MCPWM_DTH00", "type": "register", "confidence": 0.9, "page": 29, "context": "WM_CNT 配置寄存器.. .188  \n表 13-32 MCPWM_DTH00 配置寄存器 . .188  \n表 13-33 MCPWM_DTH01 ", "degree": 0}, {"id": "register_mcpwm_dth01", "name": "MCPWM_DTH01", "type": "register", "confidence": 0.9, "page": 29, "context": "_DTH00 配置寄存器 . .188  \n表 13-33 MCPWM_DTH01 配置寄存器 .189  \n表 13-34 MCPWM_DTH10 配置", "degree": 0}, {"id": "register_mcpwm_dth10", "name": "MCPWM_DTH10", "type": "register", "confidence": 0.9, "page": 29, "context": "WM_DTH01 配置寄存器 .189  \n表 13-34 MCPWM_DTH10 配置寄存器 . .189  \n表 13-35 MCPWM_DTH11 ", "degree": 0}, {"id": "register_mcpwm_dth11", "name": "MCPWM_DTH11", "type": "register", "confidence": 0.9, "page": 29, "context": "_DTH10 配置寄存器 . .189  \n表 13-35 MCPWM_DTH11 配置寄存器 . .190  \n表 13-36 MCPWM_DTH20 ", "degree": 0}, {"id": "register_mcpwm_dth20", "name": "MCPWM_DTH20", "type": "register", "confidence": 0.9, "page": 29, "context": "_DTH11 配置寄存器 . .190  \n表 13-36 MCPWM_DTH20 配置寄存器 ... ...190  \n表 13-37 MCPWM_DT", "degree": 0}, {"id": "register_mcpwm_dth21", "name": "MCPWM_DTH21", "type": "register", "confidence": 0.9, "page": 29, "context": "20 配置寄存器 ... ...190  \n表 13-37 MCPWM_DTH21 配置寄存器 ....... .........190  \n表 13-3", "degree": 0}, {"id": "register_mcpwm_dth30", "name": "MCPWM_DTH30", "type": "register", "confidence": 0.9, "page": 29, "context": "...... .........190  \n表 13-38 MCPWM_DTH30 配置寄存器 .... ...191  \n表 13-39 MCPWM_D", "degree": 0}, {"id": "register_mcpwm_dth31", "name": "MCPWM_DTH31", "type": "register", "confidence": 0.9, "page": 29, "context": "0 配置寄存器 .... ...191  \n表 13-39 MCPWM_DTH31 配置寄存器 ......... ........191  \n表 14-", "degree": 0}, {"id": "register_spi_div", "name": "SPI_DIV", "type": "register", "confidence": 0.9, "page": 29, "context": "I_IE 中断寄存器 .. ...252  \n表 17-4 SPI_DIV 控制寄存器.. .253  \n表 17-5 SPI_TX_DATA 数", "degree": 0}, {"id": "register_volatile", "name": "Volatile", "type": "register", "confidence": 0.9, "page": 39, "context": "t，即上电复位，芯片系统上电时产生的复位信号NVR：Non-Volatile Register，flash 中区别于 main 区域之外的一块存储区域", "degree": 0}, {"id": "module_wdt", "name": "WDT", "type": "module", "confidence": 0.9, "page": 39, "context": "er，数模转换器  \nBGP：Bandgap，带隙基准  \nWDT：Watch dog，看门狗  \nLSI：Low Speed", "degree": 0}, {"id": "parameter_32", "name": "32", "type": "parameter", "confidence": 0.9, "page": 39, "context": "SI：Low Speed Internal Clock，即 32kHZ RC 时钟  \nHSI：High Speed Intern", "degree": 0}, {"id": "parameter_96", "name": "96", "type": "parameter", "confidence": 0.9, "page": 39, "context": " \nPLL：Phase Lock Loop Clock，即 96MHz 锁相环时钟，通常用作系统高速时钟POR：Power-On ", "degree": 0}, {"id": "parameter_16", "name": "16", "type": "parameter", "confidence": 0.9, "page": 39, "context": "字：32 位数据/指令。  \n半字：16 位数据/指令。  \n字节：8 位数据。  \n双字：64 位数据", "degree": 0}, {"id": "parameter_64", "name": "64", "type": "parameter", "confidence": 0.9, "page": 39, "context": "字：16 位数据/指令。  \n字节：8 位数据。  \n双字：64 位数据。  \nADC：Analog-Digital Conve", "degree": 0}, {"id": "function_暂停", "name": "暂停", "type": "function", "confidence": 0.9, "page": 41, "context": "级与当前的优先级进行比较，如果新的优先级更高，当前的任务会被暂停，处理器进行核心寄存器入栈保持，然后开始处理新的异常程序，这", "degree": 0}, {"id": "function_停止", "name": "停止", "type": "function", "confidence": 0.9, "page": 42, "context": "异常，会引起程序控制的变化。在异常发生时，处理器停止当前的任务，转而执行异常处理程序，异常处理完成后，会继续执行", "degree": 0}, {"id": "parameter_频率", "name": "频率", "type": "parameter", "confidence": 0.9, "page": 43, "context": "可编程设置频率的RTOS 定时器(例如 $\\lvert 1 0 0 \\ma", "degree": 0}, {"id": "address_0x00000000", "name": "0x00000000", "type": "address", "confidence": 0.9, "page": 43, "context": "地址：0xE000_E010  \n复位值：0x00000000", "degree": 0}, {"id": "parameter_精度", "name": "精度", "type": "parameter", "confidence": 0.9, "page": 46, "context": "1 . 2 \\mathrm { V } 0 . 8 \\%$ 精度电压基准源", "degree": 0}, {"id": "parameter_12", "name": "12", "type": "parameter", "confidence": 0.9, "page": 46, "context": "集成 1 路同步双采样的 12bit SAR ADC，采样及转换速率 3Msps。最多 20 通", "degree": 0}, {"id": "parameter_20", "name": "20", "type": "parameter", "confidence": 0.9, "page": 46, "context": "2bit SAR ADC，采样及转换速率 3Msps。最多 20 通道  \n集成4 路运算放大器，可设置为 PGA 模式  \n集成", "degree": 0}, {"id": "function_监测", "name": "监测", "type": "function", "confidence": 0.9, "page": 47, "context": "POR 模块监测AVDD 的电压，在AVDD 电压低于3.0V 时（例如上电", "degree": 0}, {"id": "bit_field_pvdsel", "name": "PVDSEL", "type": "bit_field", "confidence": 0.9, "page": 48, "context": "PVDSEL[1:0]/ PD_PDT 的说明见模拟寄存器 SYS_AFE_REG", "degree": 0}, {"id": "parameter_电流", "name": "电流", "type": "parameter", "confidence": 0.9, "page": 48, "context": " 关闭），RC 时钟需要BGP 电压基准源模块提供基准电压和电流，因此开启RC 时钟需要先开启BGP 模块（ $\\mathr", "degree": 0}, {"id": "module_pwm", "name": "PWM", "type": "module", "confidence": 0.9, "page": 48, "context": "给 CPU、ADC 等模块提供更高速的工作时钟。CPU 和 PWM 模块的最高时钟为96MHz，ADC 模块最高时钟 $4 8", "degree": 0}, {"id": "bit_field_adclksel", "name": "ADCLKSEL", "type": "bit_field", "confidence": 0.9, "page": 48, "context": "\\mathrm { { M H z } }$ ，通过寄存器 ADCLKSEL[1:0]可设置不同的 ADC工作频率。", "degree": 0}, {"id": "parameter_15", "name": "15", "type": "parameter", "confidence": 0.9, "page": 48, "context": "入一个晶体，且在OSC_IN/OSC_OUT 引脚各接一个 15pF 电容到地，设置 XTALPDN $_ { = 1 }$ 即", "degree": 0}, {"id": "bit_field_res_opax", "name": "RES_OPAx", "type": "bit_field", "confidence": 0.9, "page": 49, "context": "rm { R } } _ { 1 }$ 的阻值可通过寄存器 RES_OPAx[1:0]设置，以实现不同的放大倍数。", "degree": 0}, {"id": "parameter_85", "name": "85", "type": "parameter", "confidence": 0.9, "page": 51, "context": "寄存器 DAC_GAIN<1:0>设置为1.2V/3V/4.85V", "degree": 0}, {"id": "parameter_50", "name": "50", "type": "parameter", "confidence": 0.9, "page": 51, "context": "5 \\mathrm { k } \\Omega$ 的负载电阻和50pF 的负载电容。", "degree": 0}, {"id": "register_sys_afe_dac_amc", "name": "SYS_AFE_DAC_AMC", "type": "register", "confidence": 0.9, "page": 51, "context": "YS_AFE_DAC 填入值（理想值对应数字量）。a 来自 SYS_AFE_DAC_AMC 寄存器，b 来自SYS_AFE_DAC_DC。硬件根据 SYS_A", "degree": 0}, {"id": "address_0x00000340", "name": "0x00000340", "type": "address", "confidence": 0.9, "page": 51, "context": " 0 0 3 3 0$ ，为 3V 量程的 a 参数，地址 0x00000340，为 3V 量程的 b 参数。", "degree": 0}, {"id": "address_0x00000334", "name": "0x00000334", "type": "address", "confidence": 0.9, "page": 51, "context": "地址 0x00000334，为 1.2V 量程的 a 参数，地址 0x00000344", "degree": 0}, {"id": "address_0x00000344", "name": "0x00000344", "type": "address", "confidence": 0.9, "page": 51, "context": "0x00000334，为 1.2V 量程的 a 参数，地址 0x00000344，为 1.2V 量程的 b 参数。", "degree": 0}, {"id": "address_0x00000338", "name": "0x00000338", "type": "address", "confidence": 0.9, "page": 51, "context": "地址 0x00000338，为 4.85V 量程的 a 参数，地址 0x0000034", "degree": 0}, {"id": "address_0x00000348", "name": "0x00000348", "type": "address", "confidence": 0.9, "page": 51, "context": "x00000338，为 4.85V 量程的 a 参数，地址 0x00000348，为 4.85V 量程的 b 参数。", "degree": 0}, {"id": "register_sys_clk_cfg", "name": "SYS_CLK_CFG", "type": "register", "confidence": 0.9, "page": 54, "context": "MCLK 是系统主时钟。可以通过 SYS_CLK_CFG 寄存器 CLK_DIV 位域控制进行 $\\mathrm { n }", "degree": 0}, {"id": "register_sys_clk_fen", "name": "SYS_CLK_FEN", "type": "register", "confidence": 0.9, "page": 54, "context": "MCLK 时钟经过 SYS_CLK_FEN 寄存器控制的开关之后供给外设。I2C 时钟由 SYS_CLK_DI", "degree": 0}, {"id": "register_sys_clk_div0", "name": "SYS_CLK_DIV0", "type": "register", "confidence": 0.9, "page": 54, "context": "LK_FEN 寄存器控制的开关之后供给外设。I2C 时钟由 SYS_CLK_DIV0 寄存器控制可以进一步分频，UART 时钟由SYS_CLK_DIV2", "degree": 0}, {"id": "parameter_48", "name": "48", "type": "parameter", "confidence": 0.9, "page": 54, "context": "的 $2 / 4 / 8$ 分频后送至 ADC（典型工作频率48MHz），即 ACLK。", "degree": 0}, {"id": "register_sys_clk_slp", "name": "SYS_CLK_SLP", "type": "register", "confidence": 0.9, "page": 55, "context": "向 SYS_CLK_SLP 寄存器写入 0xDEAD 可以令芯片准备进入休眠状态，之后立刻执行", "degree": 0}, {"id": "address_0xdead", "name": "0xDEAD", "type": "address", "confidence": 0.9, "page": 55, "context": "向 SYS_CLK_SLP 寄存器写入 0xDEAD 可以令芯片准备进入休眠状态，之后立刻执行__WFI()宏指", "degree": 0}, {"id": "bit_field_p0", "name": "P0", "type": "bit_field", "confidence": 0.9, "page": 55, "context": "仅有 P0[1:0]、P1[1:0]四个 IO 可以作为外部唤醒 IO 使用，可", "degree": 0}, {"id": "bit_field_p1", "name": "P1", "type": "bit_field", "confidence": 0.9, "page": 55, "context": "仅有 P0[1:0]、P1[1:0]四个 IO 可以作为外部唤醒 IO 使用，可以配置独立的使能", "degree": 0}, {"id": "register_sys_rst_src", "name": "SYS_RST_SRC", "type": "register", "confidence": 0.9, "page": 57, "context": "SYS_RST_SRC 寄存器用于保存硬件复位事件，当某个硬件复位发生后，SYS_RST_", "degree": 0}, {"id": "register_sys_clr_rst", "name": "SYS_CLR_RST", "type": "register", "confidence": 0.9, "page": 57, "context": "_RST_SRC 寄存器本身无法被复位信号复位，只能通过向 SYS_CLR_RST 寄存器写入 0xCA40清空记录，复位记录可以方便地了解是否发生以", "degree": 0}, {"id": "bit_field_sys_clk_cfg", "name": "SYS_CLK_CFG", "type": "bit_field", "confidence": 0.9, "page": 69, "context": "当使用4MHz HSI 时钟作为系统主时钟时，SYS_CLK_CFG[7:0]的分频系数无效，最终输出的系统时钟频率即为4MHz。", "degree": 0}, {"id": "address_0xffffffff", "name": "0xFFFFFFFF", "type": "address", "confidence": 0.9, "page": 76, "context": "Flash 数据防窃取（最后一个 word 须写入非 0xFFFFFFFF 的任意值）", "degree": 0}, {"id": "register_flash", "name": "FLASH", "type": "register", "confidence": 0.9, "page": 77, "context": "\nFLASH 的读取加速操作，以提升芯片整体运行效率。  \nFLASH 控制寄存器的访问。", "degree": 0}, {"id": "parameter_24", "name": "24", "type": "parameter", "confidence": 0.9, "page": 79, "context": "配置 FLASH_CFG.TBS 的值，以实现 48MHz/24MHz和 12MHz 的计数值（其它频率暂不支持）。最终保证计数值", "degree": 0}, {"id": "register_flash_wdata", "name": "FLASH_WDATA", "type": "register", "confidence": 0.9, "page": 79, "context": "G.ADR_INC，开启地址自动递增模式，后续只需要反复写 FLASH_WDATA 寄存器即可，FLASH_ADDR 每次写入一次数据会自动增加 $0", "degree": 0}, {"id": "address_0x7654dcba", "name": "0x7654DCBA", "type": "address", "confidence": 0.9, "page": 81, "context": "ASH_ADDR 的值将失效。FLASH_ERASE 写入 0x7654DCBA 触发擦除操作。", "degree": 0}, {"id": "register_flash_protect", "name": "FLASH_PROTECT", "type": "register", "confidence": 0.9, "page": 81, "context": "，执行最后一个 WORD 的编程，写入非全 1 的值，读取 FLASH_PROTECT 寄存器，即触发一次加密状态更新，完成加密（读取FLASH_PROT", "degree": 0}, {"id": "module_ram", "name": "RAM", "type": "module", "confidence": 0.9, "page": 83, "context": "如果需要将flash 全部擦除，则需要将在线升级函数放置在 RAM 中，如果需要使用中断则新的中断向量入口地址也需要位于RAM", "degree": 0}, {"id": "address_0x00000060", "name": "0x00000060", "type": "address", "confidence": 0.9, "page": 83, "context": "地址：0x4000_0400  \n复位值：0x00000060", "degree": 0}, {"id": "function_禁用", "name": "禁用", "type": "function", "confidence": 0.9, "page": 90, "context": "A 模块可以通过设置 DMA_CTRL.EN 位为 0 来被禁用（要求关闭 DMA使能前先关闭4 个通道对应的使能 DMA_", "degree": 0}, {"id": "bit_field_req_en", "name": "REQ_EN", "type": "bit_field", "confidence": 0.9, "page": 96, "context": " _ { 1 } }$ 以通道 0 为例，DMA_CCR0.REQ_EN[2:0]分别为 Timer1、Timer0、ADC0 的 DMA 请", "degree": 0}, {"id": "register_dma_ctmsx", "name": "DMA_CTMSx", "type": "register", "confidence": 0.9, "page": 97, "context": "DMA_CTMSx 寄存器只有在通道禁用，即 DMA_CCRx.EN $= 0$ 之后", "degree": 0}, {"id": "function_启动", "name": "启动", "type": "function", "confidence": 0.9, "page": 97, "context": "通常在开启DMA 之前需要将触发 DMA 启动的外设接收中断标志位清零，防止之前留下的中断标志位成为DMA", "degree": 0}, {"id": "bit_field_peri_addr", "name": "PERI_ADDR", "type": "bit_field", "confidence": 0.9, "page": 98, "context": "时，即配置为以 32bit 为单位搬运外设数据。CPARx.PERI_ADDR[1:0]值无效，外设地址会以4 为单位递增。", "degree": 0}, {"id": "register_dma_cparx", "name": "DMA_CPARx", "type": "register", "confidence": 0.9, "page": 98, "context": "注意：DMA_CPARx 寄存器只有在通道禁用，即 DMA_CCRx.EN $= 0$ 之后", "degree": 0}, {"id": "register_sys", "name": "SYS", "type": "register", "confidence": 0.9, "page": 98, "context": " \\mathrm { x } 4 0 0 0 0$ (对应 SYS 寄存器)或 $0 \\mathrm { x } 4 0 0 1 ^ ", "degree": 0}, {"id": "parameter_17", "name": "17", "type": "parameter", "confidence": 0.9, "page": 98, "context": "设间的数据搬运，因此 DMA_CPAR 只存储外设地址的低 17 位，高 15 位恒为 $0 \\mathrm { x } 2 0", "degree": 0}, {"id": "bit_field_mem_addr", "name": "MEM_ADDR", "type": "bit_field", "confidence": 0.9, "page": 99, "context": "时，即配置为以 32bit 为单位搬运内存数据。CMARx.MEM_ADDR[1:0]值无效，内存地址会以4 为单位递增。", "degree": 0}, {"id": "register_dma_cmarx", "name": "DMA_CMARx", "type": "register", "confidence": 0.9, "page": 99, "context": "注意：DMA_CMARx 寄存器只有在通道禁用，即 DMA_CCRx.EN=0 之后才可以写", "degree": 0}, {"id": "parameter_13", "name": "13", "type": "parameter", "confidence": 0.9, "page": 99, "context": "内存与外设间的数据搬运，因此DMA_CMAR 只存储地址的低13 位，对应SRAM 8kB地址空间。高19 位恒为 $0 \\ma", "degree": 0}, {"id": "parameter_19", "name": "19", "type": "parameter", "confidence": 0.9, "page": 99, "context": "R 只存储地址的低13 位，对应SRAM 8kB地址空间。高19 位恒为 $0 \\mathrm { x } 1 0 0 0 0$", "degree": 0}, {"id": "address_0x40012000", "name": "0x40012000", "type": "address", "confidence": 0.9, "page": 102, "context": "GPIO 0 模块在芯片中的基地址是 0x40012000。  \nGPIO 1 模块在芯片中的基地址是 0x40012", "degree": 0}, {"id": "address_0x40012040", "name": "0x40012040", "type": "address", "confidence": 0.9, "page": 102, "context": "0012000。  \nGPIO 1 模块在芯片中的基地址是 0x40012040。  \nGPIO 2 模块在芯片中的基地址是 0x40012", "degree": 0}, {"id": "address_0x40012080", "name": "0x40012080", "type": "address", "confidence": 0.9, "page": 102, "context": "0012040。  \nGPIO 2 模块在芯片中的基地址是 0x40012080。  \nGPIO 3 模块在芯片中的基地址是 0x40012", "degree": 0}, {"id": "address_0x400120c0", "name": "0x400120C0", "type": "address", "confidence": 0.9, "page": 102, "context": "0012080。  \nGPIO 3 模块在芯片中的基地址是 0x400120C0。  \nGPIO 0/1/2/3 的寄存器定义完全相同，仅基", "degree": 0}, {"id": "address_0x40012100", "name": "0x40012100", "type": "address", "confidence": 0.9, "page": 102, "context": "GPIO 中断/唤醒/配置锁定模块的基地址是 0x40012100，寄存器列表如下：", "degree": 0}, {"id": "address_0x4001", "name": "0x4001", "type": "address", "confidence": 0.9, "page": 105, "context": "地址分别是：0x4001_2010，0x4001_2050，0x4001-2090，0x4001_20D0  \n复位值：0x0", "degree": 0}, {"id": "address_0x3233", "name": "0x3233", "type": "address", "confidence": 0.9, "page": 116, "context": "SS or FAIL   \nif(GPIO0_PIE != 0x3233)FAIL;   \nif(GPIO1_PIE $\\ ! = ", "degree": 0}, {"id": "address_0x5ac4", "name": "0x5AC4", "type": "address", "confidence": 0.9, "page": 116, "context": "// write any value other than 0x5AC4 to enable lock protect GPIO0_", "degree": 0}, {"id": "address_0x0100", "name": "0x0100", "type": "address", "confidence": 0.9, "page": 116, "context": " 0 0 0 0$ ; if(GPIO0_LCKR  != 0x0100)FAIL;   \nif(GPIO1_LCKR != 0xF", "degree": 0}, {"id": "address_0xffff", "name": "0xFFFF", "type": "address", "confidence": 0.9, "page": 116, "context": "100)FAIL;   \nif(GPIO1_LCKR != 0xFFFF)FAIL;   \nif(GPIO2_LCKR $\\math", "degree": 0}, {"id": "address_0x0000", "name": "0x0000", "type": "address", "confidence": 0.9, "page": 116, "context": "\\$ )FAIL;   \nif(GPIO1_LCKR != 0x0000)FAIL;   \nif(GPIO2_LCKR != 0x0", "degree": 0}, {"id": "address_0x0080", "name": "0x0080", "type": "address", "confidence": 0.9, "page": 117, "context": " { \\tau } = \\mathbf { \\tau }$ 0x0080; // 使能 P0[7]输入  \nNVIC_EnableI", "degree": 0}, {"id": "function_通信", "name": "通信", "type": "function", "confidence": 0.9, "page": 118, "context": "码（Cyclic Redundancy Check）：是数据通信领域中最常用的一种查错校验码，其特征是信息字段和校验字段的长", "degree": 0}, {"id": "register_crc_dr", "name": "CRC_DR", "type": "register", "confidence": 0.9, "page": 121, "context": "CRC_DR 寄存器既用于放入待校验数据，也用于返回校验结果。写入 CRC_DR", "degree": 0}, {"id": "register_crc_init", "name": "CRC_INIT", "type": "register", "confidence": 0.9, "page": 122, "context": "需要注意的是，向 CRC_CR.RESET 写入 1 会将 CRC_INIT 寄存器复位为 0xFFFFFFFF。", "degree": 0}, {"id": "bit_field_adc_chn0", "name": "ADC_CHN0", "type": "bit_field", "confidence": 0.9, "page": 124, "context": "1 9$ 任选，若ADC_CHN0[4:0] $= 0$ ，ADC_CHN0[12:8] $^ { = 3 }$ ，", "degree": 0}, {"id": "bit_field_adc_amc", "name": "ADC_AMC", "type": "bit_field", "confidence": 0.9, "page": 129, "context": " 10bit 无符号定点数，ADC_AMC[9]为整数部分，ADC_AMC[8:0]为小数部分。可以表示数值在1 附近的定点数。", "degree": 0}, {"id": "bit_field_adcclksel", "name": "ADCCLKSEL", "type": "bit_field", "confidence": 0.9, "page": 130, "context": "和 PLL 模块。通过配置寄存器 SYS_AFE_REG7.ADCCLKSEL[5:4]设置 ADC 工作频率，00 为 48MHz，10 为 12", "degree": 0}, {"id": "register_data_align", "name": "DATA_ALIGN", "type": "register", "confidence": 0.9, "page": 130, "context": "出格式可配置为左对齐或者右对齐，配置的是 ADC0_CFG.DATA_ALIGN 寄存器，0 为左对齐，1 为右对齐。", "degree": 0}, {"id": "bit_field_trg_mode", "name": "TRG_MODE", "type": "bit_field", "confidence": 0.9, "page": 130, "context": " 段 采 样 模 式 ， 配 置 的 是ADC0_TRIG.TRG_MODE[13:12]寄存器，00 为单段采样模式，01 为两段采样模式，11 为", "degree": 0}, {"id": "bit_field_single_tcnt", "name": "SINGLE_TCNT", "type": "bit_field", "confidence": 0.9, "page": 130, "context": "以设置触发一次采样所需的事件数，配置的是ADC0_TRIG.SINGLE_TCNT[11:8]寄存器，设置范围是 $_ { 0 \\sim 1 5 , 0 ", "degree": 0}, {"id": "register_adc0_chnt1", "name": "ADC0_CHNT1", "type": "register", "confidence": 0.9, "page": 130, "context": "段采样模式下，采样的通道个数，配置的是ADC0_CHNT0、ADC0_CHNT1 寄存器，设置范围是 $_ { 1 \\sim 2 0 }$ ，1 代", "degree": 0}, {"id": "register_adc0_chnx", "name": "ADC0_CHNx", "type": "register", "confidence": 0.9, "page": 146, "context": "以单段触发采样8 个采样为例，ADC0_CHNx 寄存器里设置的第 0/1 个采样是同步采样的，2/3是同步采样的，", "degree": 0}, {"id": "address_0x5aa5", "name": "0x5AA5", "type": "address", "confidence": 0.9, "page": 152, "context": "注意，软件触发采集寄存器为只写寄存器，且只有写入数据为 0x5AA5 时产生软件触发事件，一次总线的写入产生一次软件触发，数据写", "degree": 0}, {"id": "bit_field_adcamc", "name": "ADCAMC", "type": "bit_field", "confidence": 0.9, "page": 155, "context": "为 10bit 无符号定点数，ADCAMC[9]为整数部分，ADCAMC[8:0]为小数部分。所存值为 1 左右。", "degree": 0}, {"id": "parameter_54", "name": "54", "type": "parameter", "confidence": 0.9, "page": 155, "context": "表 10-54 通道 0 阈值寄存器 ADC0_DAT0_TH", "degree": 0}, {"id": "bit_field_adc_chnt0", "name": "ADC_CHNT0", "type": "bit_field", "confidence": 0.9, "page": 158, "context": "TADC[0]或软件触发发生后，先进行 ADC_CHNT0[4:0]次采样，完成后进入空闲状态并等待下一个触发信号的到来；TAD", "degree": 0}, {"id": "register_adc_swt", "name": "ADC_SWT", "type": "register", "confidence": 0.9, "page": 159, "context": "需要使用软件触发采样，需要确保硬件触发已经关闭。然后通过向 ADC_SWT 寄存器写入 0x5AA5 以产生一次软件触发。", "degree": 0}, {"id": "bit_field_adc_chnt1", "name": "ADC_CHNT1", "type": "bit_field", "confidence": 0.9, "page": 159, "context": "DC_CHNT0[4:0]、ADC_CHNT0[12:8]、ADC_CHNT1[4:0]、ADC_CHNT1[12:8]。", "degree": 0}, {"id": "address_0x0004", "name": "0x0004", "type": "address", "confidence": 0.9, "page": 160, "context": "; //关闭 ADC 采样触发  \nADC0_CFG |= 0x0004; //复位ADC 接口电路状态机//此处进行的 ADC 采", "degree": 0}, {"id": "address_0x0005", "name": "0x0005", "type": "address", "confidence": 0.9, "page": 160, "context": "样通道和通道数的修改仅为示例  \nADC0_CHNT0 = 0x0005 //修改ADC 单段采样通道数为5  \nADC0_CHN0", "degree": 0}, {"id": "address_0x0305", "name": "0x0305", "type": "address", "confidence": 0.9, "page": 160, "context": "修改ADC 单段采样通道数为5  \nADC0_CHN0 = 0x0305; //修改 ADC 第 0/1 次采样通道为模拟通道 5 ", "degree": 0}, {"id": "address_0x0604", "name": "0x0604", "type": "address", "confidence": 0.9, "page": 160, "context": " { \\tau } = \\mathbf { \\tau }$ 0x0604; //修改ADC 第2/3 次采样通道为模拟通道 4 和6", "degree": 0}, {"id": "register_utimer_reg", "name": "utimer_reg", "type": "register", "confidence": 0.9, "page": 162, "context": "utimer_reg 寄存器模块，实现", "degree": 0}, {"id": "register_utimer_untx_cmp", "name": "UTIMER_UNTx_CMP", "type": "register", "confidence": 0.9, "page": 166, "context": "沿，发生捕获事件（即输入信号电平变化）时，定时器计数值存入 UTIMER_UNTx_CMP 寄存器，并产生捕获中断。计数器回零时，仍然会产生回零中断。", "degree": 0}, {"id": "register_encoderx", "name": "EncoderX", "type": "register", "confidence": 0.9, "page": 187, "context": "******** UTIMER_ECDx_CFG  EncoderX 配置寄存器UTIMER_ECD0_CFG 地址：0x4001_1880", "degree": 0}, {"id": "register_clk_div", "name": "CLK_DIV", "type": "register", "confidence": 0.9, "page": 193, "context": "HALL模块工作频率可调。通过配置HALL_CFG.CLK_DIV寄存器，可以选择系统主时钟的1/2/4/8分频作为HALL 模块工", "degree": 0}, {"id": "bit_field_fil_data", "name": "FIL_DATA", "type": "bit_field", "confidence": 0.9, "page": 194, "context": "通过访问 HALL_INFO.FIL_DATA[2:0]可以捕捉滤波后的 HALL 信号；HALL_INFO.RAW", "degree": 0}, {"id": "bit_field_raw_data", "name": "RAW_DATA", "type": "bit_field", "confidence": 0.9, "page": 194, "context": ":0]可以捕捉滤波后的 HALL 信号；HALL_INFO.RAW_DATA[2:0]则是滤波前原始HALL 输入信号，详见12.3.3。", "degree": 0}, {"id": "parameter_42", "name": "42", "type": "parameter", "confidence": 0.9, "page": 194, "context": "6 { = } 1 . 4 0 s$ 的时间宽度，达到10.42ns 的时间分辨率。", "degree": 0}, {"id": "parameter_分辨率", "name": "分辨率", "type": "parameter", "confidence": 0.9, "page": 194, "context": "1 . 4 0 s$ 的时间宽度，达到10.42ns 的时间分辨率。", "degree": 0}, {"id": "bit_field_mcpwm_bkin", "name": "MCPWM_BKIN", "type": "bit_field", "confidence": 0.9, "page": 201, "context": "M，即 FAIL0 和 FAIL1，分别可以来自芯片 IO MCPWM_BKIN[1:0]或芯片内部比较器的输出 CMP[1:0]。", "degree": 0}, {"id": "bit_field_cmp", "name": "CMP", "type": "bit_field", "confidence": 0.9, "page": 201, "context": "IO MCPWM_BKIN[1:0]或芯片内部比较器的输出 CMP[1:0]。", "degree": 0}, {"id": "bit_field_io_flt_clkdiv", "name": "IO_FLT_CLKDIV", "type": "bit_field", "confidence": 0.9, "page": 201, "context": "MCPWM_BKIN[1:0]则使用 MCPWM_TCLK.IO_FLT_CLKDIV[3:0]作为第二级的分频系数；如果 Fail 信号来自芯片内部比较器", "degree": 0}, {"id": "bit_field_cmp_flt_clkdiv", "name": "CMP_FLT_CLKDIV", "type": "bit_field", "confidence": 0.9, "page": 201, "context": " 信号来自芯片内部比较器输出，则使用 MCPWM_TCLK.CMP_FLT_CLKDIV[3:0]作为第二级的分频系数，如图 13-5 所示。", "degree": 0}, {"id": "register_chxp_default", "name": "CHxP_DEFAULT", "type": "register", "confidence": 0.9, "page": 202, "context": "FAIL.CHxN_DEFAULT 和MCPWM_FAIL.CHxP_DEFAULT 寄存器所指定的故障缺省值，此时 MCPWM_FAIL.CHxN_D", "degree": 0}, {"id": "bit_field_fail", "name": "FAIL", "type": "bit_field", "confidence": 0.9, "page": 207, "context": "当芯片调试中，CPU Halt 时，PWM 停止输出，输出 FAIL[15:8]的值。", "degree": 0}, {"id": "address_0x0c", "name": "0x0C", "type": "address", "confidence": 0.9, "page": 234, "context": "ART 的 Tx_buffer 和 Rx_buffer 共享地址 0x0C 地址。其中，Tx_buffer 是只写的，Rx_buffe", "degree": 0}, {"id": "register_dsp_cos", "name": "DSP_COS", "type": "register", "confidence": 0.9, "page": 242, "context": " 为输入，计算并输出 sin/cos 结果到DSP_SIN/DSP_COS 寄存器；计算 arctan 时以坐标 DSP_X/DSP_Y 为输", "degree": 0}, {"id": "register_dsp_mod", "name": "DSP_MOD", "type": "register", "confidence": 0.9, "page": 242, "context": "{ 2 } \\right)$ 到 DSP_ARCTAN 和 DSP_MOD 寄存器。", "degree": 0}, {"id": "address_0x5555", "name": "0x5555", "type": "address", "confidence": 0.9, "page": 261, "context": "R5 0x5555 # Assign 0x5555 to R5   \nR1 2", "degree": 0}, {"id": "address_0x01000100", "name": "0x01000100", "type": "address", "confidence": 0.9, "page": 262, "context": "以如下 DATA MEM 内容为例，第一行数据 0x01000100 对应 0x0 地址，第二行数据 0x30005000对应 ", "degree": 0}, {"id": "address_0x0003fff8", "name": "0x0003FFF8", "type": "address", "confidence": 0.9, "page": 262, "context": "行数据 0x30005000对应 0x1 地址，第三行数据 0x0003FFF8 对应 $0 \\mathrm { x } 2$ 地址。", "degree": 0}, {"id": "address_0xffffe000", "name": "0xFFFFE000", "type": "address", "confidence": 0.9, "page": 262, "context": " R3 的 32bit 数据写入 0x3 地址并覆盖掉数据 0xFFFFE000。", "degree": 0}, {"id": "address_0x30005000", "name": "0x30005000", "type": "address", "confidence": 0.9, "page": 262, "context": "式进行计算。以如下 DATA MEM 内容为例，第二行数据 0x30005000 对应的 CPU 寻址地址为 0x4001_4804，第三行", "degree": 0}, {"id": "address_0x00100010", "name": "0x00100010", "type": "address", "confidence": 0.9, "page": 262, "context": "0x00100010   \n0x30005000   \n0x0003FFF8  ", "degree": 0}, {"id": "address_0x30004000", "name": "0x30004000", "type": "address", "confidence": 0.9, "page": 262, "context": " \n0x0003FFF8   \n0xFFFFE000   \n0x30004000   \n0x7FFFFFFF   \n0x7FFFFFFF  ", "degree": 0}, {"id": "address_0x7fffffff", "name": "0x7FFFFFFF", "type": "address", "confidence": 0.9, "page": 262, "context": " \n0xFFFFE000   \n0x30004000   \n0x7FFFFFFF   \n0x7FFFFFFF   \n0xF0000003  ", "degree": 0}, {"id": "address_0xf0000003", "name": "0xF0000003", "type": "address", "confidence": 0.9, "page": 262, "context": " \n0x7FFFFFFF   \n0x7FFFFFFF   \n0xF0000003   \n0x00007FFF # 8   \n0x000080", "degree": 0}, {"id": "address_0x00007fff", "name": "0x00007FFF", "type": "address", "confidence": 0.9, "page": 262, "context": " \n0x7FFFFFFF   \n0xF0000003   \n0x00007FFF # 8   \n0x00008000   \n0x800000", "degree": 0}, {"id": "address_0x00008000", "name": "0x00008000", "type": "address", "confidence": 0.9, "page": 262, "context": "F0000003   \n0x00007FFF # 8   \n0x00008000   \n0x80000000   \n0x00000000", "degree": 0}, {"id": "address_0x80000000", "name": "0x80000000", "type": "address", "confidence": 0.9, "page": 262, "context": "00007FFF # 8   \n0x00008000   \n0x80000000   \n0x00000000", "degree": 0}, {"id": "register_i2c_data", "name": "I2C_DATA", "type": "register", "confidence": 0.9, "page": 269, "context": "地址匹配后，从发送器将字节从 I2C_DATA 寄存器经由内部移位寄存器发送到 SDA 线上。在I2C_DATA ", "degree": 0}, {"id": "register_i2c_mscr", "name": "I2C_MSCR", "type": "register", "confidence": 0.9, "page": 270, "context": "2C 接口执行主模式传输之前，需要判断总线是否空闲。可读取 I2C_MSCR 寄存器的 BIT3，查", "degree": 0}, {"id": "register_i2c_scr", "name": "I2C_SCR", "type": "register", "confidence": 0.9, "page": 278, "context": "一般，进入中断后，需读取 I2C_SCR 寄存器，获得当前 I2C 总线状态及当前传输处于什么阶段；然后，对", "degree": 0}, {"id": "bit_field_cfg", "name": "CFG", "type": "bit_field", "confidence": 0.9, "page": 284, "context": "CFG[7:6]配置为2，半双工发送模式有效。此时，本接口只能发送数据。GP", "degree": 0}, {"id": "register_tx_data", "name": "TX_DATA", "type": "register", "confidence": 0.9, "page": 288, "context": "配置完毕。注意 SIZE 只能配置为 1。  \nCPU 对 TX_DATA 寄存器执行写操作，触发 SPI 接口进入发送流程，发送的数据来自 ", "degree": 0}, {"id": "bit_field_baud", "name": "BAUD", "type": "bit_field", "confidence": 0.9, "page": 289, "context": "SPI 接口时钟通过对系统时钟分频获得，分频系数来自 BAUD[5:0]。分频范围是 $1 \\sim 1 2 8$ ，对应的 BAU", "degree": 0}, {"id": "bit_field_spi_cfg", "name": "SPI_CFG", "type": "bit_field", "confidence": 0.9, "page": 290, "context": "SPI_CFG[3:2]对应的通讯波形极性和相位可参考 17.3.2.4。", "degree": 0}, {"id": "bit_field_fil_clk_div16", "name": "FIL_CLK_DIV16", "type": "bit_field", "confidence": 0.9, "page": 295, "context": " 表示打开，0 表示关闭；通过配置寄存器 CMP_TCLK.FIL_CLK_DIV16[7:4]可以配置滤波时钟，数值设置范围是 $_ { 0 \\sim 1", "degree": 0}, {"id": "bit_field_it_cm", "name": "IT_CM", "type": "bit_field", "confidence": 0.9, "page": 295, "context": " 可 通 过 配 置 寄 存 器 SYS_AFE_REG1.IT_CM[1:0] 设 置 为$0 . 1 5 \\mathrm { u S }", "degree": 0}, {"id": "bit_field_cmpx_selp", "name": "CMPx_SELP", "type": "bit_field", "confidence": 0.9, "page": 295, "context": "可 以 通 过 配 置 寄 存 器SYS_AFE_REG3.CMPx_SELP[2:0]进行设置，负端有 4 种信号来源可选择，可以通过配置寄存器 ", "degree": 0}, {"id": "bit_field_cmpx_seln", "name": "CMPx_SELN", "type": "bit_field", "confidence": 0.9, "page": 295, "context": "号来源可选择，可以通过配置寄存器 SYS_AFE_REG3.CMPx_SELN[1:0]进行设置。", "degree": 0}, {"id": "register_cmp_cfg", "name": "CMP_CFG", "type": "register", "confidence": 0.9, "page": 299, "context": "控制信号。但比较器自身的中断信号产生于开窗控制无关，仅仅受 CMP_CFG 寄存器影响。", "degree": 0}, {"id": "bit_field_blcwin", "name": "BLCWIN", "type": "bit_field", "confidence": 0.9, "page": 301, "context": "通常 CMP_ BLCWIN[3:0]或 CMP_ BLCWIN[7:4]中有 1bit 为 1，", "degree": 0}, {"id": "parameter_29", "name": "29", "type": "parameter", "confidence": 0.9, "page": 302, "context": " 11 位 ID 格式；2.0B 包含了 11 位ID 和 29 位 ID。", "degree": 0}, {"id": "register_can_eir", "name": "CAN_EIR", "type": "register", "confidence": 0.9, "page": 304, "context": "CAN 接口包含中断事件比较多，在CAN_IR 和 CAN_EIR 寄存器描述中有相应说明。根据实际使用情况，开启对应的中断事件使能开", "degree": 0}, {"id": "register_btr0", "name": "BTR0", "type": "register", "confidence": 0.9, "page": 305, "context": "成。CAN_BTR0 主要是配置TQ 参数（TQ 的计算见 BTR0 寄存器说明），CAN_BTR1 主要处理1-bit 数据的采样点、", "degree": 0}, {"id": "register_mask", "name": "MASK", "type": "register", "confidence": 0.9, "page": 307, "context": "CAN_ACR 列出了一个特定的 ID，CAN_AMR 为 MASK 寄存器，标识 CAN_ACR 中对应位数据同接收到的ID 对应位数", "degree": 0}, {"id": "bit_field_acr1", "name": "ACR1", "type": "bit_field", "confidence": 0.9, "page": 308, "context": "用到，对应的 AMR 需配置为全 1。例如滤波 ID1 的 ACR1[3:0]和 ACR[3:0]。  \nRTR 是否需要匹配，结合实际情", "degree": 0}, {"id": "bit_field_acr", "name": "ACR", "type": "bit_field", "confidence": 0.9, "page": 308, "context": "需配置为全 1。例如滤波 ID1 的 ACR1[3:0]和 ACR[3:0]。  \nRTR 是否需要匹配，结合实际情况配置AMR。", "degree": 0}, {"id": "bit_field_acr3", "name": "ACR3", "type": "bit_field", "confidence": 0.9, "page": 308, "context": "此时， ACR3[1:0]没有用到，对应的 AMR 需配置为全 1。", "degree": 0}, {"id": "register_can_amc", "name": "CAN_AMC", "type": "register", "confidence": 0.9, "page": 312, "context": "大数据量为8 个字节。RX FIFO 中有几个有效帧，通过 CAN_AMC 寄存器可知。通过读取 CAN_TXRX 寄存器可以获得最先被接收到", "degree": 0}, {"id": "register_can_txrx", "name": "CAN_TXRX", "type": "register", "confidence": 0.9, "page": 312, "context": "中有几个有效帧，通过 CAN_AMC 寄存器可知。通过读取 CAN_TXRX 寄存器可以获得最先被接收到的数据帧。若 RX FIFO 满了，抛弃", "degree": 0}, {"id": "register_can_ewlr", "name": "CAN_EWLR", "type": "register", "confidence": 0.9, "page": 314, "context": "N 总线错误，且在CAN 控制器进入被动错误状态之前被触发。CAN_EWLR 寄存器存储相应阈值，此寄存器的配置必须处于复位模式下。CAN_EW", "degree": 0}, {"id": "register_can_ir", "name": "CAN_IR", "type": "register", "confidence": 0.9, "page": 318, "context": "CAN_IR 寄存器，为读清除寄存器。只有 BIT0--RFIFO_N_EMPT", "degree": 0}, {"id": "register_sys_wdt_clr", "name": "SYS_WDT_CLR", "type": "register", "confidence": 0.9, "page": 333, "context": "PROTECT 写入 0xCAFE，开启 WatchDog SYS_WDT_CLR 寄存器写入。", "degree": 0}, {"id": "address_0xcafe", "name": "0xCAFE", "type": "address", "confidence": 0.9, "page": 333, "context": "看门狗清零前，需要向 SYS_WR_PROTECT 写入 0xCAFE，开启 WatchDog SYS_WDT_CLR 寄存器写入", "degree": 0}], "edges": []}