<html>
    <head>
        <meta charset="utf-8">
        
            <script src="lib/bindings/utils.js"></script>
            <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/vis-network/9.1.2/dist/dist/vis-network.min.css" integrity="sha512-WgxfT5LWjfszlPHXRmBWHkV2eceiWTOBvrKCNbdgDYTHrT2AeLCGbF4sZlZw3UMN3WtL0tGUoIAKsu8mllg/XA==" crossorigin="anonymous" referrerpolicy="no-referrer" />
            <script src="https://cdnjs.cloudflare.com/ajax/libs/vis-network/9.1.2/dist/vis-network.min.js" integrity="sha512-LnvoEWDFrqGHlHmDD2101OrLcbsfkrzoSpvtSQtxK3RMnRV0eOkhhBN2dXHKRrUU8p2DGRTk35n4O8nWSVe1mQ==" crossorigin="anonymous" referrerpolicy="no-referrer"></script>
            
        
<center>
<h1></h1>
</center>

<!-- <link rel="stylesheet" href="../node_modules/vis/dist/vis.min.css" type="text/css" />
<script type="text/javascript" src="../node_modules/vis/dist/vis.js"> </script>-->
        <link
          href="https://cdn.jsdelivr.net/npm/bootstrap@5.0.0-beta3/dist/css/bootstrap.min.css"
          rel="stylesheet"
          integrity="sha384-eOJMYsd53ii+scO/bJGFsiCZc+5NDVN2yr8+0RDqr0Ql0h+rP48ckxlpbzKgwra6"
          crossorigin="anonymous"
        />
        <script
          src="https://cdn.jsdelivr.net/npm/bootstrap@5.0.0-beta3/dist/js/bootstrap.bundle.min.js"
          integrity="sha384-JEW9xMcG8R+pH31jmWH6WWP0WintQrMb4s7ZOdauHnUtxwoG2vI5DkLtS3qm9Ekf"
          crossorigin="anonymous"
        ></script>


        <center>
          <h1></h1>
        </center>
        <style type="text/css">

             #mynetwork {
                 width: 100%;
                 height: 900px;
                 background-color: #1a1a1a;
                 border: 1px solid lightgray;
                 position: relative;
                 float: left;
             }

             
             #loadingBar {
                 position:absolute;
                 top:0px;
                 left:0px;
                 width: 100%;
                 height: 900px;
                 background-color:rgba(200,200,200,0.8);
                 -webkit-transition: all 0.5s ease;
                 -moz-transition: all 0.5s ease;
                 -ms-transition: all 0.5s ease;
                 -o-transition: all 0.5s ease;
                 transition: all 0.5s ease;
                 opacity:1;
             }

             #bar {
                 position:absolute;
                 top:0px;
                 left:0px;
                 width:20px;
                 height:20px;
                 margin:auto auto auto auto;
                 border-radius:11px;
                 border:2px solid rgba(30,30,30,0.05);
                 background: rgb(0, 173, 246); /* Old browsers */
                 box-shadow: 2px 0px 4px rgba(0,0,0,0.4);
             }

             #border {
                 position:absolute;
                 top:10px;
                 left:10px;
                 width:500px;
                 height:23px;
                 margin:auto auto auto auto;
                 box-shadow: 0px 0px 4px rgba(0,0,0,0.2);
                 border-radius:10px;
             }

             #text {
                 position:absolute;
                 top:8px;
                 left:530px;
                 width:30px;
                 height:50px;
                 margin:auto auto auto auto;
                 font-size:22px;
                 color: #000000;
             }

             div.outerBorder {
                 position:relative;
                 top:400px;
                 width:600px;
                 height:44px;
                 margin:auto auto auto auto;
                 border:8px solid rgba(0,0,0,0.1);
                 background: rgb(252,252,252); /* Old browsers */
                 background: -moz-linear-gradient(top,  rgba(252,252,252,1) 0%, rgba(237,237,237,1) 100%); /* FF3.6+ */
                 background: -webkit-gradient(linear, left top, left bottom, color-stop(0%,rgba(252,252,252,1)), color-stop(100%,rgba(237,237,237,1))); /* Chrome,Safari4+ */
                 background: -webkit-linear-gradient(top,  rgba(252,252,252,1) 0%,rgba(237,237,237,1) 100%); /* Chrome10+,Safari5.1+ */
                 background: -o-linear-gradient(top,  rgba(252,252,252,1) 0%,rgba(237,237,237,1) 100%); /* Opera 11.10+ */
                 background: -ms-linear-gradient(top,  rgba(252,252,252,1) 0%,rgba(237,237,237,1) 100%); /* IE10+ */
                 background: linear-gradient(to bottom,  rgba(252,252,252,1) 0%,rgba(237,237,237,1) 100%); /* W3C */
                 filter: progid:DXImageTransform.Microsoft.gradient( startColorstr='#fcfcfc', endColorstr='#ededed',GradientType=0 ); /* IE6-9 */
                 border-radius:72px;
                 box-shadow: 0px 0px 10px rgba(0,0,0,0.2);
             }
             

             

             
        </style>
    </head>


    <body>
        <div class="card" style="width: 100%">
            
            
            <div id="mynetwork" class="card-body"></div>
        </div>

        
            <div id="loadingBar">
              <div class="outerBorder">
                <div id="text">0%</div>
                <div id="border">
                  <div id="bar"></div>
                </div>
              </div>
            </div>
        
        

        <script type="text/javascript">

              // initialize global variables.
              var edges;
              var nodes;
              var allNodes;
              var allEdges;
              var nodeColors;
              var originalNodes;
              var network;
              var container;
              var options, data;
              var filter = {
                  item : '',
                  property : '',
                  value : []
              };

              

              

              // This method is responsible for drawing the graph, returns the drawn network
              function drawGraph() {
                  var container = document.getElementById('mynetwork');

                  

                  // parsing and collecting nodes and edges from the python
                  nodes = new vis.DataSet([{"color": "#4ecdc4", "font": {"color": "white"}, "id": "module_adc", "label": "ADC", "shape": "dot", "size": 33.5, "title": "\u540d\u79f0: ADC\n            \u7c7b\u578b: module\n            \u7f6e\u4fe1\u5ea6: 0.900\n            \u9875\u9762: 1\n            \u5ea6\u6570: 0\n            \u4e0a\u4e0b\u6587: \u7edf . 104.4 BGP \u57fa\u51c6\u7535\u538b\u6e90 . . 104.5 ADC \u6a21\u6570\u8f6c\u6362\u5668 . 114.6 OPA \u8fd0\u7b97\u653e\u5927\u5668 . 114..."}, {"color": "#4ecdc4", "font": {"color": "white"}, "id": "module_\u6a21\u6570\u8f6c\u6362\u5668", "label": "\u6a21\u6570\u8f6c\u6362\u5668", "shape": "dot", "size": 33.5, "title": "\u540d\u79f0: \u6a21\u6570\u8f6c\u6362\u5668\n            \u7c7b\u578b: module\n            \u7f6e\u4fe1\u5ea6: 0.900\n            \u9875\u9762: 1\n            \u5ea6\u6570: 0\n            \u4e0a\u4e0b\u6587: 104.4 BGP \u57fa\u51c6\u7535\u538b\u6e90 . . 104.5 ADC \u6a21\u6570\u8f6c\u6362\u5668 . 114.6 OPA \u8fd0\u7b97\u653e\u5927\u5668 . 114.7 CMP..."}, {"color": "#4ecdc4", "font": {"color": "white"}, "id": "module_\u6570\u6a21\u8f6c\u6362\u5668", "label": "\u6570\u6a21\u8f6c\u6362\u5668", "shape": "dot", "size": 33.5, "title": "\u540d\u79f0: \u6570\u6a21\u8f6c\u6362\u5668\n            \u7c7b\u578b: module\n            \u7f6e\u4fe1\u5ea6: 0.900\n            \u9875\u9762: 1\n            \u5ea6\u6570: 0\n            \u4e0a\u4e0b\u6587: . 124.8 TMP \u6e29\u5ea6\u4f20\u611f\u5668 . 124.9 DAC \u6570\u6a21\u8f6c\u6362\u5668 . 13  \n5 \u7cfb\u7edf\u63a7\u5236\u53ca\u65f6\u949f\u590d\u4f4d .. .15..."}, {"color": "#4ecdc4", "font": {"color": "white"}, "id": "module_\u5b9a\u65f6\u5668", "label": "\u5b9a\u65f6\u5668", "shape": "dot", "size": 33.5, "title": "\u540d\u79f0: \u5b9a\u65f6\u5668\n            \u7c7b\u578b: module\n            \u7f6e\u4fe1\u5ea6: 0.900\n            \u9875\u9762: 1\n            \u5ea6\u6570: 0\n            \u4e0a\u4e0b\u6587: \u5668 .3.3 \u5f02\u5e38\u548c\u4e2d\u65ad . . 43.4 SYSTICK \u5b9a\u65f6\u5668.... 53.4.1 SYST_CSR \u63a7\u5236\u548c\u72b6\u6001\u5bc4\u5b58\u5668...."}, {"color": "#4ecdc4", "font": {"color": "white"}, "id": "module_\u6bd4\u8f83\u5668", "label": "\u6bd4\u8f83\u5668", "shape": "dot", "size": 33.5, "title": "\u540d\u79f0: \u6bd4\u8f83\u5668\n            \u7c7b\u578b: module\n            \u7f6e\u4fe1\u5ea6: 0.900\n            \u9875\u9762: 1\n            \u5ea6\u6570: 0\n            \u4e0a\u4e0b\u6587: . 114.6 OPA \u8fd0\u7b97\u653e\u5927\u5668 . 114.7 CMP \u6bd4\u8f83\u5668 . 124.8 TMP \u6e29\u5ea6\u4f20\u611f\u5668 . 124.9 DAC..."}, {"color": "#96ceb4", "font": {"color": "white"}, "id": "parameter_\u7535\u538b", "label": "\u7535\u538b", "shape": "dot", "size": 33.5, "title": "\u540d\u79f0: \u7535\u538b\n            \u7c7b\u578b: parameter\n            \u7f6e\u4fe1\u5ea6: 0.900\n            \u9875\u9762: 1\n            \u5ea6\u6570: 0\n            \u4e0a\u4e0b\u6587: .4.3 CLOCK \u65f6\u949f\u7cfb\u7edf . 104.4 BGP \u57fa\u51c6\u7535\u538b\u6e90 . . 104.5 ADC \u6a21\u6570\u8f6c\u6362\u5668 . 114.6 ..."}, {"color": "#45b7d1", "font": {"color": "white"}, "id": "function_\u5b9a\u65f6", "label": "\u5b9a\u65f6", "shape": "dot", "size": 33.5, "title": "\u540d\u79f0: \u5b9a\u65f6\n            \u7c7b\u578b: function\n            \u7f6e\u4fe1\u5ea6: 0.900\n            \u9875\u9762: 1\n            \u5ea6\u6570: 0\n            \u4e0a\u4e0b\u6587: \u5668 .3.3 \u5f02\u5e38\u548c\u4e2d\u65ad . . 43.4 SYSTICK \u5b9a\u65f6\u5668.... 53.4.1 SYST_CSR \u63a7\u5236\u548c\u72b6\u6001\u5bc4\u5b58\u5668..."}, {"color": "#45b7d1", "font": {"color": "white"}, "id": "function_\u6a21\u6570\u8f6c\u6362", "label": "\u6a21\u6570\u8f6c\u6362", "shape": "dot", "size": 33.5, "title": "\u540d\u79f0: \u6a21\u6570\u8f6c\u6362\n            \u7c7b\u578b: function\n            \u7f6e\u4fe1\u5ea6: 0.900\n            \u9875\u9762: 1\n            \u5ea6\u6570: 0\n            \u4e0a\u4e0b\u6587: 104.4 BGP \u57fa\u51c6\u7535\u538b\u6e90 . . 104.5 ADC \u6a21\u6570\u8f6c\u6362\u5668 . 114.6 OPA \u8fd0\u7b97\u653e\u5927\u5668 . 114.7 CM..."}, {"color": "#45b7d1", "font": {"color": "white"}, "id": "function_\u653e\u5927", "label": "\u653e\u5927", "shape": "dot", "size": 33.5, "title": "\u540d\u79f0: \u653e\u5927\n            \u7c7b\u578b: function\n            \u7f6e\u4fe1\u5ea6: 0.900\n            \u9875\u9762: 1\n            \u5ea6\u6570: 0\n            \u4e0a\u4e0b\u6587: 104.5 ADC \u6a21\u6570\u8f6c\u6362\u5668 . 114.6 OPA \u8fd0\u7b97\u653e\u5927\u5668 . 114.7 CMP \u6bd4\u8f83\u5668 . 124.8 TMP ..."}, {"color": "#45b7d1", "font": {"color": "white"}, "id": "function_\u6bd4\u8f83", "label": "\u6bd4\u8f83", "shape": "dot", "size": 33.5, "title": "\u540d\u79f0: \u6bd4\u8f83\n            \u7c7b\u578b: function\n            \u7f6e\u4fe1\u5ea6: 0.900\n            \u9875\u9762: 1\n            \u5ea6\u6570: 0\n            \u4e0a\u4e0b\u6587: . 114.6 OPA \u8fd0\u7b97\u653e\u5927\u5668 . 114.7 CMP \u6bd4\u8f83\u5668 . 124.8 TMP \u6e29\u5ea6\u4f20\u611f\u5668 . 124.9 DA..."}, {"color": "#45b7d1", "font": {"color": "white"}, "id": "function_\u6570\u6a21\u8f6c\u6362", "label": "\u6570\u6a21\u8f6c\u6362", "shape": "dot", "size": 33.5, "title": "\u540d\u79f0: \u6570\u6a21\u8f6c\u6362\n            \u7c7b\u578b: function\n            \u7f6e\u4fe1\u5ea6: 0.900\n            \u9875\u9762: 1\n            \u5ea6\u6570: 0\n            \u4e0a\u4e0b\u6587: . 124.8 TMP \u6e29\u5ea6\u4f20\u611f\u5668 . 124.9 DAC \u6570\u6a21\u8f6c\u6362\u5668 . 13  \n5 \u7cfb\u7edf\u63a7\u5236\u53ca\u65f6\u949f\u590d\u4f4d .. .15..."}, {"color": "#45b7d1", "font": {"color": "white"}, "id": "function_\u63a7\u5236", "label": "\u63a7\u5236", "shape": "dot", "size": 33.5, "title": "\u540d\u79f0: \u63a7\u5236\n            \u7c7b\u578b: function\n            \u7f6e\u4fe1\u5ea6: 0.900\n            \u9875\u9762: 1\n            \u5ea6\u6570: 0\n            \u4e0a\u4e0b\u6587: RM\u00aeCORTEXTM-M0 \u6838\u5fc3 ...3.2 NVIC \u63a7\u5236\u5668 .3.3 \u5f02\u5e38\u548c\u4e2d\u65ad . . 43.4 SYSTICK ..."}, {"color": "#45b7d1", "font": {"color": "white"}, "id": "function_\u4e2d\u65ad", "label": "\u4e2d\u65ad", "shape": "dot", "size": 33.5, "title": "\u540d\u79f0: \u4e2d\u65ad\n            \u7c7b\u578b: function\n            \u7f6e\u4fe1\u5ea6: 0.900\n            \u9875\u9762: 1\n            \u5ea6\u6570: 0\n            \u4e0a\u4e0b\u6587: M0 \u6838\u5fc3 ...3.2 NVIC \u63a7\u5236\u5668 .3.3 \u5f02\u5e38\u548c\u4e2d\u65ad . . 43.4 SYSTICK \u5b9a\u65f6\u5668.... 53.4..."}, {"color": "#45b7d1", "font": {"color": "white"}, "id": "function_\u590d\u4f4d", "label": "\u590d\u4f4d", "shape": "dot", "size": 33.5, "title": "\u540d\u79f0: \u590d\u4f4d\n            \u7c7b\u578b: function\n            \u7f6e\u4fe1\u5ea6: 0.900\n            \u9875\u9762: 1\n            \u5ea6\u6570: 0\n            \u4e0a\u4e0b\u6587: 4.9 DAC \u6570\u6a21\u8f6c\u6362\u5668 . 13  \n5 \u7cfb\u7edf\u63a7\u5236\u53ca\u65f6\u949f\u590d\u4f4d .. .15..."}, {"color": "#45b7d1", "font": {"color": "white"}, "id": "function_\u4fdd\u62a4", "label": "\u4fdd\u62a4", "shape": "dot", "size": 33.5, "title": "\u540d\u79f0: \u4fdd\u62a4\n            \u7c7b\u578b: function\n            \u7f6e\u4fe1\u5ea6: 0.900\n            \u9875\u9762: 2\n            \u5ea6\u6570: 0\n            \u4e0a\u4e0b\u6587: \u5b58 35  \n5.3.23 SYS_WR_PROTECT \u5199\u4fdd\u62a4\u5bc4\u5b58\u5668. 35  \n5.3.24 SYS_AFE_DAC_A..."}, {"color": "#45b7d1", "font": {"color": "white"}, "id": "function_\u914d\u7f6e", "label": "\u914d\u7f6e", "shape": "dot", "size": 33.5, "title": "\u540d\u79f0: \u914d\u7f6e\n            \u7c7b\u578b: function\n            \u7f6e\u4fe1\u5ea6: 0.900\n            \u9875\u9762: 2\n            \u5ea6\u6570: 0\n            \u4e0a\u4e0b\u6587:  ...22  \n5.3.4 SYS_AFE_REG0 \u6a21\u62df\u914d\u7f6e\u5bc4\u5b58\u56680 .... 22  \n5.3.5 SYS_AFE_R..."}, {"color": "#4ecdc4", "font": {"color": "white"}, "id": "module_flash", "label": "FLASH", "shape": "dot", "size": 33.5, "title": "\u540d\u79f0: FLASH\n            \u7c7b\u578b: module\n            \u7f6e\u4fe1\u5ea6: 0.900\n            \u9875\u9762: 3\n            \u5ea6\u6570: 0\n            \u4e0a\u4e0b\u6587: 6 FLASH. .38..."}, {"color": "#ff6b6b", "font": {"color": "white"}, "id": "register_flash_cfg", "label": "FLASH_CFG", "shape": "dot", "size": 33.5, "title": "\u540d\u79f0: FLASH_CFG\n            \u7c7b\u578b: register\n            \u7f6e\u4fe1\u5ea6: 0.900\n            \u9875\u9762: 3\n            \u5ea6\u6570: 0\n            \u4e0a\u4e0b\u6587:  . 45  \n6.3.1 \u5730\u5740\u5206\u914d 45  \n6.3.2 FLASH_CFG \u914d\u7f6e\u5bc4\u5b58\u5668\uff08\u63a8\u8350\u5148\u8bfb\u56de\uff0c\u6309\u6216/\u4e0e\u65b9\u5f0f\u4fee\u6539\uff09 45  \n6.3.3 FL..."}, {"color": "#ff6b6b", "font": {"color": "white"}, "id": "register_dma", "label": "DMA", "shape": "dot", "size": 33.5, "title": "\u540d\u79f0: DMA\n            \u7c7b\u578b: register\n            \u7f6e\u4fe1\u5ea6: 0.900\n            \u9875\u9762: 4\n            \u5ea6\u6570: 0\n            \u4e0a\u4e0b\u6587: 1 \u5730\u5740\u5206\u914d .. 56  \n7.6.2 DMA_CTRL DMA \u63a7\u5236\u5bc4\u5b58\u5668. 56  \n7.6.3 DMA_IF DMA\u4e2d\u65ad\u6807\u5fd7\u5bc4\u5b58\u5668..."}, {"color": "#4ecdc4", "font": {"color": "white"}, "id": "module_dma", "label": "DMA", "shape": "dot", "size": 33.5, "title": "\u540d\u79f0: DMA\n            \u7c7b\u578b: module\n            \u7f6e\u4fe1\u5ea6: 0.900\n            \u9875\u9762: 4\n            \u5ea6\u6570: 0\n            \u4e0a\u4e0b\u6587: 1 \u5730\u5740\u5206\u914d .. 56  \n7.6.2 DMA_CTRL DMA \u63a7\u5236\u5bc4\u5b58\u5668. 56  \n7.6.3 DMA_IF DMA\u4e2d..."}, {"color": "#4ecdc4", "font": {"color": "white"}, "id": "module_gpio", "label": "GPIO", "shape": "dot", "size": 33.5, "title": "\u540d\u79f0: GPIO\n            \u7c7b\u578b: module\n            \u7f6e\u4fe1\u5ea6: 0.900\n            \u9875\u9762: 4\n            \u5ea6\u6570: 0\n            \u4e0a\u4e0b\u6587: 8 GPIO... .62..."}, {"color": "#ff6b6b", "font": {"color": "white"}, "id": "register_crc", "label": "CRC", "shape": "dot", "size": 33.5, "title": "\u540d\u79f0: CRC\n            \u7c7b\u578b: register\n            \u7f6e\u4fe1\u5ea6: 0.900\n            \u9875\u9762: 6\n            \u5ea6\u6570: 0\n            \u4e0a\u4e0b\u6587: 9.4.2.2 CRC_CR CRC \u63a7\u5236\u5bc4\u5b58\u5668 83..."}, {"color": "#96ceb4", "font": {"color": "white"}, "id": "parameter_11", "label": "11", "shape": "dot", "size": 33.5, "title": "\u540d\u79f0: 11\n            \u7c7b\u578b: parameter\n            \u7f6e\u4fe1\u5ea6: 0.900\n            \u9875\u9762: 6\n            \u5ea6\u6570: 0\n            \u4e0a\u4e0b\u6587: .2.10.4 ADC0_AMC_B1 17  \n10.2.11 \u901a\u90530\u9608\u503c\u5bc4\u5b58\u5668 . 117  \n10.2.11.1 ADC0_..."}, {"color": "#ff6b6b", "font": {"color": "white"}, "id": "register_timer0", "label": "Timer0", "shape": "dot", "size": 33.5, "title": "\u540d\u79f0: Timer0\n            \u7c7b\u578b: register\n            \u7f6e\u4fe1\u5ea6: 0.900\n            \u9875\u9762: 9\n            \u5ea6\u6570: 0\n            \u4e0a\u4e0b\u6587: 34  \n11.3.4.1 UTIMER_UNT0_CFG Timer0 \u914d\u7f6e\u5bc4\u5b58\u5668 .. .....134  \n11.3.4.2 UTIMER..."}, {"color": "#ff6b6b", "font": {"color": "white"}, "id": "register_timer1", "label": "Timer1", "shape": "dot", "size": 33.5, "title": "\u540d\u79f0: Timer1\n            \u7c7b\u578b: register\n            \u7f6e\u4fe1\u5ea6: 0.900\n            \u9875\u9762: 9\n            \u5ea6\u6570: 0\n            \u4e0a\u4e0b\u6587: 34  \n11.3.4.2 UTIMER_UNT1_CFG Timer1 \u914d\u7f6e\u5bc4\u5b58\u5668 .. ...136  \n11.3.4.3 UTIMER_U..."}, {"color": "#ff6b6b", "font": {"color": "white"}, "id": "register_timer2", "label": "Timer2", "shape": "dot", "size": 33.5, "title": "\u540d\u79f0: Timer2\n            \u7c7b\u578b: register\n            \u7f6e\u4fe1\u5ea6: 0.900\n            \u9875\u9762: 9\n            \u5ea6\u6570: 0\n            \u4e0a\u4e0b\u6587: 36  \n11.3.4.3 UTIMER_UNT2_CFG Timer2 \u914d\u7f6e\u5bc4\u5b58\u5668 . ..137  \n11.3.4.4 UTIMER_UNT..."}, {"color": "#ff6b6b", "font": {"color": "white"}, "id": "register_timer3", "label": "Timer3", "shape": "dot", "size": 33.5, "title": "\u540d\u79f0: Timer3\n            \u7c7b\u578b: register\n            \u7f6e\u4fe1\u5ea6: 0.900\n            \u9875\u9762: 9\n            \u5ea6\u6570: 0\n            \u4e0a\u4e0b\u6587: 37  \n11.3.4.4 UTIMER_UNT3_CFG Timer3 \u914d\u7f6e\u5bc4\u5b58\u5668 .. ..138  \n11.3.4.5 UTIMER_UN..."}, {"color": "#4ecdc4", "font": {"color": "white"}, "id": "module_timer", "label": "Timer", "shape": "dot", "size": 33.5, "title": "\u540d\u79f0: Timer\n            \u7c7b\u578b: module\n            \u7f6e\u4fe1\u5ea6: 0.900\n            \u9875\u9762: 9\n            \u5ea6\u6570: 0\n            \u4e0a\u4e0b\u6587: 138  \n11.3.4.5 UTIMER_UNT0_TH Timer 0 \u95e8\u9650\u5bc4\u5b58\u5668 .. ...139  \n11.3.4.6 ..."}, {"color": "#4ecdc4", "font": {"color": "white"}, "id": "module_\u8ba1\u6570\u5668", "label": "\u8ba1\u6570\u5668", "shape": "dot", "size": 33.5, "title": "\u540d\u79f0: \u8ba1\u6570\u5668\n            \u7c7b\u578b: module\n            \u7f6e\u4fe1\u5ea6: 0.900\n            \u9875\u9762: 9\n            \u5ea6\u6570: 0\n            \u4e0a\u4e0b\u6587: \n11.2.3 \u6a21\u5f0f.. . 126  \n11.2.3.1 \u8ba1\u6570\u5668 .. .126  \n11.2.3.2 \u6bd4\u8f83\u6a21\u5f0f. .127..."}, {"color": "#45b7d1", "font": {"color": "white"}, "id": "function_\u6ee4\u6ce2", "label": "\u6ee4\u6ce2", "shape": "dot", "size": 33.5, "title": "\u540d\u79f0: \u6ee4\u6ce2\n            \u7c7b\u578b: function\n            \u7f6e\u4fe1\u5ea6: 0.900\n            \u9875\u9762: 9\n            \u5ea6\u6570: 0\n            \u4e0a\u4e0b\u6587: .2 \u5bc4\u5b58\u5668\u6a21\u5757. ..124  \n11.1.1.3 IO \u6ee4\u6ce2\u6a21\u5757.. .....125  \n11.1.1.4 \u901a\u7528\u5b9a\u65f6\u5668..."}, {"color": "#45b7d1", "font": {"color": "white"}, "id": "function_\u8ba1\u6570", "label": "\u8ba1\u6570", "shape": "dot", "size": 33.5, "title": "\u540d\u79f0: \u8ba1\u6570\n            \u7c7b\u578b: function\n            \u7f6e\u4fe1\u5ea6: 0.900\n            \u9875\u9762: 9\n            \u5ea6\u6570: 0\n            \u4e0a\u4e0b\u6587: \n11.2.3 \u6a21\u5f0f.. . 126  \n11.2.3.1 \u8ba1\u6570\u5668 .. .126  \n11.2.3.2 \u6bd4\u8f83\u6a21\u5f0f. .12..."}, {"color": "#ff6b6b", "font": {"color": "white"}, "id": "register_encoder0", "label": "Encoder0", "shape": "dot", "size": 33.5, "title": "\u540d\u79f0: Encoder0\n            \u7c7b\u578b: register\n            \u7f6e\u4fe1\u5ea6: 0.900\n            \u9875\u9762: 10\n            \u5ea6\u6570: 0\n            \u4e0a\u4e0b\u6587: 11.3.5.1 UTIMER_ECD0_CFG  Encoder0 \u914d\u7f6e\u5bc4\u5b58\u5668 .149  \n11.3.5.2 UTIMER_ECD0_T..."}, {"color": "#45b7d1", "font": {"color": "white"}, "id": "function_\u4f7f\u80fd", "label": "\u4f7f\u80fd", "shape": "dot", "size": 33.5, "title": "\u540d\u79f0: \u4f7f\u80fd\n            \u7c7b\u578b: function\n            \u7f6e\u4fe1\u5ea6: 0.900\n            \u9875\u9762: 11\n            \u5ea6\u6570: 0\n            \u4e0a\u4e0b\u6587: 11.3.7.1 UTIMER_RE DMA \u8bf7\u6c42\u4f7f\u80fd\u5bc4\u5b58\u5668 153..."}, {"color": "#4ecdc4", "font": {"color": "white"}, "id": "module_mcpwm", "label": "MCPWM", "shape": "dot", "size": 33.5, "title": "\u540d\u79f0: MCPWM\n            \u7c7b\u578b: module\n            \u7f6e\u4fe1\u5ea6: 0.900\n            \u9875\u9762: 11\n            \u5ea6\u6570: 0\n            \u4e0a\u4e0b\u6587: 13 MCPWM . 161..."}, {"color": "#4ecdc4", "font": {"color": "white"}, "id": "module_counter", "label": "Counter", "shape": "dot", "size": 33.5, "title": "\u540d\u79f0: Counter\n            \u7c7b\u578b: module\n            \u7f6e\u4fe1\u5ea6: 0.900\n            \u9875\u9762: 11\n            \u5ea6\u6570: 0\n            \u4e0a\u4e0b\u6587: 13.1 \u6982\u8ff0 . .161  \n13.1.1 Base Counter \u6a21\u5757. 162  \n13.1.2 Fail \u4fe1\u53f7\u5904\u7406.. ..."}, {"color": "#45b7d1", "font": {"color": "white"}, "id": "function_\u8bbe\u7f6e", "label": "\u8bbe\u7f6e", "shape": "dot", "size": 33.5, "title": "\u540d\u79f0: \u8bbe\u7f6e\n            \u7c7b\u578b: function\n            \u7f6e\u4fe1\u5ea6: 0.900\n            \u9875\u9762: 11\n            \u5ea6\u6570: 0\n            \u4e0a\u4e0b\u6587: . ..168  \n13.1.4.6 MCPWM IO \u6781\u6027\u8bbe\u7f6e . ..168  \n13.1.4.7 MCPWM IO \u81ea..."}, {"color": "#4ecdc4", "font": {"color": "white"}, "id": "module_uart", "label": "UART", "shape": "dot", "size": 33.5, "title": "\u540d\u79f0: UART\n            \u7c7b\u578b: module\n            \u7f6e\u4fe1\u5ea6: 0.900\n            \u9875\u9762: 13\n            \u5ea6\u6570: 0\n            \u4e0a\u4e0b\u6587: 14 UART . 192..."}, {"color": "#96ceb4", "font": {"color": "white"}, "id": "parameter_\u6ce2\u7279\u7387", "label": "\u6ce2\u7279\u7387", "shape": "dot", "size": 33.5, "title": "\u540d\u79f0: \u6ce2\u7279\u7387\n            \u7c7b\u578b: parameter\n            \u7f6e\u4fe1\u5ea6: 0.900\n            \u9875\u9762: 13\n            \u5ea6\u6570: 0\n            \u4e0a\u4e0b\u6587: 192  \n14.2.2 \u63a5\u6536. 192  \n14.2.3 \u6ce2\u7279\u7387\u914d\u7f6e . 192  \n14.2.4 \u6536\u53d1\u7aef\u53e3\u4e92\u6362(TX/RX..."}, {"color": "#ff6b6b", "font": {"color": "white"}, "id": "register_uartx", "label": "UARTx", "shape": "dot", "size": 33.5, "title": "\u540d\u79f0: UARTx\n            \u7c7b\u578b: register\n            \u7f6e\u4fe1\u5ea6: 0.900\n            \u9875\u9762: 13\n            \u5ea6\u6570: 0\n            \u4e0a\u4e0b\u6587:  \u5730\u5740\u5206 194   \n14.3.2 UARTx_CTRL UARTx \u63a7\u5236\u5bc4\u5b58\u5668.. 194   \n14.3.3 UARTx_DIVH UA..."}, {"color": "#96ceb4", "font": {"color": "white"}, "id": "parameter_10", "label": "10", "shape": "dot", "size": 33.5, "title": "\u540d\u79f0: 10\n            \u7c7b\u578b: parameter\n            \u7f6e\u4fe1\u5ea6: 0.900\n            \u9875\u9762: 14\n            \u5ea6\u6570: 0\n            \u4e0a\u4e0b\u6587:  \n15.3.9.3 \u4f2a\u4ee3\u7801 .. .215  \n15.3.10 MACI (reserved) . 215  \n15.3.10...."}, {"color": "#4ecdc4", "font": {"color": "white"}, "id": "module_i2c", "label": "I2C", "shape": "dot", "size": 33.5, "title": "\u540d\u79f0: I2C\n            \u7c7b\u578b: module\n            \u7f6e\u4fe1\u5ea6: 0.900\n            \u9875\u9762: 18\n            \u5ea6\u6570: 0\n            \u4e0a\u4e0b\u6587: 16 I2C . 227..."}, {"color": "#4ecdc4", "font": {"color": "white"}, "id": "module_spi", "label": "SPI", "shape": "dot", "size": 33.5, "title": "\u540d\u79f0: SPI\n            \u7c7b\u578b: module\n            \u7f6e\u4fe1\u5ea6: 0.900\n            \u9875\u9762: 19\n            \u5ea6\u6570: 0\n            \u4e0a\u4e0b\u6587: I2C0_BCR DMA \u4f20\u8f93\u63a7\u5236\u5bc4\u5b58\u5668 242  \n17 SPI.. 243  \n17.1 \u6982\u8ff0 . .243  \n17.2..."}, {"color": "#ff6b6b", "font": {"color": "white"}, "id": "register_spi", "label": "SPI", "shape": "dot", "size": 33.5, "title": "\u540d\u79f0: SPI\n            \u7c7b\u578b: register\n            \u7f6e\u4fe1\u5ea6: 0.900\n            \u9875\u9762: 20\n            \u5ea6\u6570: 0\n            \u4e0a\u4e0b\u6587: .1 \u5730\u5740\u5206\u914d. 251  \n17.4.2 SPI_CFG SPI \u63a7\u5236\u5bc4\u5b58\u5668. 251  \n17.4.3 SPI_IE SPI \u4e2d\u65ad\u5bc4\u5b58..."}, {"color": "#45b7d1", "font": {"color": "white"}, "id": "function_\u6570\u636e\u4f20\u8f93", "label": "\u6570\u636e\u4f20\u8f93", "shape": "dot", "size": 33.5, "title": "\u540d\u79f0: \u6570\u636e\u4f20\u8f93\n            \u7c7b\u578b: function\n            \u7f6e\u4fe1\u5ea6: 0.900\n            \u9875\u9762: 20\n            \u5ea6\u6570: 0\n            \u4e0a\u4e0b\u6587: \u5b58\u5668. 254  \n17.4.7 SPI_SIZE SPI \u6570\u636e\u4f20\u8f93\u957f\u5ea6\u5bc4\u5b58\u5668. 254..."}, {"color": "#4ecdc4", "font": {"color": "white"}, "id": "module_can", "label": "CAN", "shape": "dot", "size": 33.5, "title": "\u540d\u79f0: CAN\n            \u7c7b\u578b: module\n            \u7f6e\u4fe1\u5ea6: 0.900\n            \u9875\u9762: 21\n            \u5ea6\u6570: 0\n            \u4e0a\u4e0b\u6587: 19 CAN. 264..."}, {"color": "#ff6b6b", "font": {"color": "white"}, "id": "register_fifo", "label": "FIFO", "shape": "dot", "size": 33.5, "title": "\u540d\u79f0: FIFO\n            \u7c7b\u578b: register\n            \u7f6e\u4fe1\u5ea6: 0.900\n            \u9875\u9762: 21\n            \u5ea6\u6570: 0\n            \u4e0a\u4e0b\u6587: 8 CAN_ RFIFO0\\~CAN_RFIFO31 RX FIFO \u5bc4\u5b58\u5668 ..288  \n19.2.3.2.19 CAN_TFIFO..."}, {"color": "#45b7d1", "font": {"color": "white"}, "id": "function_\u6062\u590d", "label": "\u6062\u590d", "shape": "dot", "size": 33.5, "title": "\u540d\u79f0: \u6062\u590d\n            \u7c7b\u578b: function\n            \u7f6e\u4fe1\u5ea6: 0.900\n            \u9875\u9762: 21\n            \u5ea6\u6570: 0\n            \u4e0a\u4e0b\u6587: .......276  \n19.2.2.15 \u79bb\u7ebf\u72b6\u6001\u4e0e\u79bb\u7ebf\u6062\u590d . ..276  \n19.2.3 \u5bc4\u5b58\u5668 .. .. 27..."}, {"color": "#ff6b6b", "font": {"color": "white"}, "id": "register_sif_cfg", "label": "SIF_CFG", "shape": "dot", "size": 33.5, "title": "\u540d\u79f0: SIF_CFG\n            \u7c7b\u578b: register\n            \u7f6e\u4fe1\u5ea6: 0.900\n            \u9875\u9762: 23\n            \u5ea6\u6570: 0\n            \u4e0a\u4e0b\u6587: 0.4.2 \u5bc4\u5b58\u5668\u8bf4\u660e .. 292  \n20.4.2.1 SIF_CFG \u914d\u7f6e\u5bc4\u5b58\u5668. 292  \n20.4.2.2 SIF_FREQ \u6ce2\u7279\u7387\u5bc4..."}, {"color": "#4ecdc4", "font": {"color": "white"}, "id": "module_\u770b\u95e8\u72d7", "label": "\u770b\u95e8\u72d7", "shape": "dot", "size": 33.5, "title": "\u540d\u79f0: \u770b\u95e8\u72d7\n            \u7c7b\u578b: module\n            \u7f6e\u4fe1\u5ea6: 0.900\n            \u9875\u9762: 23\n            \u5ea6\u6570: 0\n            \u4e0a\u4e0b\u6587: 21 \u770b\u95e8\u72d7 .. 294..."}, {"color": "#ff6b6b", "font": {"color": "white"}, "id": "register_vtor", "label": "VTOR", "shape": "dot", "size": 33.5, "title": "\u540d\u79f0: VTOR\n            \u7c7b\u578b: register\n            \u7f6e\u4fe1\u5ea6: 0.900\n            \u9875\u9762: 26\n            \u5ea6\u6570: 0\n            \u4e0a\u4e0b\u6587: ctor \u5730\u5740\u5206\u914d\u8868 ... 42  \n\u8868 6-3 IAP VTOR \u5bc4\u5b58\u5668\u63cf\u8ff0 . 44  \n\u8868 6-4 FLASH \u63a7\u5236\u5668\u6a21\u5757\u5bc4\u5b58\u5668..."}, {"color": "#ff6b6b", "font": {"color": "white"}, "id": "register_gpiox", "label": "GPIOx", "shape": "dot", "size": 33.5, "title": "\u540d\u79f0: GPIOx\n            \u7c7b\u578b: register\n            \u7f6e\u4fe1\u5ea6: 0.900\n            \u9875\u9762: 26\n            \u5ea6\u6570: 0\n            \u4e0a\u4e0b\u6587: \u5bc4\u5b58\u5668 DMA_CMARx .. . 60  \n\u8868 8-1 GPIOx \u5bc4\u5b58\u5668\u5217\u8868 . . 64  \n\u88688-2 GPIO \u4e2d\u65ad/\u5524\u9192/\u914d\u7f6e..."}, {"color": "#ff6b6b", "font": {"color": "white"}, "id": "register_adc0", "label": "ADC0", "shape": "dot", "size": 33.5, "title": "\u540d\u79f0: ADC0\n            \u7c7b\u578b: register\n            \u7f6e\u4fe1\u5ea6: 0.900\n            \u9875\u9762: 26\n            \u5ea6\u6570: 0\n            \u4e0a\u4e0b\u6587: \u8f93\u51fa\u6570\u5b57\u91cf\u6570\u5236\u8f6c\u6362 ... ... 90  \n\u8868 10-5 ADC0 \u5bc4\u5b58\u5668\u5217\u8868 .. ......... 93  \n\u8868 10-6 \u91c7\u6837..."}, {"color": "#ff6b6b", "font": {"color": "white"}, "id": "register_dsp", "label": "DSP", "shape": "dot", "size": 33.5, "title": "\u540d\u79f0: DSP\n            \u7c7b\u578b: register\n            \u7f6e\u4fe1\u5ea6: 0.900\n            \u9875\u9762: 29\n            \u5ea6\u6570: 0\n            \u4e0a\u4e0b\u6587: 5-2 DSP \u5730\u5740\u7a7a\u95f4 .. .202  \n\u8868 15-3 DSP \u5bc4\u5b58\u5668\u5217\u8868 .. ...203  \n\u8868 15-4 DSP \u72b6\u6001\u63a7\u5236..."}, {"color": "#ff6b6b", "font": {"color": "white"}, "id": "register_i2c", "label": "I2C", "shape": "dot", "size": 33.5, "title": "\u540d\u79f0: I2C\n            \u7c7b\u578b: register\n            \u7f6e\u4fe1\u5ea6: 0.900\n            \u9875\u9762: 29\n            \u5ea6\u6570: 0\n            \u4e0a\u4e0b\u6587: SP \u5e73\u65b9\u6839\u5bc4\u5b58\u5668 .......210  \n\u8868 16-1 I2C \u5bc4\u5b58\u5668\u5730\u5740\u5206\u914d\u8868 . .238  \n\u8868 16-2 \u5730\u5740\u5bc4\u5b58\u5668 I2..."}, {"color": "#ff6b6b", "font": {"color": "white"}, "id": "register_can", "label": "CAN", "shape": "dot", "size": 33.5, "title": "\u540d\u79f0: CAN\n            \u7c7b\u578b: register\n            \u7f6e\u4fe1\u5ea6: 0.900\n            \u9875\u9762: 29\n            \u5ea6\u6570: 0\n            \u4e0a\u4e0b\u6587:  \u5934\u4fe1\u606f . ..........273  \n\u8868 19-7 CAN \u5bc4\u5b58\u5668\u5730\u5740\u5206\u914d .... .....276  \n\u8868 19-8 \u6a21\u5f0f..."}, {"color": "#ff6b6b", "font": {"color": "white"}, "id": "register_utimer", "label": "UTIMER", "shape": "dot", "size": 33.5, "title": "\u540d\u79f0: UTIMER\n            \u7c7b\u578b: register\n            \u7f6e\u4fe1\u5ea6: 0.900\n            \u9875\u9762: 29\n            \u5ea6\u6570: 0\n            \u4e0a\u4e0b\u6587: \u7528\u5b9a\u65f6\u5668\u914d\u7f6e\u5bc4\u5b58\u5668\u5730\u5740\u5206\u914d . .131  \n\u8868 11-5 UTIMER \u914d\u7f6e\u5bc4\u5b58\u5668 UTIMER_CFG .. .133  \n\u8868 11-6 \u6ee4..."}, {"color": "#ff6b6b", "font": {"color": "white"}, "id": "register_mcpwm_th00", "label": "MCPWM_TH00", "shape": "dot", "size": 33.5, "title": "\u540d\u79f0: MCPWM_TH00\n            \u7c7b\u578b: register\n            \u7f6e\u4fe1\u5ea6: 0.900\n            \u9875\u9762: 29\n            \u5ea6\u6570: 0\n            \u4e0a\u4e0b\u6587: \u5b58\u5728\u5f71\u5b50\u5bc4\u5b58\u5668\u7684\u5bc4\u5b58\u5668 ... .171  \n\u8868 13-5 MCPWM_TH00 \u914d\u7f6e\u5bc4\u5b58\u5668..... ...171  \n\u8868 13-6 MCPWM_TH..."}, {"color": "#ff6b6b", "font": {"color": "white"}, "id": "register_mcpwm_th01", "label": "MCPWM_TH01", "shape": "dot", "size": 33.5, "title": "\u540d\u79f0: MCPWM_TH01\n            \u7c7b\u578b: register\n            \u7f6e\u4fe1\u5ea6: 0.900\n            \u9875\u9762: 29\n            \u5ea6\u6570: 0\n            \u4e0a\u4e0b\u6587: 00 \u914d\u7f6e\u5bc4\u5b58\u5668..... ...171  \n\u8868 13-6 MCPWM_TH01 \u914d\u7f6e\u5bc4\u5b58\u5668........ ........172  \n\u8868 13-7 ..."}, {"color": "#ff6b6b", "font": {"color": "white"}, "id": "register_mcpwm_th10", "label": "MCPWM_TH10", "shape": "dot", "size": 33.5, "title": "\u540d\u79f0: MCPWM_TH10\n            \u7c7b\u578b: register\n            \u7f6e\u4fe1\u5ea6: 0.900\n            \u9875\u9762: 29\n            \u5ea6\u6570: 0\n            \u4e0a\u4e0b\u6587: ........ ........172  \n\u8868 13-7 MCPWM_TH10 \u914d\u7f6e\u5bc4\u5b58\u5668...... ....172  \n\u8868 13-8 MCPWM_..."}, {"color": "#ff6b6b", "font": {"color": "white"}, "id": "register_mcpwm_th11", "label": "MCPWM_TH11", "shape": "dot", "size": 33.5, "title": "\u540d\u79f0: MCPWM_TH11\n            \u7c7b\u578b: register\n            \u7f6e\u4fe1\u5ea6: 0.900\n            \u9875\u9762: 29\n            \u5ea6\u6570: 0\n            \u4e0a\u4e0b\u6587:  \u914d\u7f6e\u5bc4\u5b58\u5668...... ....172  \n\u8868 13-8 MCPWM_TH11 \u914d\u7f6e\u5bc4\u5b58\u5668..... .....173  \n\u8868 13-9 MCPWM_..."}, {"color": "#ff6b6b", "font": {"color": "white"}, "id": "register_mcpwm_th20", "label": "MCPWM_TH20", "shape": "dot", "size": 33.5, "title": "\u540d\u79f0: MCPWM_TH20\n            \u7c7b\u578b: register\n            \u7f6e\u4fe1\u5ea6: 0.900\n            \u9875\u9762: 29\n            \u5ea6\u6570: 0\n            \u4e0a\u4e0b\u6587:  \u914d\u7f6e\u5bc4\u5b58\u5668..... .....173  \n\u8868 13-9 MCPWM_TH20 \u914d\u7f6e\u5bc4\u5b58\u5668... .173  \n\u8868 13-10 MCPWM_TH21 ..."}, {"color": "#ff6b6b", "font": {"color": "white"}, "id": "register_mcpwm_th21", "label": "MCPWM_TH21", "shape": "dot", "size": 33.5, "title": "\u540d\u79f0: MCPWM_TH21\n            \u7c7b\u578b: register\n            \u7f6e\u4fe1\u5ea6: 0.900\n            \u9875\u9762: 29\n            \u5ea6\u6570: 0\n            \u4e0a\u4e0b\u6587: _TH20 \u914d\u7f6e\u5bc4\u5b58\u5668... .173  \n\u8868 13-10 MCPWM_TH21 \u914d\u7f6e\u5bc4\u5b58\u5668 ... .174  \n\u8868 13-11 MCPWM_TH30..."}, {"color": "#ff6b6b", "font": {"color": "white"}, "id": "register_mcpwm_th30", "label": "MCPWM_TH30", "shape": "dot", "size": 33.5, "title": "\u540d\u79f0: MCPWM_TH30\n            \u7c7b\u578b: register\n            \u7f6e\u4fe1\u5ea6: 0.900\n            \u9875\u9762: 29\n            \u5ea6\u6570: 0\n            \u4e0a\u4e0b\u6587: TH21 \u914d\u7f6e\u5bc4\u5b58\u5668 ... .174  \n\u8868 13-11 MCPWM_TH30 \u914d\u7f6e\u5bc4\u5b58\u5668 .. .174  \n\u8868 13-12 MCPWM_TH31 ..."}, {"color": "#ff6b6b", "font": {"color": "white"}, "id": "register_mcpwm_th31", "label": "MCPWM_TH31", "shape": "dot", "size": 33.5, "title": "\u540d\u79f0: MCPWM_TH31\n            \u7c7b\u578b: register\n            \u7f6e\u4fe1\u5ea6: 0.900\n            \u9875\u9762: 29\n            \u5ea6\u6570: 0\n            \u4e0a\u4e0b\u6587: _TH30 \u914d\u7f6e\u5bc4\u5b58\u5668 .. .174  \n\u8868 13-12 MCPWM_TH31 \u914d\u7f6e\u5bc4\u5b58\u5668 .. .174  \n\u8868 13-13 MCPWM_TMR0 ..."}, {"color": "#ff6b6b", "font": {"color": "white"}, "id": "register_mcpwm_tmr0", "label": "MCPWM_TMR0", "shape": "dot", "size": 33.5, "title": "\u540d\u79f0: MCPWM_TMR0\n            \u7c7b\u578b: register\n            \u7f6e\u4fe1\u5ea6: 0.900\n            \u9875\u9762: 29\n            \u5ea6\u6570: 0\n            \u4e0a\u4e0b\u6587: _TH31 \u914d\u7f6e\u5bc4\u5b58\u5668 .. .174  \n\u8868 13-13 MCPWM_TMR0 \u914d\u7f6e\u5bc4\u5b58\u5668 .. .175  \n\u8868 13-14 MCPWM_TMR1 ..."}, {"color": "#ff6b6b", "font": {"color": "white"}, "id": "register_mcpwm_tmr1", "label": "MCPWM_TMR1", "shape": "dot", "size": 33.5, "title": "\u540d\u79f0: MCPWM_TMR1\n            \u7c7b\u578b: register\n            \u7f6e\u4fe1\u5ea6: 0.900\n            \u9875\u9762: 29\n            \u5ea6\u6570: 0\n            \u4e0a\u4e0b\u6587: _TMR0 \u914d\u7f6e\u5bc4\u5b58\u5668 .. .175  \n\u8868 13-14 MCPWM_TMR1 \u914d\u7f6e\u5bc4\u5b58\u5668 ... .......175  \n\u8868 13-15 MCPW..."}, {"color": "#ff6b6b", "font": {"color": "white"}, "id": "register_mcpwm_tmr2", "label": "MCPWM_TMR2", "shape": "dot", "size": 33.5, "title": "\u540d\u79f0: MCPWM_TMR2\n            \u7c7b\u578b: register\n            \u7f6e\u4fe1\u5ea6: 0.900\n            \u9875\u9762: 29\n            \u5ea6\u6570: 0\n            \u4e0a\u4e0b\u6587: \u7f6e\u5bc4\u5b58\u5668 ... .......175  \n\u8868 13-15 MCPWM_TMR2 \u914d\u7f6e\u5bc4\u5b58\u5668 ... .................176  \n\u8868 ..."}, {"color": "#ff6b6b", "font": {"color": "white"}, "id": "register_mcpwm_tmr3", "label": "MCPWM_TMR3", "shape": "dot", "size": 33.5, "title": "\u540d\u79f0: MCPWM_TMR3\n            \u7c7b\u578b: register\n            \u7f6e\u4fe1\u5ea6: 0.900\n            \u9875\u9762: 29\n            \u5ea6\u6570: 0\n            \u4e0a\u4e0b\u6587: ................176  \n\u8868 13-16 MCPWM_TMR3 \u914d\u7f6e\u5bc4\u5b58\u5668 ... ....176  \n\u8868 13-17 MCPWM_T..."}, {"color": "#ff6b6b", "font": {"color": "white"}, "id": "register_mcpwm_th", "label": "MCPWM_TH", "shape": "dot", "size": 33.5, "title": "\u540d\u79f0: MCPWM_TH\n            \u7c7b\u578b: register\n            \u7f6e\u4fe1\u5ea6: 0.900\n            \u9875\u9762: 29\n            \u5ea6\u6570: 0\n            \u4e0a\u4e0b\u6587: 3 \u914d\u7f6e\u5bc4\u5b58\u5668 ... ....176  \n\u8868 13-17 MCPWM_TH \u914d\u7f6e\u5bc4\u5b58\u5668 ..  \n\u8868 13-18 MCPWM_UPDATE \u914d\u7f6e\u5bc4..."}, {"color": "#ff6b6b", "font": {"color": "white"}, "id": "register_mcpwm_update", "label": "MCPWM_UPDATE", "shape": "dot", "size": 33.5, "title": "\u540d\u79f0: MCPWM_UPDATE\n            \u7c7b\u578b: register\n            \u7f6e\u4fe1\u5ea6: 0.900\n            \u9875\u9762: 29\n            \u5ea6\u6570: 0\n            \u4e0a\u4e0b\u6587: 7 MCPWM_TH \u914d\u7f6e\u5bc4\u5b58\u5668 ..  \n\u8868 13-18 MCPWM_UPDATE \u914d\u7f6e\u5bc4\u5b58\u5668 .. .177  \n\u8868 13-19 MCPWM_IE \u914d\u7f6e..."}, {"color": "#ff6b6b", "font": {"color": "white"}, "id": "register_mcpwm_ie", "label": "MCPWM_IE", "shape": "dot", "size": 33.5, "title": "\u540d\u79f0: MCPWM_IE\n            \u7c7b\u578b: register\n            \u7f6e\u4fe1\u5ea6: 0.900\n            \u9875\u9762: 29\n            \u5ea6\u6570: 0\n            \u4e0a\u4e0b\u6587: PDATE \u914d\u7f6e\u5bc4\u5b58\u5668 .. .177  \n\u8868 13-19 MCPWM_IE \u914d\u7f6e\u5bc4\u5b58\u5668 ... ......178  \n\u8868 13-20 MCPWM..."}, {"color": "#ff6b6b", "font": {"color": "white"}, "id": "register_mcpwm_if", "label": "MCPWM_IF", "shape": "dot", "size": 33.5, "title": "\u540d\u79f0: MCPWM_IF\n            \u7c7b\u578b: register\n            \u7f6e\u4fe1\u5ea6: 0.900\n            \u9875\u9762: 29\n            \u5ea6\u6570: 0\n            \u4e0a\u4e0b\u6587: \u914d\u7f6e\u5bc4\u5b58\u5668 ... ......178  \n\u8868 13-20 MCPWM_IF \u914d\u7f6e\u5bc4\u5b58\u5668 . 179  \n\u8868 13-21 MCPWM_EIE \u914d\u7f6e\u5bc4..."}, {"color": "#ff6b6b", "font": {"color": "white"}, "id": "register_mcpwm_eie", "label": "MCPWM_EIE", "shape": "dot", "size": 33.5, "title": "\u540d\u79f0: MCPWM_EIE\n            \u7c7b\u578b: register\n            \u7f6e\u4fe1\u5ea6: 0.900\n            \u9875\u9762: 29\n            \u5ea6\u6570: 0\n            \u4e0a\u4e0b\u6587: CPWM_IF \u914d\u7f6e\u5bc4\u5b58\u5668 . 179  \n\u8868 13-21 MCPWM_EIE \u914d\u7f6e\u5bc4\u5b58\u5668 . .180  \n\u8868 13-22 MCPWM_EIF \u914d\u7f6e..."}, {"color": "#ff6b6b", "font": {"color": "white"}, "id": "register_mcpwm_eif", "label": "MCPWM_EIF", "shape": "dot", "size": 33.5, "title": "\u540d\u79f0: MCPWM_EIF\n            \u7c7b\u578b: register\n            \u7f6e\u4fe1\u5ea6: 0.900\n            \u9875\u9762: 29\n            \u5ea6\u6570: 0\n            \u4e0a\u4e0b\u6587: WM_EIE \u914d\u7f6e\u5bc4\u5b58\u5668 . .180  \n\u8868 13-22 MCPWM_EIF \u914d\u7f6e\u5bc4\u5b58\u5668 . .181  \n\u8868 13-23 MCPWM_RE \u914d\u7f6e\u5bc4..."}, {"color": "#ff6b6b", "font": {"color": "white"}, "id": "register_mcpwm_re", "label": "MCPWM_RE", "shape": "dot", "size": 33.5, "title": "\u540d\u79f0: MCPWM_RE\n            \u7c7b\u578b: register\n            \u7f6e\u4fe1\u5ea6: 0.900\n            \u9875\u9762: 29\n            \u5ea6\u6570: 0\n            \u4e0a\u4e0b\u6587: WM_EIF \u914d\u7f6e\u5bc4\u5b58\u5668 . .181  \n\u8868 13-23 MCPWM_RE \u914d\u7f6e\u5bc4\u5b58\u5668 .. ...182  \n\u8868 13-24 MCPWM_PP ..."}, {"color": "#ff6b6b", "font": {"color": "white"}, "id": "register_mcpwm_pp", "label": "MCPWM_PP", "shape": "dot", "size": 33.5, "title": "\u540d\u79f0: MCPWM_PP\n            \u7c7b\u578b: register\n            \u7f6e\u4fe1\u5ea6: 0.900\n            \u9875\u9762: 29\n            \u5ea6\u6570: 0\n            \u4e0a\u4e0b\u6587: _RE \u914d\u7f6e\u5bc4\u5b58\u5668 .. ...182  \n\u8868 13-24 MCPWM_PP \u914d\u7f6e\u5bc4\u5b58\u5668 ....... ........................"}, {"color": "#ff6b6b", "font": {"color": "white"}, "id": "register_mcpwm_io01", "label": "MCPWM_IO01", "shape": "dot", "size": 33.5, "title": "\u540d\u79f0: MCPWM_IO01\n            \u7c7b\u578b: register\n            \u7f6e\u4fe1\u5ea6: 0.900\n            \u9875\u9762: 29\n            \u5ea6\u6570: 0\n            \u4e0a\u4e0b\u6587: ................182  \n\u8868 13-25 MCPWM_IO01 \u914d\u7f6e\u5bc4\u5b58\u5668 ..... .....183  \n\u8868 13-26 MCPW..."}, {"color": "#ff6b6b", "font": {"color": "white"}, "id": "register_mcpwm_io23", "label": "MCPWM_IO23", "shape": "dot", "size": 33.5, "title": "\u540d\u79f0: MCPWM_IO23\n            \u7c7b\u578b: register\n            \u7f6e\u4fe1\u5ea6: 0.900\n            \u9875\u9762: 29\n            \u5ea6\u6570: 0\n            \u4e0a\u4e0b\u6587: \u7f6e\u5bc4\u5b58\u5668 ..... .....183  \n\u8868 13-26 MCPWM_IO23 \u914d\u7f6e\u5bc4\u5b58\u5668 .. .........184  \n\u8868 13-27 MCP..."}, {"color": "#ff6b6b", "font": {"color": "white"}, "id": "register_mcpwm_sdcfg", "label": "MCPWM_SDCFG", "shape": "dot", "size": 33.5, "title": "\u540d\u79f0: MCPWM_SDCFG\n            \u7c7b\u578b: register\n            \u7f6e\u4fe1\u5ea6: 0.900\n            \u9875\u9762: 29\n            \u5ea6\u6570: 0\n            \u4e0a\u4e0b\u6587: \u5bc4\u5b58\u5668 .. .........184  \n\u8868 13-27 MCPWM_SDCFG \u914d\u7f6e\u5bc4\u5b58\u5668... ...185  \n\u8868 13-28 MCPWM_TCL..."}, {"color": "#ff6b6b", "font": {"color": "white"}, "id": "register_mcpwm_tclk", "label": "MCPWM_TCLK", "shape": "dot", "size": 33.5, "title": "\u540d\u79f0: MCPWM_TCLK\n            \u7c7b\u578b: register\n            \u7f6e\u4fe1\u5ea6: 0.900\n            \u9875\u9762: 29\n            \u5ea6\u6570: 0\n            \u4e0a\u4e0b\u6587: CFG \u914d\u7f6e\u5bc4\u5b58\u5668... ...185  \n\u8868 13-28 MCPWM_TCLK \u914d\u7f6e\u5bc4\u5b58\u5668 . .......185  \n\u8868 13-29 MCPWM_..."}, {"color": "#ff6b6b", "font": {"color": "white"}, "id": "register_mcpwm_fail", "label": "MCPWM_FAIL", "shape": "dot", "size": 33.5, "title": "\u540d\u79f0: MCPWM_FAIL\n            \u7c7b\u578b: register\n            \u7f6e\u4fe1\u5ea6: 0.900\n            \u9875\u9762: 29\n            \u5ea6\u6570: 0\n            \u4e0a\u4e0b\u6587:  \u914d\u7f6e\u5bc4\u5b58\u5668 . .......185  \n\u8868 13-29 MCPWM_FAIL \u914d\u7f6e\u5bc4\u5b58\u5668 . .186  \n\u8868 13-30 MCPWM_PRT \u914d\u7f6e..."}, {"color": "#ff6b6b", "font": {"color": "white"}, "id": "register_mcpwm_prt", "label": "MCPWM_PRT", "shape": "dot", "size": 33.5, "title": "\u540d\u79f0: MCPWM_PRT\n            \u7c7b\u578b: register\n            \u7f6e\u4fe1\u5ea6: 0.900\n            \u9875\u9762: 29\n            \u5ea6\u6570: 0\n            \u4e0a\u4e0b\u6587: M_FAIL \u914d\u7f6e\u5bc4\u5b58\u5668 . .186  \n\u8868 13-30 MCPWM_PRT \u914d\u7f6e\u5bc4\u5b58\u5668 .. ...187  \n\u8868 13-31 MCPWM_CNT..."}, {"color": "#ff6b6b", "font": {"color": "white"}, "id": "register_mcpwm_cnt", "label": "MCPWM_CNT", "shape": "dot", "size": 33.5, "title": "\u540d\u79f0: MCPWM_CNT\n            \u7c7b\u578b: register\n            \u7f6e\u4fe1\u5ea6: 0.900\n            \u9875\u9762: 29\n            \u5ea6\u6570: 0\n            \u4e0a\u4e0b\u6587: PRT \u914d\u7f6e\u5bc4\u5b58\u5668 .. ...187  \n\u8868 13-31 MCPWM_CNT \u914d\u7f6e\u5bc4\u5b58\u5668.. .188  \n\u8868 13-32 MCPWM_DTH00 ..."}, {"color": "#ff6b6b", "font": {"color": "white"}, "id": "register_mcpwm_dth00", "label": "MCPWM_DTH00", "shape": "dot", "size": 33.5, "title": "\u540d\u79f0: MCPWM_DTH00\n            \u7c7b\u578b: register\n            \u7f6e\u4fe1\u5ea6: 0.900\n            \u9875\u9762: 29\n            \u5ea6\u6570: 0\n            \u4e0a\u4e0b\u6587: WM_CNT \u914d\u7f6e\u5bc4\u5b58\u5668.. .188  \n\u8868 13-32 MCPWM_DTH00 \u914d\u7f6e\u5bc4\u5b58\u5668 . .188  \n\u8868 13-33 MCPWM_DTH01 ..."}, {"color": "#ff6b6b", "font": {"color": "white"}, "id": "register_mcpwm_dth01", "label": "MCPWM_DTH01", "shape": "dot", "size": 33.5, "title": "\u540d\u79f0: MCPWM_DTH01\n            \u7c7b\u578b: register\n            \u7f6e\u4fe1\u5ea6: 0.900\n            \u9875\u9762: 29\n            \u5ea6\u6570: 0\n            \u4e0a\u4e0b\u6587: _DTH00 \u914d\u7f6e\u5bc4\u5b58\u5668 . .188  \n\u8868 13-33 MCPWM_DTH01 \u914d\u7f6e\u5bc4\u5b58\u5668 .189  \n\u8868 13-34 MCPWM_DTH10 \u914d\u7f6e..."}, {"color": "#ff6b6b", "font": {"color": "white"}, "id": "register_mcpwm_dth10", "label": "MCPWM_DTH10", "shape": "dot", "size": 33.5, "title": "\u540d\u79f0: MCPWM_DTH10\n            \u7c7b\u578b: register\n            \u7f6e\u4fe1\u5ea6: 0.900\n            \u9875\u9762: 29\n            \u5ea6\u6570: 0\n            \u4e0a\u4e0b\u6587: WM_DTH01 \u914d\u7f6e\u5bc4\u5b58\u5668 .189  \n\u8868 13-34 MCPWM_DTH10 \u914d\u7f6e\u5bc4\u5b58\u5668 . .189  \n\u8868 13-35 MCPWM_DTH11 ..."}, {"color": "#ff6b6b", "font": {"color": "white"}, "id": "register_mcpwm_dth11", "label": "MCPWM_DTH11", "shape": "dot", "size": 33.5, "title": "\u540d\u79f0: MCPWM_DTH11\n            \u7c7b\u578b: register\n            \u7f6e\u4fe1\u5ea6: 0.900\n            \u9875\u9762: 29\n            \u5ea6\u6570: 0\n            \u4e0a\u4e0b\u6587: _DTH10 \u914d\u7f6e\u5bc4\u5b58\u5668 . .189  \n\u8868 13-35 MCPWM_DTH11 \u914d\u7f6e\u5bc4\u5b58\u5668 . .190  \n\u8868 13-36 MCPWM_DTH20 ..."}, {"color": "#ff6b6b", "font": {"color": "white"}, "id": "register_mcpwm_dth20", "label": "MCPWM_DTH20", "shape": "dot", "size": 33.5, "title": "\u540d\u79f0: MCPWM_DTH20\n            \u7c7b\u578b: register\n            \u7f6e\u4fe1\u5ea6: 0.900\n            \u9875\u9762: 29\n            \u5ea6\u6570: 0\n            \u4e0a\u4e0b\u6587: _DTH11 \u914d\u7f6e\u5bc4\u5b58\u5668 . .190  \n\u8868 13-36 MCPWM_DTH20 \u914d\u7f6e\u5bc4\u5b58\u5668 ... ...190  \n\u8868 13-37 MCPWM_DT..."}, {"color": "#ff6b6b", "font": {"color": "white"}, "id": "register_mcpwm_dth21", "label": "MCPWM_DTH21", "shape": "dot", "size": 33.5, "title": "\u540d\u79f0: MCPWM_DTH21\n            \u7c7b\u578b: register\n            \u7f6e\u4fe1\u5ea6: 0.900\n            \u9875\u9762: 29\n            \u5ea6\u6570: 0\n            \u4e0a\u4e0b\u6587: 20 \u914d\u7f6e\u5bc4\u5b58\u5668 ... ...190  \n\u8868 13-37 MCPWM_DTH21 \u914d\u7f6e\u5bc4\u5b58\u5668 ....... .........190  \n\u8868 13-3..."}, {"color": "#ff6b6b", "font": {"color": "white"}, "id": "register_mcpwm_dth30", "label": "MCPWM_DTH30", "shape": "dot", "size": 33.5, "title": "\u540d\u79f0: MCPWM_DTH30\n            \u7c7b\u578b: register\n            \u7f6e\u4fe1\u5ea6: 0.900\n            \u9875\u9762: 29\n            \u5ea6\u6570: 0\n            \u4e0a\u4e0b\u6587: ...... .........190  \n\u8868 13-38 MCPWM_DTH30 \u914d\u7f6e\u5bc4\u5b58\u5668 .... ...191  \n\u8868 13-39 MCPWM_D..."}, {"color": "#ff6b6b", "font": {"color": "white"}, "id": "register_mcpwm_dth31", "label": "MCPWM_DTH31", "shape": "dot", "size": 33.5, "title": "\u540d\u79f0: MCPWM_DTH31\n            \u7c7b\u578b: register\n            \u7f6e\u4fe1\u5ea6: 0.900\n            \u9875\u9762: 29\n            \u5ea6\u6570: 0\n            \u4e0a\u4e0b\u6587: 0 \u914d\u7f6e\u5bc4\u5b58\u5668 .... ...191  \n\u8868 13-39 MCPWM_DTH31 \u914d\u7f6e\u5bc4\u5b58\u5668 ......... ........191  \n\u8868 14-..."}, {"color": "#ff6b6b", "font": {"color": "white"}, "id": "register_spi_div", "label": "SPI_DIV", "shape": "dot", "size": 33.5, "title": "\u540d\u79f0: SPI_DIV\n            \u7c7b\u578b: register\n            \u7f6e\u4fe1\u5ea6: 0.900\n            \u9875\u9762: 29\n            \u5ea6\u6570: 0\n            \u4e0a\u4e0b\u6587: I_IE \u4e2d\u65ad\u5bc4\u5b58\u5668 .. ...252  \n\u8868 17-4 SPI_DIV \u63a7\u5236\u5bc4\u5b58\u5668.. .253  \n\u8868 17-5 SPI_TX_DATA \u6570..."}, {"color": "#ff6b6b", "font": {"color": "white"}, "id": "register_volatile", "label": "Volatile", "shape": "dot", "size": 33.5, "title": "\u540d\u79f0: Volatile\n            \u7c7b\u578b: register\n            \u7f6e\u4fe1\u5ea6: 0.900\n            \u9875\u9762: 39\n            \u5ea6\u6570: 0\n            \u4e0a\u4e0b\u6587: t\uff0c\u5373\u4e0a\u7535\u590d\u4f4d\uff0c\u82af\u7247\u7cfb\u7edf\u4e0a\u7535\u65f6\u4ea7\u751f\u7684\u590d\u4f4d\u4fe1\u53f7NVR\uff1aNon-Volatile Register\uff0cflash \u4e2d\u533a\u522b\u4e8e main \u533a\u57df\u4e4b\u5916\u7684\u4e00\u5757\u5b58\u50a8\u533a\u57df..."}, {"color": "#4ecdc4", "font": {"color": "white"}, "id": "module_wdt", "label": "WDT", "shape": "dot", "size": 33.5, "title": "\u540d\u79f0: WDT\n            \u7c7b\u578b: module\n            \u7f6e\u4fe1\u5ea6: 0.900\n            \u9875\u9762: 39\n            \u5ea6\u6570: 0\n            \u4e0a\u4e0b\u6587: er\uff0c\u6570\u6a21\u8f6c\u6362\u5668  \nBGP\uff1aBandgap\uff0c\u5e26\u9699\u57fa\u51c6  \nWDT\uff1aWatch dog\uff0c\u770b\u95e8\u72d7  \nLSI\uff1aLow Speed..."}, {"color": "#96ceb4", "font": {"color": "white"}, "id": "parameter_32", "label": "32", "shape": "dot", "size": 33.5, "title": "\u540d\u79f0: 32\n            \u7c7b\u578b: parameter\n            \u7f6e\u4fe1\u5ea6: 0.900\n            \u9875\u9762: 39\n            \u5ea6\u6570: 0\n            \u4e0a\u4e0b\u6587: SI\uff1aLow Speed Internal Clock\uff0c\u5373 32kHZ RC \u65f6\u949f  \nHSI\uff1aHigh Speed Intern..."}, {"color": "#96ceb4", "font": {"color": "white"}, "id": "parameter_96", "label": "96", "shape": "dot", "size": 33.5, "title": "\u540d\u79f0: 96\n            \u7c7b\u578b: parameter\n            \u7f6e\u4fe1\u5ea6: 0.900\n            \u9875\u9762: 39\n            \u5ea6\u6570: 0\n            \u4e0a\u4e0b\u6587:  \nPLL\uff1aPhase Lock Loop Clock\uff0c\u5373 96MHz \u9501\u76f8\u73af\u65f6\u949f\uff0c\u901a\u5e38\u7528\u4f5c\u7cfb\u7edf\u9ad8\u901f\u65f6\u949fPOR\uff1aPower-On ..."}, {"color": "#96ceb4", "font": {"color": "white"}, "id": "parameter_16", "label": "16", "shape": "dot", "size": 33.5, "title": "\u540d\u79f0: 16\n            \u7c7b\u578b: parameter\n            \u7f6e\u4fe1\u5ea6: 0.900\n            \u9875\u9762: 39\n            \u5ea6\u6570: 0\n            \u4e0a\u4e0b\u6587: \u5b57\uff1a32 \u4f4d\u6570\u636e/\u6307\u4ee4\u3002  \n\u534a\u5b57\uff1a16 \u4f4d\u6570\u636e/\u6307\u4ee4\u3002  \n\u5b57\u8282\uff1a8 \u4f4d\u6570\u636e\u3002  \n\u53cc\u5b57\uff1a64 \u4f4d\u6570\u636e..."}, {"color": "#96ceb4", "font": {"color": "white"}, "id": "parameter_64", "label": "64", "shape": "dot", "size": 33.5, "title": "\u540d\u79f0: 64\n            \u7c7b\u578b: parameter\n            \u7f6e\u4fe1\u5ea6: 0.900\n            \u9875\u9762: 39\n            \u5ea6\u6570: 0\n            \u4e0a\u4e0b\u6587: \u5b57\uff1a16 \u4f4d\u6570\u636e/\u6307\u4ee4\u3002  \n\u5b57\u8282\uff1a8 \u4f4d\u6570\u636e\u3002  \n\u53cc\u5b57\uff1a64 \u4f4d\u6570\u636e\u3002  \nADC\uff1aAnalog-Digital Conve..."}, {"color": "#45b7d1", "font": {"color": "white"}, "id": "function_\u6682\u505c", "label": "\u6682\u505c", "shape": "dot", "size": 33.5, "title": "\u540d\u79f0: \u6682\u505c\n            \u7c7b\u578b: function\n            \u7f6e\u4fe1\u5ea6: 0.900\n            \u9875\u9762: 41\n            \u5ea6\u6570: 0\n            \u4e0a\u4e0b\u6587: \u7ea7\u4e0e\u5f53\u524d\u7684\u4f18\u5148\u7ea7\u8fdb\u884c\u6bd4\u8f83\uff0c\u5982\u679c\u65b0\u7684\u4f18\u5148\u7ea7\u66f4\u9ad8\uff0c\u5f53\u524d\u7684\u4efb\u52a1\u4f1a\u88ab\u6682\u505c\uff0c\u5904\u7406\u5668\u8fdb\u884c\u6838\u5fc3\u5bc4\u5b58\u5668\u5165\u6808\u4fdd\u6301\uff0c\u7136\u540e\u5f00\u59cb\u5904\u7406\u65b0\u7684\u5f02\u5e38\u7a0b\u5e8f\uff0c\u8fd9..."}, {"color": "#45b7d1", "font": {"color": "white"}, "id": "function_\u505c\u6b62", "label": "\u505c\u6b62", "shape": "dot", "size": 33.5, "title": "\u540d\u79f0: \u505c\u6b62\n            \u7c7b\u578b: function\n            \u7f6e\u4fe1\u5ea6: 0.900\n            \u9875\u9762: 42\n            \u5ea6\u6570: 0\n            \u4e0a\u4e0b\u6587: \u5f02\u5e38\uff0c\u4f1a\u5f15\u8d77\u7a0b\u5e8f\u63a7\u5236\u7684\u53d8\u5316\u3002\u5728\u5f02\u5e38\u53d1\u751f\u65f6\uff0c\u5904\u7406\u5668\u505c\u6b62\u5f53\u524d\u7684\u4efb\u52a1\uff0c\u8f6c\u800c\u6267\u884c\u5f02\u5e38\u5904\u7406\u7a0b\u5e8f\uff0c\u5f02\u5e38\u5904\u7406\u5b8c\u6210\u540e\uff0c\u4f1a\u7ee7\u7eed\u6267\u884c..."}, {"color": "#96ceb4", "font": {"color": "white"}, "id": "parameter_\u9891\u7387", "label": "\u9891\u7387", "shape": "dot", "size": 33.5, "title": "\u540d\u79f0: \u9891\u7387\n            \u7c7b\u578b: parameter\n            \u7f6e\u4fe1\u5ea6: 0.900\n            \u9875\u9762: 43\n            \u5ea6\u6570: 0\n            \u4e0a\u4e0b\u6587: \u53ef\u7f16\u7a0b\u8bbe\u7f6e\u9891\u7387\u7684RTOS \u5b9a\u65f6\u5668(\u4f8b\u5982 $\\lvert 1 0 0 \\ma..."}, {"color": "#feca57", "font": {"color": "white"}, "id": "address_0x00000000", "label": "0x00000000", "shape": "dot", "size": 33.5, "title": "\u540d\u79f0: 0x00000000\n            \u7c7b\u578b: address\n            \u7f6e\u4fe1\u5ea6: 0.900\n            \u9875\u9762: 43\n            \u5ea6\u6570: 0\n            \u4e0a\u4e0b\u6587: \u5730\u5740\uff1a0xE000_E010  \n\u590d\u4f4d\u503c\uff1a0x00000000..."}, {"color": "#96ceb4", "font": {"color": "white"}, "id": "parameter_\u7cbe\u5ea6", "label": "\u7cbe\u5ea6", "shape": "dot", "size": 33.5, "title": "\u540d\u79f0: \u7cbe\u5ea6\n            \u7c7b\u578b: parameter\n            \u7f6e\u4fe1\u5ea6: 0.900\n            \u9875\u9762: 46\n            \u5ea6\u6570: 0\n            \u4e0a\u4e0b\u6587: 1 . 2 \\mathrm { V } 0 . 8 \\%$ \u7cbe\u5ea6\u7535\u538b\u57fa\u51c6\u6e90..."}, {"color": "#96ceb4", "font": {"color": "white"}, "id": "parameter_12", "label": "12", "shape": "dot", "size": 33.5, "title": "\u540d\u79f0: 12\n            \u7c7b\u578b: parameter\n            \u7f6e\u4fe1\u5ea6: 0.900\n            \u9875\u9762: 46\n            \u5ea6\u6570: 0\n            \u4e0a\u4e0b\u6587: \u96c6\u6210 1 \u8def\u540c\u6b65\u53cc\u91c7\u6837\u7684 12bit SAR ADC\uff0c\u91c7\u6837\u53ca\u8f6c\u6362\u901f\u7387 3Msps\u3002\u6700\u591a 20 \u901a..."}, {"color": "#96ceb4", "font": {"color": "white"}, "id": "parameter_20", "label": "20", "shape": "dot", "size": 33.5, "title": "\u540d\u79f0: 20\n            \u7c7b\u578b: parameter\n            \u7f6e\u4fe1\u5ea6: 0.900\n            \u9875\u9762: 46\n            \u5ea6\u6570: 0\n            \u4e0a\u4e0b\u6587: 2bit SAR ADC\uff0c\u91c7\u6837\u53ca\u8f6c\u6362\u901f\u7387 3Msps\u3002\u6700\u591a 20 \u901a\u9053  \n\u96c6\u62104 \u8def\u8fd0\u7b97\u653e\u5927\u5668\uff0c\u53ef\u8bbe\u7f6e\u4e3a PGA \u6a21\u5f0f  \n\u96c6\u6210..."}, {"color": "#45b7d1", "font": {"color": "white"}, "id": "function_\u76d1\u6d4b", "label": "\u76d1\u6d4b", "shape": "dot", "size": 33.5, "title": "\u540d\u79f0: \u76d1\u6d4b\n            \u7c7b\u578b: function\n            \u7f6e\u4fe1\u5ea6: 0.900\n            \u9875\u9762: 47\n            \u5ea6\u6570: 0\n            \u4e0a\u4e0b\u6587: POR \u6a21\u5757\u76d1\u6d4bAVDD \u7684\u7535\u538b\uff0c\u5728AVDD \u7535\u538b\u4f4e\u4e8e3.0V \u65f6\uff08\u4f8b\u5982\u4e0a\u7535..."}, {"color": "#ff9ff3", "font": {"color": "white"}, "id": "bit_field_pvdsel", "label": "PVDSEL", "shape": "dot", "size": 33.5, "title": "\u540d\u79f0: PVDSEL\n            \u7c7b\u578b: bit_field\n            \u7f6e\u4fe1\u5ea6: 0.900\n            \u9875\u9762: 48\n            \u5ea6\u6570: 0\n            \u4e0a\u4e0b\u6587: PVDSEL[1:0]/ PD_PDT \u7684\u8bf4\u660e\u89c1\u6a21\u62df\u5bc4\u5b58\u5668 SYS_AFE_REG..."}, {"color": "#96ceb4", "font": {"color": "white"}, "id": "parameter_\u7535\u6d41", "label": "\u7535\u6d41", "shape": "dot", "size": 33.5, "title": "\u540d\u79f0: \u7535\u6d41\n            \u7c7b\u578b: parameter\n            \u7f6e\u4fe1\u5ea6: 0.900\n            \u9875\u9762: 48\n            \u5ea6\u6570: 0\n            \u4e0a\u4e0b\u6587:  \u5173\u95ed\uff09\uff0cRC \u65f6\u949f\u9700\u8981BGP \u7535\u538b\u57fa\u51c6\u6e90\u6a21\u5757\u63d0\u4f9b\u57fa\u51c6\u7535\u538b\u548c\u7535\u6d41\uff0c\u56e0\u6b64\u5f00\u542fRC \u65f6\u949f\u9700\u8981\u5148\u5f00\u542fBGP \u6a21\u5757\uff08 $\\mathr..."}, {"color": "#4ecdc4", "font": {"color": "white"}, "id": "module_pwm", "label": "PWM", "shape": "dot", "size": 33.5, "title": "\u540d\u79f0: PWM\n            \u7c7b\u578b: module\n            \u7f6e\u4fe1\u5ea6: 0.900\n            \u9875\u9762: 48\n            \u5ea6\u6570: 0\n            \u4e0a\u4e0b\u6587: \u7ed9 CPU\u3001ADC \u7b49\u6a21\u5757\u63d0\u4f9b\u66f4\u9ad8\u901f\u7684\u5de5\u4f5c\u65f6\u949f\u3002CPU \u548c PWM \u6a21\u5757\u7684\u6700\u9ad8\u65f6\u949f\u4e3a96MHz\uff0cADC \u6a21\u5757\u6700\u9ad8\u65f6\u949f $4 8..."}, {"color": "#ff9ff3", "font": {"color": "white"}, "id": "bit_field_adclksel", "label": "ADCLKSEL", "shape": "dot", "size": 33.5, "title": "\u540d\u79f0: ADCLKSEL\n            \u7c7b\u578b: bit_field\n            \u7f6e\u4fe1\u5ea6: 0.900\n            \u9875\u9762: 48\n            \u5ea6\u6570: 0\n            \u4e0a\u4e0b\u6587: \\mathrm { { M H z } }$ \uff0c\u901a\u8fc7\u5bc4\u5b58\u5668 ADCLKSEL[1:0]\u53ef\u8bbe\u7f6e\u4e0d\u540c\u7684 ADC\u5de5\u4f5c\u9891\u7387\u3002..."}, {"color": "#96ceb4", "font": {"color": "white"}, "id": "parameter_15", "label": "15", "shape": "dot", "size": 33.5, "title": "\u540d\u79f0: 15\n            \u7c7b\u578b: parameter\n            \u7f6e\u4fe1\u5ea6: 0.900\n            \u9875\u9762: 48\n            \u5ea6\u6570: 0\n            \u4e0a\u4e0b\u6587: \u5165\u4e00\u4e2a\u6676\u4f53\uff0c\u4e14\u5728OSC_IN/OSC_OUT \u5f15\u811a\u5404\u63a5\u4e00\u4e2a 15pF \u7535\u5bb9\u5230\u5730\uff0c\u8bbe\u7f6e XTALPDN $_ { = 1 }$ \u5373..."}, {"color": "#ff9ff3", "font": {"color": "white"}, "id": "bit_field_res_opax", "label": "RES_OPAx", "shape": "dot", "size": 33.5, "title": "\u540d\u79f0: RES_OPAx\n            \u7c7b\u578b: bit_field\n            \u7f6e\u4fe1\u5ea6: 0.900\n            \u9875\u9762: 49\n            \u5ea6\u6570: 0\n            \u4e0a\u4e0b\u6587: rm { R } } _ { 1 }$ \u7684\u963b\u503c\u53ef\u901a\u8fc7\u5bc4\u5b58\u5668 RES_OPAx[1:0]\u8bbe\u7f6e\uff0c\u4ee5\u5b9e\u73b0\u4e0d\u540c\u7684\u653e\u5927\u500d\u6570\u3002..."}, {"color": "#96ceb4", "font": {"color": "white"}, "id": "parameter_85", "label": "85", "shape": "dot", "size": 33.5, "title": "\u540d\u79f0: 85\n            \u7c7b\u578b: parameter\n            \u7f6e\u4fe1\u5ea6: 0.900\n            \u9875\u9762: 51\n            \u5ea6\u6570: 0\n            \u4e0a\u4e0b\u6587: \u5bc4\u5b58\u5668 DAC_GAIN\u003c1:0\u003e\u8bbe\u7f6e\u4e3a1.2V/3V/4.85V..."}, {"color": "#96ceb4", "font": {"color": "white"}, "id": "parameter_50", "label": "50", "shape": "dot", "size": 33.5, "title": "\u540d\u79f0: 50\n            \u7c7b\u578b: parameter\n            \u7f6e\u4fe1\u5ea6: 0.900\n            \u9875\u9762: 51\n            \u5ea6\u6570: 0\n            \u4e0a\u4e0b\u6587: 5 \\mathrm { k } \\Omega$ \u7684\u8d1f\u8f7d\u7535\u963b\u548c50pF \u7684\u8d1f\u8f7d\u7535\u5bb9\u3002..."}, {"color": "#ff6b6b", "font": {"color": "white"}, "id": "register_sys_afe_dac_amc", "label": "SYS_AFE_DAC_AMC", "shape": "dot", "size": 33.5, "title": "\u540d\u79f0: SYS_AFE_DAC_AMC\n            \u7c7b\u578b: register\n            \u7f6e\u4fe1\u5ea6: 0.900\n            \u9875\u9762: 51\n            \u5ea6\u6570: 0\n            \u4e0a\u4e0b\u6587: YS_AFE_DAC \u586b\u5165\u503c\uff08\u7406\u60f3\u503c\u5bf9\u5e94\u6570\u5b57\u91cf\uff09\u3002a \u6765\u81ea SYS_AFE_DAC_AMC \u5bc4\u5b58\u5668\uff0cb \u6765\u81eaSYS_AFE_DAC_DC\u3002\u786c\u4ef6\u6839\u636e SYS_A..."}, {"color": "#feca57", "font": {"color": "white"}, "id": "address_0x00000340", "label": "0x00000340", "shape": "dot", "size": 33.5, "title": "\u540d\u79f0: 0x00000340\n            \u7c7b\u578b: address\n            \u7f6e\u4fe1\u5ea6: 0.900\n            \u9875\u9762: 51\n            \u5ea6\u6570: 0\n            \u4e0a\u4e0b\u6587:  0 0 3 3 0$ \uff0c\u4e3a 3V \u91cf\u7a0b\u7684 a \u53c2\u6570\uff0c\u5730\u5740 0x00000340\uff0c\u4e3a 3V \u91cf\u7a0b\u7684 b \u53c2\u6570\u3002..."}, {"color": "#feca57", "font": {"color": "white"}, "id": "address_0x00000334", "label": "0x00000334", "shape": "dot", "size": 33.5, "title": "\u540d\u79f0: 0x00000334\n            \u7c7b\u578b: address\n            \u7f6e\u4fe1\u5ea6: 0.900\n            \u9875\u9762: 51\n            \u5ea6\u6570: 0\n            \u4e0a\u4e0b\u6587: \u5730\u5740 0x00000334\uff0c\u4e3a 1.2V \u91cf\u7a0b\u7684 a \u53c2\u6570\uff0c\u5730\u5740 0x00000344..."}, {"color": "#feca57", "font": {"color": "white"}, "id": "address_0x00000344", "label": "0x00000344", "shape": "dot", "size": 33.5, "title": "\u540d\u79f0: 0x00000344\n            \u7c7b\u578b: address\n            \u7f6e\u4fe1\u5ea6: 0.900\n            \u9875\u9762: 51\n            \u5ea6\u6570: 0\n            \u4e0a\u4e0b\u6587: 0x00000334\uff0c\u4e3a 1.2V \u91cf\u7a0b\u7684 a \u53c2\u6570\uff0c\u5730\u5740 0x00000344\uff0c\u4e3a 1.2V \u91cf\u7a0b\u7684 b \u53c2\u6570\u3002..."}, {"color": "#feca57", "font": {"color": "white"}, "id": "address_0x00000338", "label": "0x00000338", "shape": "dot", "size": 33.5, "title": "\u540d\u79f0: 0x00000338\n            \u7c7b\u578b: address\n            \u7f6e\u4fe1\u5ea6: 0.900\n            \u9875\u9762: 51\n            \u5ea6\u6570: 0\n            \u4e0a\u4e0b\u6587: \u5730\u5740 0x00000338\uff0c\u4e3a 4.85V \u91cf\u7a0b\u7684 a \u53c2\u6570\uff0c\u5730\u5740 0x0000034..."}, {"color": "#feca57", "font": {"color": "white"}, "id": "address_0x00000348", "label": "0x00000348", "shape": "dot", "size": 33.5, "title": "\u540d\u79f0: 0x00000348\n            \u7c7b\u578b: address\n            \u7f6e\u4fe1\u5ea6: 0.900\n            \u9875\u9762: 51\n            \u5ea6\u6570: 0\n            \u4e0a\u4e0b\u6587: x00000338\uff0c\u4e3a 4.85V \u91cf\u7a0b\u7684 a \u53c2\u6570\uff0c\u5730\u5740 0x00000348\uff0c\u4e3a 4.85V \u91cf\u7a0b\u7684 b \u53c2\u6570\u3002..."}, {"color": "#ff6b6b", "font": {"color": "white"}, "id": "register_sys_clk_cfg", "label": "SYS_CLK_CFG", "shape": "dot", "size": 33.5, "title": "\u540d\u79f0: SYS_CLK_CFG\n            \u7c7b\u578b: register\n            \u7f6e\u4fe1\u5ea6: 0.900\n            \u9875\u9762: 54\n            \u5ea6\u6570: 0\n            \u4e0a\u4e0b\u6587: MCLK \u662f\u7cfb\u7edf\u4e3b\u65f6\u949f\u3002\u53ef\u4ee5\u901a\u8fc7 SYS_CLK_CFG \u5bc4\u5b58\u5668 CLK_DIV \u4f4d\u57df\u63a7\u5236\u8fdb\u884c $\\mathrm { n }..."}, {"color": "#ff6b6b", "font": {"color": "white"}, "id": "register_sys_clk_fen", "label": "SYS_CLK_FEN", "shape": "dot", "size": 33.5, "title": "\u540d\u79f0: SYS_CLK_FEN\n            \u7c7b\u578b: register\n            \u7f6e\u4fe1\u5ea6: 0.900\n            \u9875\u9762: 54\n            \u5ea6\u6570: 0\n            \u4e0a\u4e0b\u6587: MCLK \u65f6\u949f\u7ecf\u8fc7 SYS_CLK_FEN \u5bc4\u5b58\u5668\u63a7\u5236\u7684\u5f00\u5173\u4e4b\u540e\u4f9b\u7ed9\u5916\u8bbe\u3002I2C \u65f6\u949f\u7531 SYS_CLK_DI..."}, {"color": "#ff6b6b", "font": {"color": "white"}, "id": "register_sys_clk_div0", "label": "SYS_CLK_DIV0", "shape": "dot", "size": 33.5, "title": "\u540d\u79f0: SYS_CLK_DIV0\n            \u7c7b\u578b: register\n            \u7f6e\u4fe1\u5ea6: 0.900\n            \u9875\u9762: 54\n            \u5ea6\u6570: 0\n            \u4e0a\u4e0b\u6587: LK_FEN \u5bc4\u5b58\u5668\u63a7\u5236\u7684\u5f00\u5173\u4e4b\u540e\u4f9b\u7ed9\u5916\u8bbe\u3002I2C \u65f6\u949f\u7531 SYS_CLK_DIV0 \u5bc4\u5b58\u5668\u63a7\u5236\u53ef\u4ee5\u8fdb\u4e00\u6b65\u5206\u9891\uff0cUART \u65f6\u949f\u7531SYS_CLK_DIV2..."}, {"color": "#96ceb4", "font": {"color": "white"}, "id": "parameter_48", "label": "48", "shape": "dot", "size": 33.5, "title": "\u540d\u79f0: 48\n            \u7c7b\u578b: parameter\n            \u7f6e\u4fe1\u5ea6: 0.900\n            \u9875\u9762: 54\n            \u5ea6\u6570: 0\n            \u4e0a\u4e0b\u6587: \u7684 $2 / 4 / 8$ \u5206\u9891\u540e\u9001\u81f3 ADC\uff08\u5178\u578b\u5de5\u4f5c\u9891\u738748MHz\uff09\uff0c\u5373 ACLK\u3002..."}, {"color": "#ff6b6b", "font": {"color": "white"}, "id": "register_sys_clk_slp", "label": "SYS_CLK_SLP", "shape": "dot", "size": 33.5, "title": "\u540d\u79f0: SYS_CLK_SLP\n            \u7c7b\u578b: register\n            \u7f6e\u4fe1\u5ea6: 0.900\n            \u9875\u9762: 55\n            \u5ea6\u6570: 0\n            \u4e0a\u4e0b\u6587: \u5411 SYS_CLK_SLP \u5bc4\u5b58\u5668\u5199\u5165 0xDEAD \u53ef\u4ee5\u4ee4\u82af\u7247\u51c6\u5907\u8fdb\u5165\u4f11\u7720\u72b6\u6001\uff0c\u4e4b\u540e\u7acb\u523b\u6267\u884c..."}, {"color": "#feca57", "font": {"color": "white"}, "id": "address_0xdead", "label": "0xDEAD", "shape": "dot", "size": 33.5, "title": "\u540d\u79f0: 0xDEAD\n            \u7c7b\u578b: address\n            \u7f6e\u4fe1\u5ea6: 0.900\n            \u9875\u9762: 55\n            \u5ea6\u6570: 0\n            \u4e0a\u4e0b\u6587: \u5411 SYS_CLK_SLP \u5bc4\u5b58\u5668\u5199\u5165 0xDEAD \u53ef\u4ee5\u4ee4\u82af\u7247\u51c6\u5907\u8fdb\u5165\u4f11\u7720\u72b6\u6001\uff0c\u4e4b\u540e\u7acb\u523b\u6267\u884c__WFI()\u5b8f\u6307..."}, {"color": "#ff9ff3", "font": {"color": "white"}, "id": "bit_field_p0", "label": "P0", "shape": "dot", "size": 33.5, "title": "\u540d\u79f0: P0\n            \u7c7b\u578b: bit_field\n            \u7f6e\u4fe1\u5ea6: 0.900\n            \u9875\u9762: 55\n            \u5ea6\u6570: 0\n            \u4e0a\u4e0b\u6587: \u4ec5\u6709 P0[1:0]\u3001P1[1:0]\u56db\u4e2a IO \u53ef\u4ee5\u4f5c\u4e3a\u5916\u90e8\u5524\u9192 IO \u4f7f\u7528\uff0c\u53ef..."}, {"color": "#ff9ff3", "font": {"color": "white"}, "id": "bit_field_p1", "label": "P1", "shape": "dot", "size": 33.5, "title": "\u540d\u79f0: P1\n            \u7c7b\u578b: bit_field\n            \u7f6e\u4fe1\u5ea6: 0.900\n            \u9875\u9762: 55\n            \u5ea6\u6570: 0\n            \u4e0a\u4e0b\u6587: \u4ec5\u6709 P0[1:0]\u3001P1[1:0]\u56db\u4e2a IO \u53ef\u4ee5\u4f5c\u4e3a\u5916\u90e8\u5524\u9192 IO \u4f7f\u7528\uff0c\u53ef\u4ee5\u914d\u7f6e\u72ec\u7acb\u7684\u4f7f\u80fd..."}, {"color": "#ff6b6b", "font": {"color": "white"}, "id": "register_sys_rst_src", "label": "SYS_RST_SRC", "shape": "dot", "size": 33.5, "title": "\u540d\u79f0: SYS_RST_SRC\n            \u7c7b\u578b: register\n            \u7f6e\u4fe1\u5ea6: 0.900\n            \u9875\u9762: 57\n            \u5ea6\u6570: 0\n            \u4e0a\u4e0b\u6587: SYS_RST_SRC \u5bc4\u5b58\u5668\u7528\u4e8e\u4fdd\u5b58\u786c\u4ef6\u590d\u4f4d\u4e8b\u4ef6\uff0c\u5f53\u67d0\u4e2a\u786c\u4ef6\u590d\u4f4d\u53d1\u751f\u540e\uff0cSYS_RST_..."}, {"color": "#ff6b6b", "font": {"color": "white"}, "id": "register_sys_clr_rst", "label": "SYS_CLR_RST", "shape": "dot", "size": 33.5, "title": "\u540d\u79f0: SYS_CLR_RST\n            \u7c7b\u578b: register\n            \u7f6e\u4fe1\u5ea6: 0.900\n            \u9875\u9762: 57\n            \u5ea6\u6570: 0\n            \u4e0a\u4e0b\u6587: _RST_SRC \u5bc4\u5b58\u5668\u672c\u8eab\u65e0\u6cd5\u88ab\u590d\u4f4d\u4fe1\u53f7\u590d\u4f4d\uff0c\u53ea\u80fd\u901a\u8fc7\u5411 SYS_CLR_RST \u5bc4\u5b58\u5668\u5199\u5165 0xCA40\u6e05\u7a7a\u8bb0\u5f55\uff0c\u590d\u4f4d\u8bb0\u5f55\u53ef\u4ee5\u65b9\u4fbf\u5730\u4e86\u89e3\u662f\u5426\u53d1\u751f\u4ee5..."}, {"color": "#ff9ff3", "font": {"color": "white"}, "id": "bit_field_sys_clk_cfg", "label": "SYS_CLK_CFG", "shape": "dot", "size": 33.5, "title": "\u540d\u79f0: SYS_CLK_CFG\n            \u7c7b\u578b: bit_field\n            \u7f6e\u4fe1\u5ea6: 0.900\n            \u9875\u9762: 69\n            \u5ea6\u6570: 0\n            \u4e0a\u4e0b\u6587: \u5f53\u4f7f\u75284MHz HSI \u65f6\u949f\u4f5c\u4e3a\u7cfb\u7edf\u4e3b\u65f6\u949f\u65f6\uff0cSYS_CLK_CFG[7:0]\u7684\u5206\u9891\u7cfb\u6570\u65e0\u6548\uff0c\u6700\u7ec8\u8f93\u51fa\u7684\u7cfb\u7edf\u65f6\u949f\u9891\u7387\u5373\u4e3a4MHz\u3002..."}, {"color": "#feca57", "font": {"color": "white"}, "id": "address_0xffffffff", "label": "0xFFFFFFFF", "shape": "dot", "size": 33.5, "title": "\u540d\u79f0: 0xFFFFFFFF\n            \u7c7b\u578b: address\n            \u7f6e\u4fe1\u5ea6: 0.900\n            \u9875\u9762: 76\n            \u5ea6\u6570: 0\n            \u4e0a\u4e0b\u6587: Flash \u6570\u636e\u9632\u7a83\u53d6\uff08\u6700\u540e\u4e00\u4e2a word \u987b\u5199\u5165\u975e 0xFFFFFFFF \u7684\u4efb\u610f\u503c\uff09..."}, {"color": "#ff6b6b", "font": {"color": "white"}, "id": "register_flash", "label": "FLASH", "shape": "dot", "size": 33.5, "title": "\u540d\u79f0: FLASH\n            \u7c7b\u578b: register\n            \u7f6e\u4fe1\u5ea6: 0.900\n            \u9875\u9762: 77\n            \u5ea6\u6570: 0\n            \u4e0a\u4e0b\u6587: \nFLASH \u7684\u8bfb\u53d6\u52a0\u901f\u64cd\u4f5c\uff0c\u4ee5\u63d0\u5347\u82af\u7247\u6574\u4f53\u8fd0\u884c\u6548\u7387\u3002  \nFLASH \u63a7\u5236\u5bc4\u5b58\u5668\u7684\u8bbf\u95ee\u3002..."}, {"color": "#96ceb4", "font": {"color": "white"}, "id": "parameter_24", "label": "24", "shape": "dot", "size": 33.5, "title": "\u540d\u79f0: 24\n            \u7c7b\u578b: parameter\n            \u7f6e\u4fe1\u5ea6: 0.900\n            \u9875\u9762: 79\n            \u5ea6\u6570: 0\n            \u4e0a\u4e0b\u6587: \u914d\u7f6e FLASH_CFG.TBS \u7684\u503c\uff0c\u4ee5\u5b9e\u73b0 48MHz/24MHz\u548c 12MHz \u7684\u8ba1\u6570\u503c\uff08\u5176\u5b83\u9891\u7387\u6682\u4e0d\u652f\u6301\uff09\u3002\u6700\u7ec8\u4fdd\u8bc1\u8ba1\u6570\u503c..."}, {"color": "#ff6b6b", "font": {"color": "white"}, "id": "register_flash_wdata", "label": "FLASH_WDATA", "shape": "dot", "size": 33.5, "title": "\u540d\u79f0: FLASH_WDATA\n            \u7c7b\u578b: register\n            \u7f6e\u4fe1\u5ea6: 0.900\n            \u9875\u9762: 79\n            \u5ea6\u6570: 0\n            \u4e0a\u4e0b\u6587: G.ADR_INC\uff0c\u5f00\u542f\u5730\u5740\u81ea\u52a8\u9012\u589e\u6a21\u5f0f\uff0c\u540e\u7eed\u53ea\u9700\u8981\u53cd\u590d\u5199 FLASH_WDATA \u5bc4\u5b58\u5668\u5373\u53ef\uff0cFLASH_ADDR \u6bcf\u6b21\u5199\u5165\u4e00\u6b21\u6570\u636e\u4f1a\u81ea\u52a8\u589e\u52a0 $0..."}, {"color": "#feca57", "font": {"color": "white"}, "id": "address_0x7654dcba", "label": "0x7654DCBA", "shape": "dot", "size": 33.5, "title": "\u540d\u79f0: 0x7654DCBA\n            \u7c7b\u578b: address\n            \u7f6e\u4fe1\u5ea6: 0.900\n            \u9875\u9762: 81\n            \u5ea6\u6570: 0\n            \u4e0a\u4e0b\u6587: ASH_ADDR \u7684\u503c\u5c06\u5931\u6548\u3002FLASH_ERASE \u5199\u5165 0x7654DCBA \u89e6\u53d1\u64e6\u9664\u64cd\u4f5c\u3002..."}, {"color": "#ff6b6b", "font": {"color": "white"}, "id": "register_flash_protect", "label": "FLASH_PROTECT", "shape": "dot", "size": 33.5, "title": "\u540d\u79f0: FLASH_PROTECT\n            \u7c7b\u578b: register\n            \u7f6e\u4fe1\u5ea6: 0.900\n            \u9875\u9762: 81\n            \u5ea6\u6570: 0\n            \u4e0a\u4e0b\u6587: \uff0c\u6267\u884c\u6700\u540e\u4e00\u4e2a WORD \u7684\u7f16\u7a0b\uff0c\u5199\u5165\u975e\u5168 1 \u7684\u503c\uff0c\u8bfb\u53d6 FLASH_PROTECT \u5bc4\u5b58\u5668\uff0c\u5373\u89e6\u53d1\u4e00\u6b21\u52a0\u5bc6\u72b6\u6001\u66f4\u65b0\uff0c\u5b8c\u6210\u52a0\u5bc6\uff08\u8bfb\u53d6FLASH_PROT..."}, {"color": "#4ecdc4", "font": {"color": "white"}, "id": "module_ram", "label": "RAM", "shape": "dot", "size": 33.5, "title": "\u540d\u79f0: RAM\n            \u7c7b\u578b: module\n            \u7f6e\u4fe1\u5ea6: 0.900\n            \u9875\u9762: 83\n            \u5ea6\u6570: 0\n            \u4e0a\u4e0b\u6587: \u5982\u679c\u9700\u8981\u5c06flash \u5168\u90e8\u64e6\u9664\uff0c\u5219\u9700\u8981\u5c06\u5728\u7ebf\u5347\u7ea7\u51fd\u6570\u653e\u7f6e\u5728 RAM \u4e2d\uff0c\u5982\u679c\u9700\u8981\u4f7f\u7528\u4e2d\u65ad\u5219\u65b0\u7684\u4e2d\u65ad\u5411\u91cf\u5165\u53e3\u5730\u5740\u4e5f\u9700\u8981\u4f4d\u4e8eRAM..."}, {"color": "#feca57", "font": {"color": "white"}, "id": "address_0x00000060", "label": "0x00000060", "shape": "dot", "size": 33.5, "title": "\u540d\u79f0: 0x00000060\n            \u7c7b\u578b: address\n            \u7f6e\u4fe1\u5ea6: 0.900\n            \u9875\u9762: 83\n            \u5ea6\u6570: 0\n            \u4e0a\u4e0b\u6587: \u5730\u5740\uff1a0x4000_0400  \n\u590d\u4f4d\u503c\uff1a0x00000060..."}, {"color": "#45b7d1", "font": {"color": "white"}, "id": "function_\u7981\u7528", "label": "\u7981\u7528", "shape": "dot", "size": 33.5, "title": "\u540d\u79f0: \u7981\u7528\n            \u7c7b\u578b: function\n            \u7f6e\u4fe1\u5ea6: 0.900\n            \u9875\u9762: 90\n            \u5ea6\u6570: 0\n            \u4e0a\u4e0b\u6587: A \u6a21\u5757\u53ef\u4ee5\u901a\u8fc7\u8bbe\u7f6e DMA_CTRL.EN \u4f4d\u4e3a 0 \u6765\u88ab\u7981\u7528\uff08\u8981\u6c42\u5173\u95ed DMA\u4f7f\u80fd\u524d\u5148\u5173\u95ed4 \u4e2a\u901a\u9053\u5bf9\u5e94\u7684\u4f7f\u80fd DMA_..."}, {"color": "#ff9ff3", "font": {"color": "white"}, "id": "bit_field_req_en", "label": "REQ_EN", "shape": "dot", "size": 33.5, "title": "\u540d\u79f0: REQ_EN\n            \u7c7b\u578b: bit_field\n            \u7f6e\u4fe1\u5ea6: 0.900\n            \u9875\u9762: 96\n            \u5ea6\u6570: 0\n            \u4e0a\u4e0b\u6587:  _ { 1 } }$ \u4ee5\u901a\u9053 0 \u4e3a\u4f8b\uff0cDMA_CCR0.REQ_EN[2:0]\u5206\u522b\u4e3a Timer1\u3001Timer0\u3001ADC0 \u7684 DMA \u8bf7..."}, {"color": "#ff6b6b", "font": {"color": "white"}, "id": "register_dma_ctmsx", "label": "DMA_CTMSx", "shape": "dot", "size": 33.5, "title": "\u540d\u79f0: DMA_CTMSx\n            \u7c7b\u578b: register\n            \u7f6e\u4fe1\u5ea6: 0.900\n            \u9875\u9762: 97\n            \u5ea6\u6570: 0\n            \u4e0a\u4e0b\u6587: DMA_CTMSx \u5bc4\u5b58\u5668\u53ea\u6709\u5728\u901a\u9053\u7981\u7528\uff0c\u5373 DMA_CCRx.EN $= 0$ \u4e4b\u540e..."}, {"color": "#45b7d1", "font": {"color": "white"}, "id": "function_\u542f\u52a8", "label": "\u542f\u52a8", "shape": "dot", "size": 33.5, "title": "\u540d\u79f0: \u542f\u52a8\n            \u7c7b\u578b: function\n            \u7f6e\u4fe1\u5ea6: 0.900\n            \u9875\u9762: 97\n            \u5ea6\u6570: 0\n            \u4e0a\u4e0b\u6587: \u901a\u5e38\u5728\u5f00\u542fDMA \u4e4b\u524d\u9700\u8981\u5c06\u89e6\u53d1 DMA \u542f\u52a8\u7684\u5916\u8bbe\u63a5\u6536\u4e2d\u65ad\u6807\u5fd7\u4f4d\u6e05\u96f6\uff0c\u9632\u6b62\u4e4b\u524d\u7559\u4e0b\u7684\u4e2d\u65ad\u6807\u5fd7\u4f4d\u6210\u4e3aDMA..."}, {"color": "#ff9ff3", "font": {"color": "white"}, "id": "bit_field_peri_addr", "label": "PERI_ADDR", "shape": "dot", "size": 33.5, "title": "\u540d\u79f0: PERI_ADDR\n            \u7c7b\u578b: bit_field\n            \u7f6e\u4fe1\u5ea6: 0.900\n            \u9875\u9762: 98\n            \u5ea6\u6570: 0\n            \u4e0a\u4e0b\u6587: \u65f6\uff0c\u5373\u914d\u7f6e\u4e3a\u4ee5 32bit \u4e3a\u5355\u4f4d\u642c\u8fd0\u5916\u8bbe\u6570\u636e\u3002CPARx.PERI_ADDR[1:0]\u503c\u65e0\u6548\uff0c\u5916\u8bbe\u5730\u5740\u4f1a\u4ee54 \u4e3a\u5355\u4f4d\u9012\u589e\u3002..."}, {"color": "#ff6b6b", "font": {"color": "white"}, "id": "register_dma_cparx", "label": "DMA_CPARx", "shape": "dot", "size": 33.5, "title": "\u540d\u79f0: DMA_CPARx\n            \u7c7b\u578b: register\n            \u7f6e\u4fe1\u5ea6: 0.900\n            \u9875\u9762: 98\n            \u5ea6\u6570: 0\n            \u4e0a\u4e0b\u6587: \u6ce8\u610f\uff1aDMA_CPARx \u5bc4\u5b58\u5668\u53ea\u6709\u5728\u901a\u9053\u7981\u7528\uff0c\u5373 DMA_CCRx.EN $= 0$ \u4e4b\u540e..."}, {"color": "#ff6b6b", "font": {"color": "white"}, "id": "register_sys", "label": "SYS", "shape": "dot", "size": 33.5, "title": "\u540d\u79f0: SYS\n            \u7c7b\u578b: register\n            \u7f6e\u4fe1\u5ea6: 0.900\n            \u9875\u9762: 98\n            \u5ea6\u6570: 0\n            \u4e0a\u4e0b\u6587:  \\mathrm { x } 4 0 0 0 0$ (\u5bf9\u5e94 SYS \u5bc4\u5b58\u5668)\u6216 $0 \\mathrm { x } 4 0 0 1 ^ ..."}, {"color": "#96ceb4", "font": {"color": "white"}, "id": "parameter_17", "label": "17", "shape": "dot", "size": 33.5, "title": "\u540d\u79f0: 17\n            \u7c7b\u578b: parameter\n            \u7f6e\u4fe1\u5ea6: 0.900\n            \u9875\u9762: 98\n            \u5ea6\u6570: 0\n            \u4e0a\u4e0b\u6587: \u8bbe\u95f4\u7684\u6570\u636e\u642c\u8fd0\uff0c\u56e0\u6b64 DMA_CPAR \u53ea\u5b58\u50a8\u5916\u8bbe\u5730\u5740\u7684\u4f4e 17 \u4f4d\uff0c\u9ad8 15 \u4f4d\u6052\u4e3a $0 \\mathrm { x } 2 0..."}, {"color": "#ff9ff3", "font": {"color": "white"}, "id": "bit_field_mem_addr", "label": "MEM_ADDR", "shape": "dot", "size": 33.5, "title": "\u540d\u79f0: MEM_ADDR\n            \u7c7b\u578b: bit_field\n            \u7f6e\u4fe1\u5ea6: 0.900\n            \u9875\u9762: 99\n            \u5ea6\u6570: 0\n            \u4e0a\u4e0b\u6587: \u65f6\uff0c\u5373\u914d\u7f6e\u4e3a\u4ee5 32bit \u4e3a\u5355\u4f4d\u642c\u8fd0\u5185\u5b58\u6570\u636e\u3002CMARx.MEM_ADDR[1:0]\u503c\u65e0\u6548\uff0c\u5185\u5b58\u5730\u5740\u4f1a\u4ee54 \u4e3a\u5355\u4f4d\u9012\u589e\u3002..."}, {"color": "#ff6b6b", "font": {"color": "white"}, "id": "register_dma_cmarx", "label": "DMA_CMARx", "shape": "dot", "size": 33.5, "title": "\u540d\u79f0: DMA_CMARx\n            \u7c7b\u578b: register\n            \u7f6e\u4fe1\u5ea6: 0.900\n            \u9875\u9762: 99\n            \u5ea6\u6570: 0\n            \u4e0a\u4e0b\u6587: \u6ce8\u610f\uff1aDMA_CMARx \u5bc4\u5b58\u5668\u53ea\u6709\u5728\u901a\u9053\u7981\u7528\uff0c\u5373 DMA_CCRx.EN=0 \u4e4b\u540e\u624d\u53ef\u4ee5\u5199..."}, {"color": "#96ceb4", "font": {"color": "white"}, "id": "parameter_13", "label": "13", "shape": "dot", "size": 33.5, "title": "\u540d\u79f0: 13\n            \u7c7b\u578b: parameter\n            \u7f6e\u4fe1\u5ea6: 0.900\n            \u9875\u9762: 99\n            \u5ea6\u6570: 0\n            \u4e0a\u4e0b\u6587: \u5185\u5b58\u4e0e\u5916\u8bbe\u95f4\u7684\u6570\u636e\u642c\u8fd0\uff0c\u56e0\u6b64DMA_CMAR \u53ea\u5b58\u50a8\u5730\u5740\u7684\u4f4e13 \u4f4d\uff0c\u5bf9\u5e94SRAM 8kB\u5730\u5740\u7a7a\u95f4\u3002\u9ad819 \u4f4d\u6052\u4e3a $0 \\ma..."}, {"color": "#96ceb4", "font": {"color": "white"}, "id": "parameter_19", "label": "19", "shape": "dot", "size": 33.5, "title": "\u540d\u79f0: 19\n            \u7c7b\u578b: parameter\n            \u7f6e\u4fe1\u5ea6: 0.900\n            \u9875\u9762: 99\n            \u5ea6\u6570: 0\n            \u4e0a\u4e0b\u6587: R \u53ea\u5b58\u50a8\u5730\u5740\u7684\u4f4e13 \u4f4d\uff0c\u5bf9\u5e94SRAM 8kB\u5730\u5740\u7a7a\u95f4\u3002\u9ad819 \u4f4d\u6052\u4e3a $0 \\mathrm { x } 1 0 0 0 0$..."}, {"color": "#feca57", "font": {"color": "white"}, "id": "address_0x40012000", "label": "0x40012000", "shape": "dot", "size": 33.5, "title": "\u540d\u79f0: 0x40012000\n            \u7c7b\u578b: address\n            \u7f6e\u4fe1\u5ea6: 0.900\n            \u9875\u9762: 102\n            \u5ea6\u6570: 0\n            \u4e0a\u4e0b\u6587: GPIO 0 \u6a21\u5757\u5728\u82af\u7247\u4e2d\u7684\u57fa\u5730\u5740\u662f 0x40012000\u3002  \nGPIO 1 \u6a21\u5757\u5728\u82af\u7247\u4e2d\u7684\u57fa\u5730\u5740\u662f 0x40012..."}, {"color": "#feca57", "font": {"color": "white"}, "id": "address_0x40012040", "label": "0x40012040", "shape": "dot", "size": 33.5, "title": "\u540d\u79f0: 0x40012040\n            \u7c7b\u578b: address\n            \u7f6e\u4fe1\u5ea6: 0.900\n            \u9875\u9762: 102\n            \u5ea6\u6570: 0\n            \u4e0a\u4e0b\u6587: 0012000\u3002  \nGPIO 1 \u6a21\u5757\u5728\u82af\u7247\u4e2d\u7684\u57fa\u5730\u5740\u662f 0x40012040\u3002  \nGPIO 2 \u6a21\u5757\u5728\u82af\u7247\u4e2d\u7684\u57fa\u5730\u5740\u662f 0x40012..."}, {"color": "#feca57", "font": {"color": "white"}, "id": "address_0x40012080", "label": "0x40012080", "shape": "dot", "size": 33.5, "title": "\u540d\u79f0: 0x40012080\n            \u7c7b\u578b: address\n            \u7f6e\u4fe1\u5ea6: 0.900\n            \u9875\u9762: 102\n            \u5ea6\u6570: 0\n            \u4e0a\u4e0b\u6587: 0012040\u3002  \nGPIO 2 \u6a21\u5757\u5728\u82af\u7247\u4e2d\u7684\u57fa\u5730\u5740\u662f 0x40012080\u3002  \nGPIO 3 \u6a21\u5757\u5728\u82af\u7247\u4e2d\u7684\u57fa\u5730\u5740\u662f 0x40012..."}, {"color": "#feca57", "font": {"color": "white"}, "id": "address_0x400120c0", "label": "0x400120C0", "shape": "dot", "size": 33.5, "title": "\u540d\u79f0: 0x400120C0\n            \u7c7b\u578b: address\n            \u7f6e\u4fe1\u5ea6: 0.900\n            \u9875\u9762: 102\n            \u5ea6\u6570: 0\n            \u4e0a\u4e0b\u6587: 0012080\u3002  \nGPIO 3 \u6a21\u5757\u5728\u82af\u7247\u4e2d\u7684\u57fa\u5730\u5740\u662f 0x400120C0\u3002  \nGPIO 0/1/2/3 \u7684\u5bc4\u5b58\u5668\u5b9a\u4e49\u5b8c\u5168\u76f8\u540c\uff0c\u4ec5\u57fa..."}, {"color": "#feca57", "font": {"color": "white"}, "id": "address_0x40012100", "label": "0x40012100", "shape": "dot", "size": 33.5, "title": "\u540d\u79f0: 0x40012100\n            \u7c7b\u578b: address\n            \u7f6e\u4fe1\u5ea6: 0.900\n            \u9875\u9762: 102\n            \u5ea6\u6570: 0\n            \u4e0a\u4e0b\u6587: GPIO \u4e2d\u65ad/\u5524\u9192/\u914d\u7f6e\u9501\u5b9a\u6a21\u5757\u7684\u57fa\u5730\u5740\u662f 0x40012100\uff0c\u5bc4\u5b58\u5668\u5217\u8868\u5982\u4e0b\uff1a..."}, {"color": "#feca57", "font": {"color": "white"}, "id": "address_0x4001", "label": "0x4001", "shape": "dot", "size": 33.5, "title": "\u540d\u79f0: 0x4001\n            \u7c7b\u578b: address\n            \u7f6e\u4fe1\u5ea6: 0.900\n            \u9875\u9762: 105\n            \u5ea6\u6570: 0\n            \u4e0a\u4e0b\u6587: \u5730\u5740\u5206\u522b\u662f\uff1a0x4001_2010\uff0c0x4001_2050\uff0c0x4001-2090\uff0c0x4001_20D0  \n\u590d\u4f4d\u503c\uff1a0x0..."}, {"color": "#feca57", "font": {"color": "white"}, "id": "address_0x3233", "label": "0x3233", "shape": "dot", "size": 33.5, "title": "\u540d\u79f0: 0x3233\n            \u7c7b\u578b: address\n            \u7f6e\u4fe1\u5ea6: 0.900\n            \u9875\u9762: 116\n            \u5ea6\u6570: 0\n            \u4e0a\u4e0b\u6587: SS or FAIL   \nif(GPIO0_PIE != 0x3233)FAIL;   \nif(GPIO1_PIE $\\ ! = ..."}, {"color": "#feca57", "font": {"color": "white"}, "id": "address_0x5ac4", "label": "0x5AC4", "shape": "dot", "size": 33.5, "title": "\u540d\u79f0: 0x5AC4\n            \u7c7b\u578b: address\n            \u7f6e\u4fe1\u5ea6: 0.900\n            \u9875\u9762: 116\n            \u5ea6\u6570: 0\n            \u4e0a\u4e0b\u6587: // write any value other than 0x5AC4 to enable lock protect GPIO0_..."}, {"color": "#feca57", "font": {"color": "white"}, "id": "address_0x0100", "label": "0x0100", "shape": "dot", "size": 33.5, "title": "\u540d\u79f0: 0x0100\n            \u7c7b\u578b: address\n            \u7f6e\u4fe1\u5ea6: 0.900\n            \u9875\u9762: 116\n            \u5ea6\u6570: 0\n            \u4e0a\u4e0b\u6587:  0 0 0 0$ ; if(GPIO0_LCKR  != 0x0100)FAIL;   \nif(GPIO1_LCKR != 0xF..."}, {"color": "#feca57", "font": {"color": "white"}, "id": "address_0xffff", "label": "0xFFFF", "shape": "dot", "size": 33.5, "title": "\u540d\u79f0: 0xFFFF\n            \u7c7b\u578b: address\n            \u7f6e\u4fe1\u5ea6: 0.900\n            \u9875\u9762: 116\n            \u5ea6\u6570: 0\n            \u4e0a\u4e0b\u6587: 100)FAIL;   \nif(GPIO1_LCKR != 0xFFFF)FAIL;   \nif(GPIO2_LCKR $\\math..."}, {"color": "#feca57", "font": {"color": "white"}, "id": "address_0x0000", "label": "0x0000", "shape": "dot", "size": 33.5, "title": "\u540d\u79f0: 0x0000\n            \u7c7b\u578b: address\n            \u7f6e\u4fe1\u5ea6: 0.900\n            \u9875\u9762: 116\n            \u5ea6\u6570: 0\n            \u4e0a\u4e0b\u6587: \\$ )FAIL;   \nif(GPIO1_LCKR != 0x0000)FAIL;   \nif(GPIO2_LCKR != 0x0..."}, {"color": "#feca57", "font": {"color": "white"}, "id": "address_0x0080", "label": "0x0080", "shape": "dot", "size": 33.5, "title": "\u540d\u79f0: 0x0080\n            \u7c7b\u578b: address\n            \u7f6e\u4fe1\u5ea6: 0.900\n            \u9875\u9762: 117\n            \u5ea6\u6570: 0\n            \u4e0a\u4e0b\u6587:  { \\tau } = \\mathbf { \\tau }$ 0x0080; // \u4f7f\u80fd P0[7]\u8f93\u5165  \nNVIC_EnableI..."}, {"color": "#45b7d1", "font": {"color": "white"}, "id": "function_\u901a\u4fe1", "label": "\u901a\u4fe1", "shape": "dot", "size": 33.5, "title": "\u540d\u79f0: \u901a\u4fe1\n            \u7c7b\u578b: function\n            \u7f6e\u4fe1\u5ea6: 0.900\n            \u9875\u9762: 118\n            \u5ea6\u6570: 0\n            \u4e0a\u4e0b\u6587: \u7801\uff08Cyclic Redundancy Check\uff09\uff1a\u662f\u6570\u636e\u901a\u4fe1\u9886\u57df\u4e2d\u6700\u5e38\u7528\u7684\u4e00\u79cd\u67e5\u9519\u6821\u9a8c\u7801\uff0c\u5176\u7279\u5f81\u662f\u4fe1\u606f\u5b57\u6bb5\u548c\u6821\u9a8c\u5b57\u6bb5\u7684\u957f..."}, {"color": "#ff6b6b", "font": {"color": "white"}, "id": "register_crc_dr", "label": "CRC_DR", "shape": "dot", "size": 33.5, "title": "\u540d\u79f0: CRC_DR\n            \u7c7b\u578b: register\n            \u7f6e\u4fe1\u5ea6: 0.900\n            \u9875\u9762: 121\n            \u5ea6\u6570: 0\n            \u4e0a\u4e0b\u6587: CRC_DR \u5bc4\u5b58\u5668\u65e2\u7528\u4e8e\u653e\u5165\u5f85\u6821\u9a8c\u6570\u636e\uff0c\u4e5f\u7528\u4e8e\u8fd4\u56de\u6821\u9a8c\u7ed3\u679c\u3002\u5199\u5165 CRC_DR..."}, {"color": "#ff6b6b", "font": {"color": "white"}, "id": "register_crc_init", "label": "CRC_INIT", "shape": "dot", "size": 33.5, "title": "\u540d\u79f0: CRC_INIT\n            \u7c7b\u578b: register\n            \u7f6e\u4fe1\u5ea6: 0.900\n            \u9875\u9762: 122\n            \u5ea6\u6570: 0\n            \u4e0a\u4e0b\u6587: \u9700\u8981\u6ce8\u610f\u7684\u662f\uff0c\u5411 CRC_CR.RESET \u5199\u5165 1 \u4f1a\u5c06 CRC_INIT \u5bc4\u5b58\u5668\u590d\u4f4d\u4e3a 0xFFFFFFFF\u3002..."}, {"color": "#ff9ff3", "font": {"color": "white"}, "id": "bit_field_adc_chn0", "label": "ADC_CHN0", "shape": "dot", "size": 33.5, "title": "\u540d\u79f0: ADC_CHN0\n            \u7c7b\u578b: bit_field\n            \u7f6e\u4fe1\u5ea6: 0.900\n            \u9875\u9762: 124\n            \u5ea6\u6570: 0\n            \u4e0a\u4e0b\u6587: 1 9$ \u4efb\u9009\uff0c\u82e5ADC_CHN0[4:0] $= 0$ \uff0cADC_CHN0[12:8] $^ { = 3 }$ \uff0c..."}, {"color": "#ff9ff3", "font": {"color": "white"}, "id": "bit_field_adc_amc", "label": "ADC_AMC", "shape": "dot", "size": 33.5, "title": "\u540d\u79f0: ADC_AMC\n            \u7c7b\u578b: bit_field\n            \u7f6e\u4fe1\u5ea6: 0.900\n            \u9875\u9762: 129\n            \u5ea6\u6570: 0\n            \u4e0a\u4e0b\u6587:  10bit \u65e0\u7b26\u53f7\u5b9a\u70b9\u6570\uff0cADC_AMC[9]\u4e3a\u6574\u6570\u90e8\u5206\uff0cADC_AMC[8:0]\u4e3a\u5c0f\u6570\u90e8\u5206\u3002\u53ef\u4ee5\u8868\u793a\u6570\u503c\u57281 \u9644\u8fd1\u7684\u5b9a\u70b9\u6570\u3002..."}, {"color": "#ff9ff3", "font": {"color": "white"}, "id": "bit_field_adcclksel", "label": "ADCCLKSEL", "shape": "dot", "size": 33.5, "title": "\u540d\u79f0: ADCCLKSEL\n            \u7c7b\u578b: bit_field\n            \u7f6e\u4fe1\u5ea6: 0.900\n            \u9875\u9762: 130\n            \u5ea6\u6570: 0\n            \u4e0a\u4e0b\u6587: \u548c PLL \u6a21\u5757\u3002\u901a\u8fc7\u914d\u7f6e\u5bc4\u5b58\u5668 SYS_AFE_REG7.ADCCLKSEL[5:4]\u8bbe\u7f6e ADC \u5de5\u4f5c\u9891\u7387\uff0c00 \u4e3a 48MHz\uff0c10 \u4e3a 12..."}, {"color": "#ff6b6b", "font": {"color": "white"}, "id": "register_data_align", "label": "DATA_ALIGN", "shape": "dot", "size": 33.5, "title": "\u540d\u79f0: DATA_ALIGN\n            \u7c7b\u578b: register\n            \u7f6e\u4fe1\u5ea6: 0.900\n            \u9875\u9762: 130\n            \u5ea6\u6570: 0\n            \u4e0a\u4e0b\u6587: \u51fa\u683c\u5f0f\u53ef\u914d\u7f6e\u4e3a\u5de6\u5bf9\u9f50\u6216\u8005\u53f3\u5bf9\u9f50\uff0c\u914d\u7f6e\u7684\u662f ADC0_CFG.DATA_ALIGN \u5bc4\u5b58\u5668\uff0c0 \u4e3a\u5de6\u5bf9\u9f50\uff0c1 \u4e3a\u53f3\u5bf9\u9f50\u3002..."}, {"color": "#ff9ff3", "font": {"color": "white"}, "id": "bit_field_trg_mode", "label": "TRG_MODE", "shape": "dot", "size": 33.5, "title": "\u540d\u79f0: TRG_MODE\n            \u7c7b\u578b: bit_field\n            \u7f6e\u4fe1\u5ea6: 0.900\n            \u9875\u9762: 130\n            \u5ea6\u6570: 0\n            \u4e0a\u4e0b\u6587:  \u6bb5 \u91c7 \u6837 \u6a21 \u5f0f \uff0c \u914d \u7f6e \u7684 \u662fADC0_TRIG.TRG_MODE[13:12]\u5bc4\u5b58\u5668\uff0c00 \u4e3a\u5355\u6bb5\u91c7\u6837\u6a21\u5f0f\uff0c01 \u4e3a\u4e24\u6bb5\u91c7\u6837\u6a21\u5f0f\uff0c11 \u4e3a..."}, {"color": "#ff9ff3", "font": {"color": "white"}, "id": "bit_field_single_tcnt", "label": "SINGLE_TCNT", "shape": "dot", "size": 33.5, "title": "\u540d\u79f0: SINGLE_TCNT\n            \u7c7b\u578b: bit_field\n            \u7f6e\u4fe1\u5ea6: 0.900\n            \u9875\u9762: 130\n            \u5ea6\u6570: 0\n            \u4e0a\u4e0b\u6587: \u4ee5\u8bbe\u7f6e\u89e6\u53d1\u4e00\u6b21\u91c7\u6837\u6240\u9700\u7684\u4e8b\u4ef6\u6570\uff0c\u914d\u7f6e\u7684\u662fADC0_TRIG.SINGLE_TCNT[11:8]\u5bc4\u5b58\u5668\uff0c\u8bbe\u7f6e\u8303\u56f4\u662f $_ { 0 \\sim 1 5 , 0 ..."}, {"color": "#ff6b6b", "font": {"color": "white"}, "id": "register_adc0_chnt1", "label": "ADC0_CHNT1", "shape": "dot", "size": 33.5, "title": "\u540d\u79f0: ADC0_CHNT1\n            \u7c7b\u578b: register\n            \u7f6e\u4fe1\u5ea6: 0.900\n            \u9875\u9762: 130\n            \u5ea6\u6570: 0\n            \u4e0a\u4e0b\u6587: \u6bb5\u91c7\u6837\u6a21\u5f0f\u4e0b\uff0c\u91c7\u6837\u7684\u901a\u9053\u4e2a\u6570\uff0c\u914d\u7f6e\u7684\u662fADC0_CHNT0\u3001ADC0_CHNT1 \u5bc4\u5b58\u5668\uff0c\u8bbe\u7f6e\u8303\u56f4\u662f $_ { 1 \\sim 2 0 }$ \uff0c1 \u4ee3..."}, {"color": "#ff6b6b", "font": {"color": "white"}, "id": "register_adc0_chnx", "label": "ADC0_CHNx", "shape": "dot", "size": 33.5, "title": "\u540d\u79f0: ADC0_CHNx\n            \u7c7b\u578b: register\n            \u7f6e\u4fe1\u5ea6: 0.900\n            \u9875\u9762: 146\n            \u5ea6\u6570: 0\n            \u4e0a\u4e0b\u6587: \u4ee5\u5355\u6bb5\u89e6\u53d1\u91c7\u68378 \u4e2a\u91c7\u6837\u4e3a\u4f8b\uff0cADC0_CHNx \u5bc4\u5b58\u5668\u91cc\u8bbe\u7f6e\u7684\u7b2c 0/1 \u4e2a\u91c7\u6837\u662f\u540c\u6b65\u91c7\u6837\u7684\uff0c2/3\u662f\u540c\u6b65\u91c7\u6837\u7684\uff0c..."}, {"color": "#feca57", "font": {"color": "white"}, "id": "address_0x5aa5", "label": "0x5AA5", "shape": "dot", "size": 33.5, "title": "\u540d\u79f0: 0x5AA5\n            \u7c7b\u578b: address\n            \u7f6e\u4fe1\u5ea6: 0.900\n            \u9875\u9762: 152\n            \u5ea6\u6570: 0\n            \u4e0a\u4e0b\u6587: \u6ce8\u610f\uff0c\u8f6f\u4ef6\u89e6\u53d1\u91c7\u96c6\u5bc4\u5b58\u5668\u4e3a\u53ea\u5199\u5bc4\u5b58\u5668\uff0c\u4e14\u53ea\u6709\u5199\u5165\u6570\u636e\u4e3a 0x5AA5 \u65f6\u4ea7\u751f\u8f6f\u4ef6\u89e6\u53d1\u4e8b\u4ef6\uff0c\u4e00\u6b21\u603b\u7ebf\u7684\u5199\u5165\u4ea7\u751f\u4e00\u6b21\u8f6f\u4ef6\u89e6\u53d1\uff0c\u6570\u636e\u5199..."}, {"color": "#ff9ff3", "font": {"color": "white"}, "id": "bit_field_adcamc", "label": "ADCAMC", "shape": "dot", "size": 33.5, "title": "\u540d\u79f0: ADCAMC\n            \u7c7b\u578b: bit_field\n            \u7f6e\u4fe1\u5ea6: 0.900\n            \u9875\u9762: 155\n            \u5ea6\u6570: 0\n            \u4e0a\u4e0b\u6587: \u4e3a 10bit \u65e0\u7b26\u53f7\u5b9a\u70b9\u6570\uff0cADCAMC[9]\u4e3a\u6574\u6570\u90e8\u5206\uff0cADCAMC[8:0]\u4e3a\u5c0f\u6570\u90e8\u5206\u3002\u6240\u5b58\u503c\u4e3a 1 \u5de6\u53f3\u3002..."}, {"color": "#96ceb4", "font": {"color": "white"}, "id": "parameter_54", "label": "54", "shape": "dot", "size": 33.5, "title": "\u540d\u79f0: 54\n            \u7c7b\u578b: parameter\n            \u7f6e\u4fe1\u5ea6: 0.900\n            \u9875\u9762: 155\n            \u5ea6\u6570: 0\n            \u4e0a\u4e0b\u6587: \u8868 10-54 \u901a\u9053 0 \u9608\u503c\u5bc4\u5b58\u5668 ADC0_DAT0_TH..."}, {"color": "#ff9ff3", "font": {"color": "white"}, "id": "bit_field_adc_chnt0", "label": "ADC_CHNT0", "shape": "dot", "size": 33.5, "title": "\u540d\u79f0: ADC_CHNT0\n            \u7c7b\u578b: bit_field\n            \u7f6e\u4fe1\u5ea6: 0.900\n            \u9875\u9762: 158\n            \u5ea6\u6570: 0\n            \u4e0a\u4e0b\u6587: TADC[0]\u6216\u8f6f\u4ef6\u89e6\u53d1\u53d1\u751f\u540e\uff0c\u5148\u8fdb\u884c ADC_CHNT0[4:0]\u6b21\u91c7\u6837\uff0c\u5b8c\u6210\u540e\u8fdb\u5165\u7a7a\u95f2\u72b6\u6001\u5e76\u7b49\u5f85\u4e0b\u4e00\u4e2a\u89e6\u53d1\u4fe1\u53f7\u7684\u5230\u6765\uff1bTAD..."}, {"color": "#ff6b6b", "font": {"color": "white"}, "id": "register_adc_swt", "label": "ADC_SWT", "shape": "dot", "size": 33.5, "title": "\u540d\u79f0: ADC_SWT\n            \u7c7b\u578b: register\n            \u7f6e\u4fe1\u5ea6: 0.900\n            \u9875\u9762: 159\n            \u5ea6\u6570: 0\n            \u4e0a\u4e0b\u6587: \u9700\u8981\u4f7f\u7528\u8f6f\u4ef6\u89e6\u53d1\u91c7\u6837\uff0c\u9700\u8981\u786e\u4fdd\u786c\u4ef6\u89e6\u53d1\u5df2\u7ecf\u5173\u95ed\u3002\u7136\u540e\u901a\u8fc7\u5411 ADC_SWT \u5bc4\u5b58\u5668\u5199\u5165 0x5AA5 \u4ee5\u4ea7\u751f\u4e00\u6b21\u8f6f\u4ef6\u89e6\u53d1\u3002..."}, {"color": "#ff9ff3", "font": {"color": "white"}, "id": "bit_field_adc_chnt1", "label": "ADC_CHNT1", "shape": "dot", "size": 33.5, "title": "\u540d\u79f0: ADC_CHNT1\n            \u7c7b\u578b: bit_field\n            \u7f6e\u4fe1\u5ea6: 0.900\n            \u9875\u9762: 159\n            \u5ea6\u6570: 0\n            \u4e0a\u4e0b\u6587: DC_CHNT0[4:0]\u3001ADC_CHNT0[12:8]\u3001ADC_CHNT1[4:0]\u3001ADC_CHNT1[12:8]\u3002..."}, {"color": "#feca57", "font": {"color": "white"}, "id": "address_0x0004", "label": "0x0004", "shape": "dot", "size": 33.5, "title": "\u540d\u79f0: 0x0004\n            \u7c7b\u578b: address\n            \u7f6e\u4fe1\u5ea6: 0.900\n            \u9875\u9762: 160\n            \u5ea6\u6570: 0\n            \u4e0a\u4e0b\u6587: ; //\u5173\u95ed ADC \u91c7\u6837\u89e6\u53d1  \nADC0_CFG |= 0x0004; //\u590d\u4f4dADC \u63a5\u53e3\u7535\u8def\u72b6\u6001\u673a//\u6b64\u5904\u8fdb\u884c\u7684 ADC \u91c7..."}, {"color": "#feca57", "font": {"color": "white"}, "id": "address_0x0005", "label": "0x0005", "shape": "dot", "size": 33.5, "title": "\u540d\u79f0: 0x0005\n            \u7c7b\u578b: address\n            \u7f6e\u4fe1\u5ea6: 0.900\n            \u9875\u9762: 160\n            \u5ea6\u6570: 0\n            \u4e0a\u4e0b\u6587: \u6837\u901a\u9053\u548c\u901a\u9053\u6570\u7684\u4fee\u6539\u4ec5\u4e3a\u793a\u4f8b  \nADC0_CHNT0 = 0x0005 //\u4fee\u6539ADC \u5355\u6bb5\u91c7\u6837\u901a\u9053\u6570\u4e3a5  \nADC0_CHN0..."}, {"color": "#feca57", "font": {"color": "white"}, "id": "address_0x0305", "label": "0x0305", "shape": "dot", "size": 33.5, "title": "\u540d\u79f0: 0x0305\n            \u7c7b\u578b: address\n            \u7f6e\u4fe1\u5ea6: 0.900\n            \u9875\u9762: 160\n            \u5ea6\u6570: 0\n            \u4e0a\u4e0b\u6587: \u4fee\u6539ADC \u5355\u6bb5\u91c7\u6837\u901a\u9053\u6570\u4e3a5  \nADC0_CHN0 = 0x0305; //\u4fee\u6539 ADC \u7b2c 0/1 \u6b21\u91c7\u6837\u901a\u9053\u4e3a\u6a21\u62df\u901a\u9053 5 ..."}, {"color": "#feca57", "font": {"color": "white"}, "id": "address_0x0604", "label": "0x0604", "shape": "dot", "size": 33.5, "title": "\u540d\u79f0: 0x0604\n            \u7c7b\u578b: address\n            \u7f6e\u4fe1\u5ea6: 0.900\n            \u9875\u9762: 160\n            \u5ea6\u6570: 0\n            \u4e0a\u4e0b\u6587:  { \\tau } = \\mathbf { \\tau }$ 0x0604; //\u4fee\u6539ADC \u7b2c2/3 \u6b21\u91c7\u6837\u901a\u9053\u4e3a\u6a21\u62df\u901a\u9053 4 \u548c6..."}, {"color": "#ff6b6b", "font": {"color": "white"}, "id": "register_utimer_reg", "label": "utimer_reg", "shape": "dot", "size": 33.5, "title": "\u540d\u79f0: utimer_reg\n            \u7c7b\u578b: register\n            \u7f6e\u4fe1\u5ea6: 0.900\n            \u9875\u9762: 162\n            \u5ea6\u6570: 0\n            \u4e0a\u4e0b\u6587: utimer_reg \u5bc4\u5b58\u5668\u6a21\u5757\uff0c\u5b9e\u73b0..."}, {"color": "#ff6b6b", "font": {"color": "white"}, "id": "register_utimer_untx_cmp", "label": "UTIMER_UNTx_CMP", "shape": "dot", "size": 33.5, "title": "\u540d\u79f0: UTIMER_UNTx_CMP\n            \u7c7b\u578b: register\n            \u7f6e\u4fe1\u5ea6: 0.900\n            \u9875\u9762: 166\n            \u5ea6\u6570: 0\n            \u4e0a\u4e0b\u6587: \u6cbf\uff0c\u53d1\u751f\u6355\u83b7\u4e8b\u4ef6\uff08\u5373\u8f93\u5165\u4fe1\u53f7\u7535\u5e73\u53d8\u5316\uff09\u65f6\uff0c\u5b9a\u65f6\u5668\u8ba1\u6570\u503c\u5b58\u5165 UTIMER_UNTx_CMP \u5bc4\u5b58\u5668\uff0c\u5e76\u4ea7\u751f\u6355\u83b7\u4e2d\u65ad\u3002\u8ba1\u6570\u5668\u56de\u96f6\u65f6\uff0c\u4ecd\u7136\u4f1a\u4ea7\u751f\u56de\u96f6\u4e2d\u65ad\u3002..."}, {"color": "#ff6b6b", "font": {"color": "white"}, "id": "register_encoderx", "label": "EncoderX", "shape": "dot", "size": 33.5, "title": "\u540d\u79f0: EncoderX\n            \u7c7b\u578b: register\n            \u7f6e\u4fe1\u5ea6: 0.900\n            \u9875\u9762: 187\n            \u5ea6\u6570: 0\n            \u4e0a\u4e0b\u6587: 11.3.5.1 UTIMER_ECDx_CFG  EncoderX \u914d\u7f6e\u5bc4\u5b58\u5668UTIMER_ECD0_CFG \u5730\u5740\uff1a0x4001_1880..."}, {"color": "#ff6b6b", "font": {"color": "white"}, "id": "register_clk_div", "label": "CLK_DIV", "shape": "dot", "size": 33.5, "title": "\u540d\u79f0: CLK_DIV\n            \u7c7b\u578b: register\n            \u7f6e\u4fe1\u5ea6: 0.900\n            \u9875\u9762: 193\n            \u5ea6\u6570: 0\n            \u4e0a\u4e0b\u6587: HALL\u6a21\u5757\u5de5\u4f5c\u9891\u7387\u53ef\u8c03\u3002\u901a\u8fc7\u914d\u7f6eHALL_CFG.CLK_DIV\u5bc4\u5b58\u5668\uff0c\u53ef\u4ee5\u9009\u62e9\u7cfb\u7edf\u4e3b\u65f6\u949f\u76841/2/4/8\u5206\u9891\u4f5c\u4e3aHALL \u6a21\u5757\u5de5..."}, {"color": "#ff9ff3", "font": {"color": "white"}, "id": "bit_field_fil_data", "label": "FIL_DATA", "shape": "dot", "size": 33.5, "title": "\u540d\u79f0: FIL_DATA\n            \u7c7b\u578b: bit_field\n            \u7f6e\u4fe1\u5ea6: 0.900\n            \u9875\u9762: 194\n            \u5ea6\u6570: 0\n            \u4e0a\u4e0b\u6587: \u901a\u8fc7\u8bbf\u95ee HALL_INFO.FIL_DATA[2:0]\u53ef\u4ee5\u6355\u6349\u6ee4\u6ce2\u540e\u7684 HALL \u4fe1\u53f7\uff1bHALL_INFO.RAW..."}, {"color": "#ff9ff3", "font": {"color": "white"}, "id": "bit_field_raw_data", "label": "RAW_DATA", "shape": "dot", "size": 33.5, "title": "\u540d\u79f0: RAW_DATA\n            \u7c7b\u578b: bit_field\n            \u7f6e\u4fe1\u5ea6: 0.900\n            \u9875\u9762: 194\n            \u5ea6\u6570: 0\n            \u4e0a\u4e0b\u6587: :0]\u53ef\u4ee5\u6355\u6349\u6ee4\u6ce2\u540e\u7684 HALL \u4fe1\u53f7\uff1bHALL_INFO.RAW_DATA[2:0]\u5219\u662f\u6ee4\u6ce2\u524d\u539f\u59cbHALL \u8f93\u5165\u4fe1\u53f7\uff0c\u8be6\u89c112.3.3\u3002..."}, {"color": "#96ceb4", "font": {"color": "white"}, "id": "parameter_42", "label": "42", "shape": "dot", "size": 33.5, "title": "\u540d\u79f0: 42\n            \u7c7b\u578b: parameter\n            \u7f6e\u4fe1\u5ea6: 0.900\n            \u9875\u9762: 194\n            \u5ea6\u6570: 0\n            \u4e0a\u4e0b\u6587: 6 { = } 1 . 4 0 s$ \u7684\u65f6\u95f4\u5bbd\u5ea6\uff0c\u8fbe\u523010.42ns \u7684\u65f6\u95f4\u5206\u8fa8\u7387\u3002..."}, {"color": "#96ceb4", "font": {"color": "white"}, "id": "parameter_\u5206\u8fa8\u7387", "label": "\u5206\u8fa8\u7387", "shape": "dot", "size": 33.5, "title": "\u540d\u79f0: \u5206\u8fa8\u7387\n            \u7c7b\u578b: parameter\n            \u7f6e\u4fe1\u5ea6: 0.900\n            \u9875\u9762: 194\n            \u5ea6\u6570: 0\n            \u4e0a\u4e0b\u6587: 1 . 4 0 s$ \u7684\u65f6\u95f4\u5bbd\u5ea6\uff0c\u8fbe\u523010.42ns \u7684\u65f6\u95f4\u5206\u8fa8\u7387\u3002..."}, {"color": "#ff9ff3", "font": {"color": "white"}, "id": "bit_field_mcpwm_bkin", "label": "MCPWM_BKIN", "shape": "dot", "size": 33.5, "title": "\u540d\u79f0: MCPWM_BKIN\n            \u7c7b\u578b: bit_field\n            \u7f6e\u4fe1\u5ea6: 0.900\n            \u9875\u9762: 201\n            \u5ea6\u6570: 0\n            \u4e0a\u4e0b\u6587: M\uff0c\u5373 FAIL0 \u548c FAIL1\uff0c\u5206\u522b\u53ef\u4ee5\u6765\u81ea\u82af\u7247 IO MCPWM_BKIN[1:0]\u6216\u82af\u7247\u5185\u90e8\u6bd4\u8f83\u5668\u7684\u8f93\u51fa CMP[1:0]\u3002..."}, {"color": "#ff9ff3", "font": {"color": "white"}, "id": "bit_field_cmp", "label": "CMP", "shape": "dot", "size": 33.5, "title": "\u540d\u79f0: CMP\n            \u7c7b\u578b: bit_field\n            \u7f6e\u4fe1\u5ea6: 0.900\n            \u9875\u9762: 201\n            \u5ea6\u6570: 0\n            \u4e0a\u4e0b\u6587: IO MCPWM_BKIN[1:0]\u6216\u82af\u7247\u5185\u90e8\u6bd4\u8f83\u5668\u7684\u8f93\u51fa CMP[1:0]\u3002..."}, {"color": "#ff9ff3", "font": {"color": "white"}, "id": "bit_field_io_flt_clkdiv", "label": "IO_FLT_CLKDIV", "shape": "dot", "size": 33.5, "title": "\u540d\u79f0: IO_FLT_CLKDIV\n            \u7c7b\u578b: bit_field\n            \u7f6e\u4fe1\u5ea6: 0.900\n            \u9875\u9762: 201\n            \u5ea6\u6570: 0\n            \u4e0a\u4e0b\u6587: MCPWM_BKIN[1:0]\u5219\u4f7f\u7528 MCPWM_TCLK.IO_FLT_CLKDIV[3:0]\u4f5c\u4e3a\u7b2c\u4e8c\u7ea7\u7684\u5206\u9891\u7cfb\u6570\uff1b\u5982\u679c Fail \u4fe1\u53f7\u6765\u81ea\u82af\u7247\u5185\u90e8\u6bd4\u8f83\u5668..."}, {"color": "#ff9ff3", "font": {"color": "white"}, "id": "bit_field_cmp_flt_clkdiv", "label": "CMP_FLT_CLKDIV", "shape": "dot", "size": 33.5, "title": "\u540d\u79f0: CMP_FLT_CLKDIV\n            \u7c7b\u578b: bit_field\n            \u7f6e\u4fe1\u5ea6: 0.900\n            \u9875\u9762: 201\n            \u5ea6\u6570: 0\n            \u4e0a\u4e0b\u6587:  \u4fe1\u53f7\u6765\u81ea\u82af\u7247\u5185\u90e8\u6bd4\u8f83\u5668\u8f93\u51fa\uff0c\u5219\u4f7f\u7528 MCPWM_TCLK.CMP_FLT_CLKDIV[3:0]\u4f5c\u4e3a\u7b2c\u4e8c\u7ea7\u7684\u5206\u9891\u7cfb\u6570\uff0c\u5982\u56fe 13-5 \u6240\u793a\u3002..."}, {"color": "#ff6b6b", "font": {"color": "white"}, "id": "register_chxp_default", "label": "CHxP_DEFAULT", "shape": "dot", "size": 33.5, "title": "\u540d\u79f0: CHxP_DEFAULT\n            \u7c7b\u578b: register\n            \u7f6e\u4fe1\u5ea6: 0.900\n            \u9875\u9762: 202\n            \u5ea6\u6570: 0\n            \u4e0a\u4e0b\u6587: FAIL.CHxN_DEFAULT \u548cMCPWM_FAIL.CHxP_DEFAULT \u5bc4\u5b58\u5668\u6240\u6307\u5b9a\u7684\u6545\u969c\u7f3a\u7701\u503c\uff0c\u6b64\u65f6 MCPWM_FAIL.CHxN_D..."}, {"color": "#ff9ff3", "font": {"color": "white"}, "id": "bit_field_fail", "label": "FAIL", "shape": "dot", "size": 33.5, "title": "\u540d\u79f0: FAIL\n            \u7c7b\u578b: bit_field\n            \u7f6e\u4fe1\u5ea6: 0.900\n            \u9875\u9762: 207\n            \u5ea6\u6570: 0\n            \u4e0a\u4e0b\u6587: \u5f53\u82af\u7247\u8c03\u8bd5\u4e2d\uff0cCPU Halt \u65f6\uff0cPWM \u505c\u6b62\u8f93\u51fa\uff0c\u8f93\u51fa FAIL[15:8]\u7684\u503c\u3002..."}, {"color": "#feca57", "font": {"color": "white"}, "id": "address_0x0c", "label": "0x0C", "shape": "dot", "size": 33.5, "title": "\u540d\u79f0: 0x0C\n            \u7c7b\u578b: address\n            \u7f6e\u4fe1\u5ea6: 0.900\n            \u9875\u9762: 234\n            \u5ea6\u6570: 0\n            \u4e0a\u4e0b\u6587: ART \u7684 Tx_buffer \u548c Rx_buffer \u5171\u4eab\u5730\u5740 0x0C \u5730\u5740\u3002\u5176\u4e2d\uff0cTx_buffer \u662f\u53ea\u5199\u7684\uff0cRx_buffe..."}, {"color": "#ff6b6b", "font": {"color": "white"}, "id": "register_dsp_cos", "label": "DSP_COS", "shape": "dot", "size": 33.5, "title": "\u540d\u79f0: DSP_COS\n            \u7c7b\u578b: register\n            \u7f6e\u4fe1\u5ea6: 0.900\n            \u9875\u9762: 242\n            \u5ea6\u6570: 0\n            \u4e0a\u4e0b\u6587:  \u4e3a\u8f93\u5165\uff0c\u8ba1\u7b97\u5e76\u8f93\u51fa sin/cos \u7ed3\u679c\u5230DSP_SIN/DSP_COS \u5bc4\u5b58\u5668\uff1b\u8ba1\u7b97 arctan \u65f6\u4ee5\u5750\u6807 DSP_X/DSP_Y \u4e3a\u8f93..."}, {"color": "#ff6b6b", "font": {"color": "white"}, "id": "register_dsp_mod", "label": "DSP_MOD", "shape": "dot", "size": 33.5, "title": "\u540d\u79f0: DSP_MOD\n            \u7c7b\u578b: register\n            \u7f6e\u4fe1\u5ea6: 0.900\n            \u9875\u9762: 242\n            \u5ea6\u6570: 0\n            \u4e0a\u4e0b\u6587: { 2 } \\right)$ \u5230 DSP_ARCTAN \u548c DSP_MOD \u5bc4\u5b58\u5668\u3002..."}, {"color": "#feca57", "font": {"color": "white"}, "id": "address_0x5555", "label": "0x5555", "shape": "dot", "size": 33.5, "title": "\u540d\u79f0: 0x5555\n            \u7c7b\u578b: address\n            \u7f6e\u4fe1\u5ea6: 0.900\n            \u9875\u9762: 261\n            \u5ea6\u6570: 0\n            \u4e0a\u4e0b\u6587: R5 0x5555 # Assign 0x5555 to R5   \nR1 2..."}, {"color": "#feca57", "font": {"color": "white"}, "id": "address_0x01000100", "label": "0x01000100", "shape": "dot", "size": 33.5, "title": "\u540d\u79f0: 0x01000100\n            \u7c7b\u578b: address\n            \u7f6e\u4fe1\u5ea6: 0.900\n            \u9875\u9762: 262\n            \u5ea6\u6570: 0\n            \u4e0a\u4e0b\u6587: \u4ee5\u5982\u4e0b DATA MEM \u5185\u5bb9\u4e3a\u4f8b\uff0c\u7b2c\u4e00\u884c\u6570\u636e 0x01000100 \u5bf9\u5e94 0x0 \u5730\u5740\uff0c\u7b2c\u4e8c\u884c\u6570\u636e 0x30005000\u5bf9\u5e94 ..."}, {"color": "#feca57", "font": {"color": "white"}, "id": "address_0x0003fff8", "label": "0x0003FFF8", "shape": "dot", "size": 33.5, "title": "\u540d\u79f0: 0x0003FFF8\n            \u7c7b\u578b: address\n            \u7f6e\u4fe1\u5ea6: 0.900\n            \u9875\u9762: 262\n            \u5ea6\u6570: 0\n            \u4e0a\u4e0b\u6587: \u884c\u6570\u636e 0x30005000\u5bf9\u5e94 0x1 \u5730\u5740\uff0c\u7b2c\u4e09\u884c\u6570\u636e 0x0003FFF8 \u5bf9\u5e94 $0 \\mathrm { x } 2$ \u5730\u5740\u3002..."}, {"color": "#feca57", "font": {"color": "white"}, "id": "address_0xffffe000", "label": "0xFFFFE000", "shape": "dot", "size": 33.5, "title": "\u540d\u79f0: 0xFFFFE000\n            \u7c7b\u578b: address\n            \u7f6e\u4fe1\u5ea6: 0.900\n            \u9875\u9762: 262\n            \u5ea6\u6570: 0\n            \u4e0a\u4e0b\u6587:  R3 \u7684 32bit \u6570\u636e\u5199\u5165 0x3 \u5730\u5740\u5e76\u8986\u76d6\u6389\u6570\u636e 0xFFFFE000\u3002..."}, {"color": "#feca57", "font": {"color": "white"}, "id": "address_0x30005000", "label": "0x30005000", "shape": "dot", "size": 33.5, "title": "\u540d\u79f0: 0x30005000\n            \u7c7b\u578b: address\n            \u7f6e\u4fe1\u5ea6: 0.900\n            \u9875\u9762: 262\n            \u5ea6\u6570: 0\n            \u4e0a\u4e0b\u6587: \u5f0f\u8fdb\u884c\u8ba1\u7b97\u3002\u4ee5\u5982\u4e0b DATA MEM \u5185\u5bb9\u4e3a\u4f8b\uff0c\u7b2c\u4e8c\u884c\u6570\u636e 0x30005000 \u5bf9\u5e94\u7684 CPU \u5bfb\u5740\u5730\u5740\u4e3a 0x4001_4804\uff0c\u7b2c\u4e09\u884c..."}, {"color": "#feca57", "font": {"color": "white"}, "id": "address_0x00100010", "label": "0x00100010", "shape": "dot", "size": 33.5, "title": "\u540d\u79f0: 0x00100010\n            \u7c7b\u578b: address\n            \u7f6e\u4fe1\u5ea6: 0.900\n            \u9875\u9762: 262\n            \u5ea6\u6570: 0\n            \u4e0a\u4e0b\u6587: 0x00100010   \n0x30005000   \n0x0003FFF8  ..."}, {"color": "#feca57", "font": {"color": "white"}, "id": "address_0x30004000", "label": "0x30004000", "shape": "dot", "size": 33.5, "title": "\u540d\u79f0: 0x30004000\n            \u7c7b\u578b: address\n            \u7f6e\u4fe1\u5ea6: 0.900\n            \u9875\u9762: 262\n            \u5ea6\u6570: 0\n            \u4e0a\u4e0b\u6587:  \n0x0003FFF8   \n0xFFFFE000   \n0x30004000   \n0x7FFFFFFF   \n0x7FFFFFFF  ..."}, {"color": "#feca57", "font": {"color": "white"}, "id": "address_0x7fffffff", "label": "0x7FFFFFFF", "shape": "dot", "size": 33.5, "title": "\u540d\u79f0: 0x7FFFFFFF\n            \u7c7b\u578b: address\n            \u7f6e\u4fe1\u5ea6: 0.900\n            \u9875\u9762: 262\n            \u5ea6\u6570: 0\n            \u4e0a\u4e0b\u6587:  \n0xFFFFE000   \n0x30004000   \n0x7FFFFFFF   \n0x7FFFFFFF   \n0xF0000003  ..."}, {"color": "#feca57", "font": {"color": "white"}, "id": "address_0xf0000003", "label": "0xF0000003", "shape": "dot", "size": 33.5, "title": "\u540d\u79f0: 0xF0000003\n            \u7c7b\u578b: address\n            \u7f6e\u4fe1\u5ea6: 0.900\n            \u9875\u9762: 262\n            \u5ea6\u6570: 0\n            \u4e0a\u4e0b\u6587:  \n0x7FFFFFFF   \n0x7FFFFFFF   \n0xF0000003   \n0x00007FFF # 8   \n0x000080..."}, {"color": "#feca57", "font": {"color": "white"}, "id": "address_0x00007fff", "label": "0x00007FFF", "shape": "dot", "size": 33.5, "title": "\u540d\u79f0: 0x00007FFF\n            \u7c7b\u578b: address\n            \u7f6e\u4fe1\u5ea6: 0.900\n            \u9875\u9762: 262\n            \u5ea6\u6570: 0\n            \u4e0a\u4e0b\u6587:  \n0x7FFFFFFF   \n0xF0000003   \n0x00007FFF # 8   \n0x00008000   \n0x800000..."}, {"color": "#feca57", "font": {"color": "white"}, "id": "address_0x00008000", "label": "0x00008000", "shape": "dot", "size": 33.5, "title": "\u540d\u79f0: 0x00008000\n            \u7c7b\u578b: address\n            \u7f6e\u4fe1\u5ea6: 0.900\n            \u9875\u9762: 262\n            \u5ea6\u6570: 0\n            \u4e0a\u4e0b\u6587: F0000003   \n0x00007FFF # 8   \n0x00008000   \n0x80000000   \n0x00000000..."}, {"color": "#feca57", "font": {"color": "white"}, "id": "address_0x80000000", "label": "0x80000000", "shape": "dot", "size": 33.5, "title": "\u540d\u79f0: 0x80000000\n            \u7c7b\u578b: address\n            \u7f6e\u4fe1\u5ea6: 0.900\n            \u9875\u9762: 262\n            \u5ea6\u6570: 0\n            \u4e0a\u4e0b\u6587: 00007FFF # 8   \n0x00008000   \n0x80000000   \n0x00000000..."}, {"color": "#ff6b6b", "font": {"color": "white"}, "id": "register_i2c_data", "label": "I2C_DATA", "shape": "dot", "size": 33.5, "title": "\u540d\u79f0: I2C_DATA\n            \u7c7b\u578b: register\n            \u7f6e\u4fe1\u5ea6: 0.900\n            \u9875\u9762: 269\n            \u5ea6\u6570: 0\n            \u4e0a\u4e0b\u6587: \u5730\u5740\u5339\u914d\u540e\uff0c\u4ece\u53d1\u9001\u5668\u5c06\u5b57\u8282\u4ece I2C_DATA \u5bc4\u5b58\u5668\u7ecf\u7531\u5185\u90e8\u79fb\u4f4d\u5bc4\u5b58\u5668\u53d1\u9001\u5230 SDA \u7ebf\u4e0a\u3002\u5728I2C_DATA ..."}, {"color": "#ff6b6b", "font": {"color": "white"}, "id": "register_i2c_mscr", "label": "I2C_MSCR", "shape": "dot", "size": 33.5, "title": "\u540d\u79f0: I2C_MSCR\n            \u7c7b\u578b: register\n            \u7f6e\u4fe1\u5ea6: 0.900\n            \u9875\u9762: 270\n            \u5ea6\u6570: 0\n            \u4e0a\u4e0b\u6587: 2C \u63a5\u53e3\u6267\u884c\u4e3b\u6a21\u5f0f\u4f20\u8f93\u4e4b\u524d\uff0c\u9700\u8981\u5224\u65ad\u603b\u7ebf\u662f\u5426\u7a7a\u95f2\u3002\u53ef\u8bfb\u53d6 I2C_MSCR \u5bc4\u5b58\u5668\u7684 BIT3\uff0c\u67e5..."}, {"color": "#ff6b6b", "font": {"color": "white"}, "id": "register_i2c_scr", "label": "I2C_SCR", "shape": "dot", "size": 33.5, "title": "\u540d\u79f0: I2C_SCR\n            \u7c7b\u578b: register\n            \u7f6e\u4fe1\u5ea6: 0.900\n            \u9875\u9762: 278\n            \u5ea6\u6570: 0\n            \u4e0a\u4e0b\u6587: \u4e00\u822c\uff0c\u8fdb\u5165\u4e2d\u65ad\u540e\uff0c\u9700\u8bfb\u53d6 I2C_SCR \u5bc4\u5b58\u5668\uff0c\u83b7\u5f97\u5f53\u524d I2C \u603b\u7ebf\u72b6\u6001\u53ca\u5f53\u524d\u4f20\u8f93\u5904\u4e8e\u4ec0\u4e48\u9636\u6bb5\uff1b\u7136\u540e\uff0c\u5bf9..."}, {"color": "#ff9ff3", "font": {"color": "white"}, "id": "bit_field_cfg", "label": "CFG", "shape": "dot", "size": 33.5, "title": "\u540d\u79f0: CFG\n            \u7c7b\u578b: bit_field\n            \u7f6e\u4fe1\u5ea6: 0.900\n            \u9875\u9762: 284\n            \u5ea6\u6570: 0\n            \u4e0a\u4e0b\u6587: CFG[7:6]\u914d\u7f6e\u4e3a2\uff0c\u534a\u53cc\u5de5\u53d1\u9001\u6a21\u5f0f\u6709\u6548\u3002\u6b64\u65f6\uff0c\u672c\u63a5\u53e3\u53ea\u80fd\u53d1\u9001\u6570\u636e\u3002GP..."}, {"color": "#ff6b6b", "font": {"color": "white"}, "id": "register_tx_data", "label": "TX_DATA", "shape": "dot", "size": 33.5, "title": "\u540d\u79f0: TX_DATA\n            \u7c7b\u578b: register\n            \u7f6e\u4fe1\u5ea6: 0.900\n            \u9875\u9762: 288\n            \u5ea6\u6570: 0\n            \u4e0a\u4e0b\u6587: \u914d\u7f6e\u5b8c\u6bd5\u3002\u6ce8\u610f SIZE \u53ea\u80fd\u914d\u7f6e\u4e3a 1\u3002  \nCPU \u5bf9 TX_DATA \u5bc4\u5b58\u5668\u6267\u884c\u5199\u64cd\u4f5c\uff0c\u89e6\u53d1 SPI \u63a5\u53e3\u8fdb\u5165\u53d1\u9001\u6d41\u7a0b\uff0c\u53d1\u9001\u7684\u6570\u636e\u6765\u81ea ..."}, {"color": "#ff9ff3", "font": {"color": "white"}, "id": "bit_field_baud", "label": "BAUD", "shape": "dot", "size": 33.5, "title": "\u540d\u79f0: BAUD\n            \u7c7b\u578b: bit_field\n            \u7f6e\u4fe1\u5ea6: 0.900\n            \u9875\u9762: 289\n            \u5ea6\u6570: 0\n            \u4e0a\u4e0b\u6587: SPI \u63a5\u53e3\u65f6\u949f\u901a\u8fc7\u5bf9\u7cfb\u7edf\u65f6\u949f\u5206\u9891\u83b7\u5f97\uff0c\u5206\u9891\u7cfb\u6570\u6765\u81ea BAUD[5:0]\u3002\u5206\u9891\u8303\u56f4\u662f $1 \\sim 1 2 8$ \uff0c\u5bf9\u5e94\u7684 BAU..."}, {"color": "#ff9ff3", "font": {"color": "white"}, "id": "bit_field_spi_cfg", "label": "SPI_CFG", "shape": "dot", "size": 33.5, "title": "\u540d\u79f0: SPI_CFG\n            \u7c7b\u578b: bit_field\n            \u7f6e\u4fe1\u5ea6: 0.900\n            \u9875\u9762: 290\n            \u5ea6\u6570: 0\n            \u4e0a\u4e0b\u6587: SPI_CFG[3:2]\u5bf9\u5e94\u7684\u901a\u8baf\u6ce2\u5f62\u6781\u6027\u548c\u76f8\u4f4d\u53ef\u53c2\u8003 17.3.2.4\u3002..."}, {"color": "#ff9ff3", "font": {"color": "white"}, "id": "bit_field_fil_clk_div16", "label": "FIL_CLK_DIV16", "shape": "dot", "size": 33.5, "title": "\u540d\u79f0: FIL_CLK_DIV16\n            \u7c7b\u578b: bit_field\n            \u7f6e\u4fe1\u5ea6: 0.900\n            \u9875\u9762: 295\n            \u5ea6\u6570: 0\n            \u4e0a\u4e0b\u6587:  \u8868\u793a\u6253\u5f00\uff0c0 \u8868\u793a\u5173\u95ed\uff1b\u901a\u8fc7\u914d\u7f6e\u5bc4\u5b58\u5668 CMP_TCLK.FIL_CLK_DIV16[7:4]\u53ef\u4ee5\u914d\u7f6e\u6ee4\u6ce2\u65f6\u949f\uff0c\u6570\u503c\u8bbe\u7f6e\u8303\u56f4\u662f $_ { 0 \\sim 1..."}, {"color": "#ff9ff3", "font": {"color": "white"}, "id": "bit_field_it_cm", "label": "IT_CM", "shape": "dot", "size": 33.5, "title": "\u540d\u79f0: IT_CM\n            \u7c7b\u578b: bit_field\n            \u7f6e\u4fe1\u5ea6: 0.900\n            \u9875\u9762: 295\n            \u5ea6\u6570: 0\n            \u4e0a\u4e0b\u6587:  \u53ef \u901a \u8fc7 \u914d \u7f6e \u5bc4 \u5b58 \u5668 SYS_AFE_REG1.IT_CM[1:0] \u8bbe \u7f6e \u4e3a$0 . 1 5 \\mathrm { u S }..."}, {"color": "#ff9ff3", "font": {"color": "white"}, "id": "bit_field_cmpx_selp", "label": "CMPx_SELP", "shape": "dot", "size": 33.5, "title": "\u540d\u79f0: CMPx_SELP\n            \u7c7b\u578b: bit_field\n            \u7f6e\u4fe1\u5ea6: 0.900\n            \u9875\u9762: 295\n            \u5ea6\u6570: 0\n            \u4e0a\u4e0b\u6587: \u53ef \u4ee5 \u901a \u8fc7 \u914d \u7f6e \u5bc4 \u5b58 \u5668SYS_AFE_REG3.CMPx_SELP[2:0]\u8fdb\u884c\u8bbe\u7f6e\uff0c\u8d1f\u7aef\u6709 4 \u79cd\u4fe1\u53f7\u6765\u6e90\u53ef\u9009\u62e9\uff0c\u53ef\u4ee5\u901a\u8fc7\u914d\u7f6e\u5bc4\u5b58\u5668 ..."}, {"color": "#ff9ff3", "font": {"color": "white"}, "id": "bit_field_cmpx_seln", "label": "CMPx_SELN", "shape": "dot", "size": 33.5, "title": "\u540d\u79f0: CMPx_SELN\n            \u7c7b\u578b: bit_field\n            \u7f6e\u4fe1\u5ea6: 0.900\n            \u9875\u9762: 295\n            \u5ea6\u6570: 0\n            \u4e0a\u4e0b\u6587: \u53f7\u6765\u6e90\u53ef\u9009\u62e9\uff0c\u53ef\u4ee5\u901a\u8fc7\u914d\u7f6e\u5bc4\u5b58\u5668 SYS_AFE_REG3.CMPx_SELN[1:0]\u8fdb\u884c\u8bbe\u7f6e\u3002..."}, {"color": "#ff6b6b", "font": {"color": "white"}, "id": "register_cmp_cfg", "label": "CMP_CFG", "shape": "dot", "size": 33.5, "title": "\u540d\u79f0: CMP_CFG\n            \u7c7b\u578b: register\n            \u7f6e\u4fe1\u5ea6: 0.900\n            \u9875\u9762: 299\n            \u5ea6\u6570: 0\n            \u4e0a\u4e0b\u6587: \u63a7\u5236\u4fe1\u53f7\u3002\u4f46\u6bd4\u8f83\u5668\u81ea\u8eab\u7684\u4e2d\u65ad\u4fe1\u53f7\u4ea7\u751f\u4e8e\u5f00\u7a97\u63a7\u5236\u65e0\u5173\uff0c\u4ec5\u4ec5\u53d7 CMP_CFG \u5bc4\u5b58\u5668\u5f71\u54cd\u3002..."}, {"color": "#ff9ff3", "font": {"color": "white"}, "id": "bit_field_blcwin", "label": "BLCWIN", "shape": "dot", "size": 33.5, "title": "\u540d\u79f0: BLCWIN\n            \u7c7b\u578b: bit_field\n            \u7f6e\u4fe1\u5ea6: 0.900\n            \u9875\u9762: 301\n            \u5ea6\u6570: 0\n            \u4e0a\u4e0b\u6587: \u901a\u5e38 CMP_ BLCWIN[3:0]\u6216 CMP_ BLCWIN[7:4]\u4e2d\u6709 1bit \u4e3a 1\uff0c..."}, {"color": "#96ceb4", "font": {"color": "white"}, "id": "parameter_29", "label": "29", "shape": "dot", "size": 33.5, "title": "\u540d\u79f0: 29\n            \u7c7b\u578b: parameter\n            \u7f6e\u4fe1\u5ea6: 0.900\n            \u9875\u9762: 302\n            \u5ea6\u6570: 0\n            \u4e0a\u4e0b\u6587:  11 \u4f4d ID \u683c\u5f0f\uff1b2.0B \u5305\u542b\u4e86 11 \u4f4dID \u548c 29 \u4f4d ID\u3002..."}, {"color": "#ff6b6b", "font": {"color": "white"}, "id": "register_can_eir", "label": "CAN_EIR", "shape": "dot", "size": 33.5, "title": "\u540d\u79f0: CAN_EIR\n            \u7c7b\u578b: register\n            \u7f6e\u4fe1\u5ea6: 0.900\n            \u9875\u9762: 304\n            \u5ea6\u6570: 0\n            \u4e0a\u4e0b\u6587: CAN \u63a5\u53e3\u5305\u542b\u4e2d\u65ad\u4e8b\u4ef6\u6bd4\u8f83\u591a\uff0c\u5728CAN_IR \u548c CAN_EIR \u5bc4\u5b58\u5668\u63cf\u8ff0\u4e2d\u6709\u76f8\u5e94\u8bf4\u660e\u3002\u6839\u636e\u5b9e\u9645\u4f7f\u7528\u60c5\u51b5\uff0c\u5f00\u542f\u5bf9\u5e94\u7684\u4e2d\u65ad\u4e8b\u4ef6\u4f7f\u80fd\u5f00..."}, {"color": "#ff6b6b", "font": {"color": "white"}, "id": "register_btr0", "label": "BTR0", "shape": "dot", "size": 33.5, "title": "\u540d\u79f0: BTR0\n            \u7c7b\u578b: register\n            \u7f6e\u4fe1\u5ea6: 0.900\n            \u9875\u9762: 305\n            \u5ea6\u6570: 0\n            \u4e0a\u4e0b\u6587: \u6210\u3002CAN_BTR0 \u4e3b\u8981\u662f\u914d\u7f6eTQ \u53c2\u6570\uff08TQ \u7684\u8ba1\u7b97\u89c1 BTR0 \u5bc4\u5b58\u5668\u8bf4\u660e\uff09\uff0cCAN_BTR1 \u4e3b\u8981\u5904\u74061-bit \u6570\u636e\u7684\u91c7\u6837\u70b9\u3001..."}, {"color": "#ff6b6b", "font": {"color": "white"}, "id": "register_mask", "label": "MASK", "shape": "dot", "size": 33.5, "title": "\u540d\u79f0: MASK\n            \u7c7b\u578b: register\n            \u7f6e\u4fe1\u5ea6: 0.900\n            \u9875\u9762: 307\n            \u5ea6\u6570: 0\n            \u4e0a\u4e0b\u6587: CAN_ACR \u5217\u51fa\u4e86\u4e00\u4e2a\u7279\u5b9a\u7684 ID\uff0cCAN_AMR \u4e3a MASK \u5bc4\u5b58\u5668\uff0c\u6807\u8bc6 CAN_ACR \u4e2d\u5bf9\u5e94\u4f4d\u6570\u636e\u540c\u63a5\u6536\u5230\u7684ID \u5bf9\u5e94\u4f4d\u6570..."}, {"color": "#ff9ff3", "font": {"color": "white"}, "id": "bit_field_acr1", "label": "ACR1", "shape": "dot", "size": 33.5, "title": "\u540d\u79f0: ACR1\n            \u7c7b\u578b: bit_field\n            \u7f6e\u4fe1\u5ea6: 0.900\n            \u9875\u9762: 308\n            \u5ea6\u6570: 0\n            \u4e0a\u4e0b\u6587: \u7528\u5230\uff0c\u5bf9\u5e94\u7684 AMR \u9700\u914d\u7f6e\u4e3a\u5168 1\u3002\u4f8b\u5982\u6ee4\u6ce2 ID1 \u7684 ACR1[3:0]\u548c ACR[3:0]\u3002  \nRTR \u662f\u5426\u9700\u8981\u5339\u914d\uff0c\u7ed3\u5408\u5b9e\u9645\u60c5..."}, {"color": "#ff9ff3", "font": {"color": "white"}, "id": "bit_field_acr", "label": "ACR", "shape": "dot", "size": 33.5, "title": "\u540d\u79f0: ACR\n            \u7c7b\u578b: bit_field\n            \u7f6e\u4fe1\u5ea6: 0.900\n            \u9875\u9762: 308\n            \u5ea6\u6570: 0\n            \u4e0a\u4e0b\u6587: \u9700\u914d\u7f6e\u4e3a\u5168 1\u3002\u4f8b\u5982\u6ee4\u6ce2 ID1 \u7684 ACR1[3:0]\u548c ACR[3:0]\u3002  \nRTR \u662f\u5426\u9700\u8981\u5339\u914d\uff0c\u7ed3\u5408\u5b9e\u9645\u60c5\u51b5\u914d\u7f6eAMR\u3002..."}, {"color": "#ff9ff3", "font": {"color": "white"}, "id": "bit_field_acr3", "label": "ACR3", "shape": "dot", "size": 33.5, "title": "\u540d\u79f0: ACR3\n            \u7c7b\u578b: bit_field\n            \u7f6e\u4fe1\u5ea6: 0.900\n            \u9875\u9762: 308\n            \u5ea6\u6570: 0\n            \u4e0a\u4e0b\u6587: \u6b64\u65f6\uff0c ACR3[1:0]\u6ca1\u6709\u7528\u5230\uff0c\u5bf9\u5e94\u7684 AMR \u9700\u914d\u7f6e\u4e3a\u5168 1\u3002..."}, {"color": "#ff6b6b", "font": {"color": "white"}, "id": "register_can_amc", "label": "CAN_AMC", "shape": "dot", "size": 33.5, "title": "\u540d\u79f0: CAN_AMC\n            \u7c7b\u578b: register\n            \u7f6e\u4fe1\u5ea6: 0.900\n            \u9875\u9762: 312\n            \u5ea6\u6570: 0\n            \u4e0a\u4e0b\u6587: \u5927\u6570\u636e\u91cf\u4e3a8 \u4e2a\u5b57\u8282\u3002RX FIFO \u4e2d\u6709\u51e0\u4e2a\u6709\u6548\u5e27\uff0c\u901a\u8fc7 CAN_AMC \u5bc4\u5b58\u5668\u53ef\u77e5\u3002\u901a\u8fc7\u8bfb\u53d6 CAN_TXRX \u5bc4\u5b58\u5668\u53ef\u4ee5\u83b7\u5f97\u6700\u5148\u88ab\u63a5\u6536\u5230..."}, {"color": "#ff6b6b", "font": {"color": "white"}, "id": "register_can_txrx", "label": "CAN_TXRX", "shape": "dot", "size": 33.5, "title": "\u540d\u79f0: CAN_TXRX\n            \u7c7b\u578b: register\n            \u7f6e\u4fe1\u5ea6: 0.900\n            \u9875\u9762: 312\n            \u5ea6\u6570: 0\n            \u4e0a\u4e0b\u6587: \u4e2d\u6709\u51e0\u4e2a\u6709\u6548\u5e27\uff0c\u901a\u8fc7 CAN_AMC \u5bc4\u5b58\u5668\u53ef\u77e5\u3002\u901a\u8fc7\u8bfb\u53d6 CAN_TXRX \u5bc4\u5b58\u5668\u53ef\u4ee5\u83b7\u5f97\u6700\u5148\u88ab\u63a5\u6536\u5230\u7684\u6570\u636e\u5e27\u3002\u82e5 RX FIFO \u6ee1\u4e86\uff0c\u629b\u5f03..."}, {"color": "#ff6b6b", "font": {"color": "white"}, "id": "register_can_ewlr", "label": "CAN_EWLR", "shape": "dot", "size": 33.5, "title": "\u540d\u79f0: CAN_EWLR\n            \u7c7b\u578b: register\n            \u7f6e\u4fe1\u5ea6: 0.900\n            \u9875\u9762: 314\n            \u5ea6\u6570: 0\n            \u4e0a\u4e0b\u6587: N \u603b\u7ebf\u9519\u8bef\uff0c\u4e14\u5728CAN \u63a7\u5236\u5668\u8fdb\u5165\u88ab\u52a8\u9519\u8bef\u72b6\u6001\u4e4b\u524d\u88ab\u89e6\u53d1\u3002CAN_EWLR \u5bc4\u5b58\u5668\u5b58\u50a8\u76f8\u5e94\u9608\u503c\uff0c\u6b64\u5bc4\u5b58\u5668\u7684\u914d\u7f6e\u5fc5\u987b\u5904\u4e8e\u590d\u4f4d\u6a21\u5f0f\u4e0b\u3002CAN_EW..."}, {"color": "#ff6b6b", "font": {"color": "white"}, "id": "register_can_ir", "label": "CAN_IR", "shape": "dot", "size": 33.5, "title": "\u540d\u79f0: CAN_IR\n            \u7c7b\u578b: register\n            \u7f6e\u4fe1\u5ea6: 0.900\n            \u9875\u9762: 318\n            \u5ea6\u6570: 0\n            \u4e0a\u4e0b\u6587: CAN_IR \u5bc4\u5b58\u5668\uff0c\u4e3a\u8bfb\u6e05\u9664\u5bc4\u5b58\u5668\u3002\u53ea\u6709 BIT0--RFIFO_N_EMPT..."}, {"color": "#ff6b6b", "font": {"color": "white"}, "id": "register_sys_wdt_clr", "label": "SYS_WDT_CLR", "shape": "dot", "size": 33.5, "title": "\u540d\u79f0: SYS_WDT_CLR\n            \u7c7b\u578b: register\n            \u7f6e\u4fe1\u5ea6: 0.900\n            \u9875\u9762: 333\n            \u5ea6\u6570: 0\n            \u4e0a\u4e0b\u6587: PROTECT \u5199\u5165 0xCAFE\uff0c\u5f00\u542f WatchDog SYS_WDT_CLR \u5bc4\u5b58\u5668\u5199\u5165\u3002..."}, {"color": "#feca57", "font": {"color": "white"}, "id": "address_0xcafe", "label": "0xCAFE", "shape": "dot", "size": 33.5, "title": "\u540d\u79f0: 0xCAFE\n            \u7c7b\u578b: address\n            \u7f6e\u4fe1\u5ea6: 0.900\n            \u9875\u9762: 333\n            \u5ea6\u6570: 0\n            \u4e0a\u4e0b\u6587: \u770b\u95e8\u72d7\u6e05\u96f6\u524d\uff0c\u9700\u8981\u5411 SYS_WR_PROTECT \u5199\u5165 0xCAFE\uff0c\u5f00\u542f WatchDog SYS_WDT_CLR \u5bc4\u5b58\u5668\u5199\u5165..."}]);
                  edges = new vis.DataSet([]);

                  nodeColors = {};
                  allNodes = nodes.get({ returnType: "Object" });
                  for (nodeId in allNodes) {
                    nodeColors[nodeId] = allNodes[nodeId].color;
                  }
                  allEdges = edges.get({ returnType: "Object" });
                  // adding nodes and edges to the graph
                  data = {nodes: nodes, edges: edges};

                  var options = {"physics": {"enabled": true, "stabilization": {"iterations": 300}, "barnesHut": {"gravitationalConstant": -8000, "centralGravity": 0.3, "springLength": 95, "springConstant": 0.04, "damping": 0.09}}, "nodes": {"font": {"size": 14, "color": "white"}, "borderWidth": 2, "shadow": true}, "edges": {"font": {"size": 10, "color": "white"}, "shadow": true, "smooth": {"type": "continuous"}}};

                  


                  

                  network = new vis.Network(container, data, options);

                  

                  

                  


                  
                      network.on("stabilizationProgress", function(params) {
                          document.getElementById('loadingBar').removeAttribute("style");
                          var maxWidth = 496;
                          var minWidth = 20;
                          var widthFactor = params.iterations/params.total;
                          var width = Math.max(minWidth,maxWidth * widthFactor);
                          document.getElementById('bar').style.width = width + 'px';
                          document.getElementById('text').innerHTML = Math.round(widthFactor*100) + '%';
                      });
                      network.once("stabilizationIterationsDone", function() {
                          document.getElementById('text').innerHTML = '100%';
                          document.getElementById('bar').style.width = '496px';
                          document.getElementById('loadingBar').style.opacity = 0;
                          // really clean the dom element
                          setTimeout(function () {document.getElementById('loadingBar').style.display = 'none';}, 500);
                      });
                  

                  return network;

              }
              drawGraph();
        </script>
    </body>
</html>