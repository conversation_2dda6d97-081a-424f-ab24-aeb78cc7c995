#!/usr/bin/env python3
"""
文件阅读智能体使用示例
"""
import asyncio
import sys
import os

# 添加路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from config.models_config import get_agent_model_client
from config.agents_config import AGENT_SYSTEM_PROMPTS, AGENT_CAPABILITIES
from agents.file_reader import FileReader

async def simple_usage():
    """简单使用示例"""
    print("📖 文件阅读智能体使用示例")
    print("=" * 40)
    
    # 1. 初始化智能体
    print("🔧 初始化智能体...")
    model_client = get_agent_model_client("DocumentReader")
    system_message = AGENT_SYSTEM_PROMPTS["DocumentReader"]
    capabilities = AGENT_CAPABILITIES["DocumentReader"]
    
    file_reader = FileReader(model_client, system_message, capabilities)
    print("✅ 智能体初始化完成")
    
    # 2. 使用默认文档（full.md）
    print("\n📄 使用默认文档:")
    response1 = await file_reader.handle_message("SYS_AFE_REG1寄存器的功能是什么？")
    print(f"回答: {response1[:200]}...")
    
    # 3. 读取指定文件
    print("\n📁 读取指定文件:")
    response2 = await file_reader.handle_message("读取文件 config/agents_config.py 并总结配置内容")
    print(f"回答: {response2[:200]}...")
    
    # 4. 配置调整
    print("\n⚙️  调整配置:")
    file_reader.configure_limits(max_file_size=50*1024*1024)  # 50MB
    limits = file_reader.get_current_limits()
    print(f"新限制: {limits}")

async def batch_processing():
    """批量处理示例"""
    print("\n📦 批量处理示例")
    print("-" * 30)
    
    # 初始化
    model_client = get_agent_model_client("DocumentReader")
    system_message = AGENT_SYSTEM_PROMPTS["DocumentReader"]
    capabilities = AGENT_CAPABILITIES["DocumentReader"]
    file_reader = FileReader(model_client, system_message, capabilities)
    
    # 批量查询
    queries = [
        "GPIO模块的主要功能",
        "ADC采样的配置方法", 
        "UART通信的波特率设置"
    ]
    
    for i, query in enumerate(queries, 1):
        print(f"\n🔍 查询 {i}: {query}")
        try:
            response = await file_reader.handle_message(query)
            print(f"✅ 回答长度: {len(response)} 字符")
        except Exception as e:
            print(f"❌ 失败: {e}")

class FileReaderWrapper:
    """文件阅读智能体包装类 - 更方便的使用接口"""
    
    def __init__(self):
        self.file_reader = None
        self.initialized = False
    
    async def init(self):
        """异步初始化"""
        if not self.initialized:
            model_client = get_agent_model_client("DocumentReader")
            system_message = AGENT_SYSTEM_PROMPTS["DocumentReader"]
            capabilities = AGENT_CAPABILITIES["DocumentReader"]
            
            self.file_reader = FileReader(model_client, system_message, capabilities)
            self.initialized = True
            print("✅ FileReaderWrapper 初始化完成")
    
    async def read_file(self, file_path: str, question: str = "请总结这个文件的主要内容") -> str:
        """读取文件并回答问题"""
        if not self.initialized:
            await self.init()
        
        query = f"读取文件 {file_path} 并回答：{question}"
        return await self.file_reader.handle_message(query)
    
    async def ask_default_doc(self, question: str) -> str:
        """询问默认文档"""
        if not self.initialized:
            await self.init()
        
        return await self.file_reader.handle_message(question)
    
    def configure(self, **kwargs):
        """配置智能体"""
        if self.file_reader:
            self.file_reader.configure_limits(**kwargs)
    
    def get_info(self):
        """获取智能体信息"""
        if self.file_reader:
            return {
                "limits": self.file_reader.get_current_limits(),
                "doc_info": self.file_reader.get_document_info()
            }
        return None

async def wrapper_usage():
    """包装类使用示例"""
    print("\n🎁 包装类使用示例")
    print("-" * 30)
    
    # 创建包装类实例
    reader = FileReaderWrapper()
    
    # 读取文件
    try:
        response1 = await reader.read_file("config/models_config.py", "这个配置文件的主要作用是什么？")
        print(f"📄 文件读取结果: {response1[:150]}...")
    except Exception as e:
        print(f"❌ 文件读取失败: {e}")
    
    # 询问默认文档
    try:
        response2 = await reader.ask_default_doc("MCPWM模块的功能")
        print(f"📋 默认文档查询: {response2[:150]}...")
    except Exception as e:
        print(f"❌ 默认文档查询失败: {e}")
    
    # 获取信息
    info = reader.get_info()
    if info:
        print(f"ℹ️  智能体信息: {info['limits']}")

async def main():
    """主函数 - 展示所有使用方式"""
    print("🚀 文件阅读智能体完整使用指南")
    print("=" * 50)
    
    try:
        # 简单使用
        await simple_usage()
        
        # 批量处理
        await batch_processing()
        
        # 包装类使用
        await wrapper_usage()
        
        print("\n✅ 所有示例执行完成！")
        
    except Exception as e:
        print(f"❌ 执行失败: {e}")

if __name__ == "__main__":
    asyncio.run(main())
