#!/usr/bin/env python3
"""
LKS32MC08x 智能文档理解系统主程序
"""
import asyncio
import sys
import os

# 添加父目录到路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from config.models_config import get_agent_model_client
from config.agents_config import AGENT_SYSTEM_PROMPTS, AGENT_CAPABILITIES
from agents import DocumentManager, TechnicalExpert, CodeEngineer, SystemArchitect, Coordinator, SimpleDocumentReader
from agents.file_reader import FileReader
from agents.base_agent import agent_registry

class DocumentIntelligenceSystem:
    """智能文档理解系统"""

    def __init__(self):
        self.agents = {}
        self.coordinator = None
        self.initialized = False

    async def initialize(self):
        """初始化系统"""
        try:
            print("🚀 正在初始化LKS32MC08x智能文档理解系统...")

            # 创建各个智能体
            await self._create_agents()

            # 注册智能体
            self._register_agents()

            # 复制文档到数据目录
            await self._setup_document()

            self.initialized = True
            print("✅ 系统初始化完成！")

        except Exception as e:
            print(f"❌ 系统初始化失败: {e}")
            raise

    async def _create_agents(self):
        """创建智能体"""
        print("📋 创建智能体...")

        # 文档管理员
        doc_client = get_agent_model_client("DocumentManager")
        self.agents["DocumentManager"] = DocumentManager(
            model_client=doc_client,
            system_message=AGENT_SYSTEM_PROMPTS["DocumentManager"],
            capabilities=AGENT_CAPABILITIES["DocumentManager"]
        )

        # 技术专家
        tech_client = get_agent_model_client("TechnicalExpert")
        self.agents["TechnicalExpert"] = TechnicalExpert(
            model_client=tech_client,
            system_message=AGENT_SYSTEM_PROMPTS["TechnicalExpert"],
            capabilities=AGENT_CAPABILITIES["TechnicalExpert"]
        )

        # 代码工程师
        code_client = get_agent_model_client("CodeEngineer")
        self.agents["CodeEngineer"] = CodeEngineer(
            model_client=code_client,
            system_message=AGENT_SYSTEM_PROMPTS["CodeEngineer"],
            capabilities=AGENT_CAPABILITIES["CodeEngineer"]
        )

        # 系统架构师
        arch_client = get_agent_model_client("SystemArchitect")
        self.agents["SystemArchitect"] = SystemArchitect(
            model_client=arch_client,
            system_message=AGENT_SYSTEM_PROMPTS["SystemArchitect"],
            capabilities=AGENT_CAPABILITIES["SystemArchitect"]
        )

        # 协调员
        coord_client = get_agent_model_client("Coordinator")
        self.coordinator = Coordinator(
            model_client=coord_client,
            system_message=AGENT_SYSTEM_PROMPTS["Coordinator"],
            capabilities=AGENT_CAPABILITIES["Coordinator"]
        )

        # 文件阅读智能体
        file_client = get_agent_model_client("DocumentReader")
        self.agents["FileReader"] = FileReader(
            model_client=file_client,
            system_message=AGENT_SYSTEM_PROMPTS["DocumentReader"],
            capabilities=AGENT_CAPABILITIES["DocumentReader"]
        )

        print(f"✅ 创建了 {len(self.agents) + 1} 个智能体")

    def _register_agents(self):
        """注册智能体到全局注册表"""
        for agent in self.agents.values():
            agent_registry.register(agent)
        agent_registry.register(self.coordinator)

    async def _setup_document(self):
        """设置文档"""
        print("📄 设置文档...")

        # 创建数据目录
        data_dir = os.path.join(os.path.dirname(__file__), "data")
        os.makedirs(data_dir, exist_ok=True)

        # 复制文档文件
        source_doc = os.path.join(os.path.dirname(os.path.dirname(__file__)), "full.md")
        target_doc = os.path.join(data_dir, "full.md")

        if os.path.exists(source_doc) and not os.path.exists(target_doc):
            import shutil
            shutil.copy2(source_doc, target_doc)
            print("✅ 文档复制完成")
        elif os.path.exists(target_doc):
            print("✅ 文档已存在")
        else:
            print("⚠️  源文档不存在，请确保full.md文件在正确位置")

    async def ask(self, question: str, agent_name: str = None) -> str:
        """询问问题"""
        if not self.initialized:
            await self.initialize()

        if agent_name and agent_name in self.agents:
            # 直接询问指定智能体
            agent = self.agents[agent_name]
            return await agent.handle_message(question)
        else:
            # 通过协调员处理
            return await self.coordinator.handle_message(question)

    async def generate_code(self, requirement: str) -> str:
        """生成代码"""
        if not self.initialized:
            await self.initialize()

        return await self.agents["CodeEngineer"].handle_message(requirement)

    async def design_system(self, requirement: str) -> str:
        """设计系统"""
        if not self.initialized:
            await self.initialize()

        return await self.agents["SystemArchitect"].handle_message(requirement)

    async def analyze_technical(self, problem: str) -> str:
        """技术分析"""
        if not self.initialized:
            await self.initialize()

        return await self.agents["TechnicalExpert"].handle_message(problem)

    async def search_document(self, query: str) -> str:
        """搜索文档"""
        if not self.initialized:
            await self.initialize()

        return await self.agents["DocumentManager"].handle_message(query)

    async def read_file(self, file_request: str) -> str:
        """读取文件"""
        if not self.initialized:
            await self.initialize()

        return await self.agents["FileReader"].handle_message(file_request)

    def get_system_status(self) -> dict:
        """获取系统状态"""
        if not self.initialized:
            return {"status": "not_initialized"}

        status = {
            "status": "active",
            "agents": {},
            "coordinator": self.coordinator.get_status() if self.coordinator else None
        }

        for name, agent in self.agents.items():
            status["agents"][name] = agent.get_status()

        return status

    def get_agent_capabilities(self) -> dict:
        """获取智能体能力"""
        capabilities = {}

        for name, agent in self.agents.items():
            capabilities[name] = agent.get_capabilities()

        if self.coordinator:
            capabilities["Coordinator"] = self.coordinator.get_capabilities()

        return capabilities

async def interactive_mode():
    """交互模式"""
    system = DocumentIntelligenceSystem()
    await system.initialize()

    print("\n🎯 LKS32MC08x智能文档理解系统")
    print("=" * 50)
    print("可用命令:")
    print("  ask <问题>          - 询问问题")
    print("  code <需求>         - 生成代码")
    print("  design <需求>       - 设计系统")
    print("  analyze <问题>      - 技术分析")
    print("  search <关键词>     - 搜索文档")
    print("  read <文件路径>     - 读取文件")
    print("  status             - 查看系统状态")
    print("  capabilities       - 查看智能体能力")
    print("  help               - 显示帮助")
    print("  quit               - 退出系统")
    print("=" * 50)

    while True:
        try:
            user_input = input("\n💬 请输入命令: ").strip()

            if not user_input:
                continue

            if user_input.lower() in ['quit', 'exit', 'q']:
                print("👋 再见！")
                break

            if user_input.lower() == 'help':
                print("📖 帮助信息已显示在上方")
                continue

            if user_input.lower() == 'status':
                status = system.get_system_status()
                print(f"📊 系统状态: {status}")
                continue

            if user_input.lower() == 'capabilities':
                caps = system.get_agent_capabilities()
                print("🔧 智能体能力:")
                for name, cap in caps.items():
                    print(f"  {name}: {cap['capabilities']['primary_skills']}")
                continue

            # 解析命令
            parts = user_input.split(' ', 1)
            command = parts[0].lower()
            content = parts[1] if len(parts) > 1 else ""

            if not content:
                print("❌ 请提供具体内容")
                continue

            print(f"🤔 处理中...")

            if command == 'ask':
                response = await system.ask(content)
            elif command == 'code':
                response = await system.generate_code(content)
            elif command == 'design':
                response = await system.design_system(content)
            elif command == 'analyze':
                response = await system.analyze_technical(content)
            elif command == 'search':
                response = await system.search_document(content)
            elif command == 'read':
                response = await system.read_file(content)
            else:
                response = await system.ask(user_input)

            print(f"\n💡 回答:\n{response}")

        except KeyboardInterrupt:
            print("\n👋 再见！")
            break
        except Exception as e:
            print(f"❌ 处理错误: {e}")

async def main():
    """主函数"""
    if len(sys.argv) > 1:
        # 命令行模式
        question = " ".join(sys.argv[1:])
        system = DocumentIntelligenceSystem()
        response = await system.ask(question)
        print(response)
    else:
        # 交互模式
        await interactive_mode()

if __name__ == "__main__":
    asyncio.run(main())
