#!/usr/bin/env python3
"""
测试文件阅读智能体
"""
import asyncio
import sys
import os

sys.path.append(os.path.dirname(os.path.abspath(__file__)))
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from config.models_config import get_agent_model_client
from config.agents_config import AGENT_SYSTEM_PROMPTS, AGENT_CAPABILITIES
from agents.file_reader import FileReader

async def test_file_reading():
    """测试文件阅读功能"""
    print("📖 文件阅读智能体测试")
    print("=" * 50)
    
    # 初始化智能体
    print("🔧 初始化文件阅读智能体...")
    try:
        model_client = get_agent_model_client("DocumentReader")
        system_message = AGENT_SYSTEM_PROMPTS["DocumentReader"]
        capabilities = AGENT_CAPABILITIES["DocumentReader"]
        
        file_reader = FileReader(model_client, system_message, capabilities)
        print("✅ 初始化完成")
        
        # 显示文档信息
        doc_info = file_reader.get_document_info()
        print(f"\n📄 文档信息:")
        for key, value in doc_info.items():
            print(f"  • {key}: {value}")
        
        if doc_info["status"] != "found":
            print("❌ 文档未找到，无法继续测试")
            return
        
    except Exception as e:
        print(f"❌ 初始化失败: {e}")
        return
    
    # 测试用例
    test_cases = [
        {
            "category": "具体查询",
            "question": "SYS_AFE_REG1寄存器的功能是什么？",
            "description": "测试模型是否能读取文档并找到具体寄存器信息"
        },
        {
            "category": "概念理解",
            "question": "ADC模块的主要功能有哪些？",
            "description": "测试模型是否能理解文档中的技术概念"
        },
        {
            "category": "配置指导",
            "question": "如何配置GPIO作为输出？",
            "description": "测试模型是否能从文档中提取配置步骤"
        }
    ]
    
    print(f"\n🧪 开始文件阅读测试 ({len(test_cases)} 个用例):")
    
    for i, test_case in enumerate(test_cases, 1):
        print(f"\n📝 测试 {i}: {test_case['category']}")
        print(f"❓ 问题: {test_case['question']}")
        print(f"📋 描述: {test_case['description']}")
        print("-" * 60)
        
        try:
            start_time = asyncio.get_event_loop().time()
            response = await file_reader.handle_message(test_case['question'])
            end_time = asyncio.get_event_loop().time()
            
            print(f"💡 模型回答:\n{response}")
            print(f"⏱️  总耗时: {end_time - start_time:.2f}秒")
            
        except Exception as e:
            print(f"❌ 测试失败: {e}")
        
        print("\n" + "=" * 60)
        
        # 添加延时
        await asyncio.sleep(1)

async def interactive_reading():
    """交互式文件阅读"""
    print("\n🎯 交互式文件阅读")
    print("输入 'info' 查看文档信息")
    print("输入 'quit' 退出")
    print("-" * 30)
    
    # 初始化
    try:
        model_client = get_agent_model_client("DocumentReader")
        system_message = AGENT_SYSTEM_PROMPTS["DocumentReader"]
        capabilities = AGENT_CAPABILITIES["DocumentReader"]
        file_reader = FileReader(model_client, system_message, capabilities)
    except Exception as e:
        print(f"❌ 初始化失败: {e}")
        return
    
    while True:
        try:
            question = input("\n📖 请输入问题（让模型阅读文档回答）: ").strip()
            
            if question.lower() in ['quit', 'exit', 'q']:
                break
            
            if question.lower() == 'info':
                doc_info = file_reader.get_document_info()
                print(f"📄 文档信息:")
                for key, value in doc_info.items():
                    print(f"  • {key}: {value}")
                continue
            
            if not question:
                continue
            
            print("📖 正在让模型阅读文档...")
            response = await file_reader.handle_message(question)
            print(f"\n🤖 模型阅读后的回答:\n{response}")
            
        except KeyboardInterrupt:
            break
        except Exception as e:
            print(f"❌ 错误: {e}")
    
    print("👋 文件阅读测试结束")

async def main():
    """主函数"""
    print("📖 文件阅读智能体测试工具")
    print("选择测试模式:")
    print("1. 预设测试用例")
    print("2. 交互式测试")
    
    try:
        choice = input("请选择 (1/2): ").strip()
        
        if choice == "1":
            await test_file_reading()
        elif choice == "2":
            await interactive_reading()
        else:
            print("无效选择，运行预设测试")
            await test_file_reading()
            
    except KeyboardInterrupt:
        print("\n👋 再见！")

if __name__ == "__main__":
    asyncio.run(main())
