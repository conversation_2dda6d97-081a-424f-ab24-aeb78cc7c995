#!/usr/bin/env python3
"""
测试智能工程师查询系统
"""
import asyncio
import sys
import os

sys.path.append(os.path.dirname(os.path.abspath(__file__)))
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from config.models_config import get_agent_model_client
from config.agents_config import AGENT_SYSTEM_PROMPTS, AGENT_CAPABILITIES
from agents.intelligent_kg_query import IntelligentKGQuery

async def test_intelligent_queries():
    """测试智能工程师查询"""
    print("🧠 智能工程师查询系统测试")
    print("=" * 50)
    
    # 初始化智能体
    print("🔧 初始化智能工程师查询系统...")
    try:
        model_client = get_agent_model_client("IntelligentKGQuery")
        system_message = AGENT_SYSTEM_PROMPTS["IntelligentKGQuery"]
        capabilities = AGENT_CAPABILITIES["IntelligentKGQuery"]
        
        intelligent_query = IntelligentKGQuery(model_client, system_message, capabilities)
        print("✅ 初始化完成")
        
    except Exception as e:
        print(f"❌ 初始化失败: {e}")
        return
    
    # 智能查询测试用例
    test_cases = [
        {
            "category": "ADC技术咨询",
            "request": "智能查询 如何配置ADC进行温度测量",
            "description": "测试ADC相关的工程师级别分析"
        },
        {
            "category": "GPIO应用场景",
            "request": "使用场景 GPIO",
            "description": "分析GPIO的典型应用场景"
        },
        {
            "category": "UART通信设计",
            "request": "设计建议 UART串口通信系统",
            "description": "提供UART设计的专业建议"
        },
        {
            "category": "寄存器关联分析",
            "request": "关联分析 SYS_AFE_REG1",
            "description": "分析寄存器的关联关系"
        },
        {
            "category": "系统集成咨询",
            "request": "智能查询 如何设计一个数据采集系统",
            "description": "测试系统级的工程咨询"
        }
    ]
    
    print(f"\n🧪 开始智能查询测试 ({len(test_cases)} 个用例):")
    
    for i, test_case in enumerate(test_cases, 1):
        print(f"\n📝 测试 {i}: {test_case['category']}")
        print(f"📋 请求: {test_case['request']}")
        print(f"📄 描述: {test_case['description']}")
        print("-" * 60)
        
        try:
            start_time = asyncio.get_event_loop().time()
            response = await intelligent_query.handle_message(test_case['request'])
            end_time = asyncio.get_event_loop().time()
            
            print(f"🧠 智能分析结果:")
            # 显示结果摘要
            lines = response.split('\n')
            summary_lines = []
            for line in lines:
                if any(keyword in line for keyword in ['##', '###', '- **', '✅', '🎯', '💡', '🔧']):
                    summary_lines.append(line)
            
            if summary_lines:
                for line in summary_lines[:12]:  # 显示前12行关键信息
                    print(f"  {line}")
                if len(summary_lines) > 12:
                    print(f"  ... (还有 {len(summary_lines) - 12} 行详细分析)")
            else:
                print(f"  {response[:400]}...")
            
            print(f"\n⏱️  分析时间: {end_time - start_time:.2f}秒")
            
        except Exception as e:
            print(f"❌ 测试失败: {e}")
        
        print("\n" + "=" * 60)
        
        # 添加延时
        await asyncio.sleep(1)

async def interactive_intelligent_query():
    """交互式智能查询"""
    print("\n🎯 交互式智能工程师咨询")
    print("支持的查询类型:")
    print("- 智能查询 <技术问题>")
    print("- 使用场景 <模块名>")
    print("- 设计建议 <设计需求>")
    print("- 关联分析 <实体名>")
    print("输入 'quit' 退出")
    print("-" * 50)
    
    # 初始化
    try:
        model_client = get_agent_model_client("IntelligentKGQuery")
        system_message = AGENT_SYSTEM_PROMPTS["IntelligentKGQuery"]
        capabilities = AGENT_CAPABILITIES["IntelligentKGQuery"]
        intelligent_query = IntelligentKGQuery(model_client, system_message, capabilities)
    except Exception as e:
        print(f"❌ 初始化失败: {e}")
        return
    
    # 显示使用示例
    print("\n💡 使用示例:")
    examples = [
        "智能查询 如何配置ADC采集电压",
        "使用场景 PWM",
        "设计建议 低功耗传感器系统",
        "关联分析 GPIO_CFG"
    ]
    for example in examples:
        print(f"  • {example}")
    
    while True:
        try:
            request = input("\n🧠 请输入工程师咨询: ").strip()
            
            if request.lower() in ['quit', 'exit', 'q']:
                break
            
            if not request:
                continue
            
            # 检查查询类型
            query_types = ["智能查询", "使用场景", "设计建议", "关联分析"]
            if not any(qt in request for qt in query_types):
                print("⚠️  请使用正确的查询格式，例如：")
                print("   智能查询 如何配置ADC")
                print("   使用场景 GPIO")
                continue
            
            print("🧠 工程师正在分析...")
            start_time = asyncio.get_event_loop().time()
            response = await intelligent_query.handle_message(request)
            end_time = asyncio.get_event_loop().time()
            
            print(f"\n📋 工程师分析结果:\n{response}")
            print(f"\n⏱️  分析时间: {end_time - start_time:.2f}秒")
            
        except KeyboardInterrupt:
            break
        except Exception as e:
            print(f"❌ 错误: {e}")
    
    print("👋 智能工程师咨询结束")

async def demo_engineering_scenarios():
    """演示工程场景"""
    print("\n🎬 工程场景演示")
    print("-" * 40)
    
    # 初始化
    model_client = get_agent_model_client("IntelligentKGQuery")
    system_message = AGENT_SYSTEM_PROMPTS["IntelligentKGQuery"]
    capabilities = AGENT_CAPABILITIES["IntelligentKGQuery"]
    intelligent_query = IntelligentKGQuery(model_client, system_message, capabilities)
    
    scenarios = [
        {
            "scenario": "🌡️ 温度监测系统设计",
            "queries": [
                "智能查询 温度传感器接口设计",
                "使用场景 ADC",
                "设计建议 温度监测系统"
            ]
        },
        {
            "scenario": "💡 LED控制系统",
            "queries": [
                "智能查询 PWM控制LED亮度",
                "使用场景 PWM",
                "关联分析 GPIO"
            ]
        },
        {
            "scenario": "📡 无线通信模块",
            "queries": [
                "智能查询 UART无线模块通信",
                "使用场景 UART",
                "设计建议 无线数据传输"
            ]
        }
    ]
    
    for i, scenario in enumerate(scenarios, 1):
        print(f"\n🎯 场景 {i}: {scenario['scenario']}")
        print("-" * 30)
        
        for j, query in enumerate(scenario['queries'], 1):
            print(f"\n  📝 步骤 {j}: {query}")
            try:
                response = await intelligent_query.handle_message(query)
                
                # 提取关键信息
                lines = response.split('\n')
                key_points = []
                for line in lines:
                    if any(marker in line for marker in ['##', '- **', '💡', '🔧', '⚡']):
                        key_points.append(line.strip())
                
                if key_points:
                    print(f"    🎯 关键点: {key_points[0]}")
                    if len(key_points) > 1:
                        print(f"    📋 详情: {key_points[1]}")
                else:
                    print(f"    📊 结果: {response[:100]}...")
                    
            except Exception as e:
                print(f"    ❌ 失败: {e}")
        
        print()

async def main():
    """主函数"""
    print("🧠 智能工程师查询系统测试工具")
    print("选择测试模式:")
    print("1. 智能查询测试")
    print("2. 交互式咨询")
    print("3. 工程场景演示")
    print("4. 全部测试")
    
    try:
        choice = input("请选择 (1/2/3/4): ").strip()
        
        if choice == "1":
            await test_intelligent_queries()
        elif choice == "2":
            await interactive_intelligent_query()
        elif choice == "3":
            await demo_engineering_scenarios()
        elif choice == "4":
            await test_intelligent_queries()
            await demo_engineering_scenarios()
        else:
            print("无效选择，运行智能查询测试")
            await test_intelligent_queries()
            
    except KeyboardInterrupt:
        print("\n👋 再见！")

if __name__ == "__main__":
    asyncio.run(main())
