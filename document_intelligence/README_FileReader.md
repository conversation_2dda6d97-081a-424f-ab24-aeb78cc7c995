# 📖 文件阅读智能体使用指南

## 🚀 快速开始

### 方式1: 命令行直接使用

```bash
# 进入项目目录
cd /Users/<USER>/Public/AutoGen/document_intelligence

# 激活环境
conda activate autogen

# 直接询问（使用默认文档full.md）
python main.py "SYS_AFE_REG1寄存器的功能是什么？"

# 读取指定文件
python main.py "读取文件 config/agents_config.py 并总结配置内容"
```

### 方式2: 交互模式

```bash
# 启动交互模式
python main.py

# 在交互界面中使用
💬 请输入命令: read config/models_config.py
💬 请输入命令: ask GPIO模块的功能
💬 请输入命令: search ADC配置
```

### 方式3: 专用测试脚本

```bash
# 文件阅读专用测试
python test_file_reader.py

# 选择模式：
# 1. 预设测试用例
# 2. 交互式测试
```

## 💻 代码中调用

### 简单调用

```python
import asyncio
from main import DocumentIntelligenceSystem

async def main():
    system = DocumentIntelligenceSystem()
    
    # 读取文件
    response = await system.read_file("读取文件 example.py 并解释代码功能")
    print(response)
    
    # 询问默认文档
    response = await system.ask("GPIO模块的功能")
    print(response)

asyncio.run(main())
```

### 直接使用FileReader

```python
import asyncio
from config.models_config import get_agent_model_client
from config.agents_config import AGENT_SYSTEM_PROMPTS, AGENT_CAPABILITIES
from agents.file_reader import FileReader

async def main():
    # 初始化
    model_client = get_agent_model_client("DocumentReader")
    system_message = AGENT_SYSTEM_PROMPTS["DocumentReader"]
    capabilities = AGENT_CAPABILITIES["DocumentReader"]
    
    file_reader = FileReader(model_client, system_message, capabilities)
    
    # 使用
    response = await file_reader.handle_message("读取文件 test.txt 并总结内容")
    print(response)

asyncio.run(main())
```

### 使用包装类（推荐）

```python
import asyncio
from use_file_reader import FileReaderWrapper

async def main():
    reader = FileReaderWrapper()
    
    # 读取文件
    response1 = await reader.read_file("config.json", "这个配置文件的作用是什么？")
    
    # 询问默认文档
    response2 = await reader.ask_default_doc("MCPWM模块的功能")
    
    # 配置调整
    reader.configure(max_file_size=50*1024*1024)  # 50MB
    
    print(response1)
    print(response2)

asyncio.run(main())
```

## 📝 支持的文件格式

```
文档类型: .txt, .md, .readme
代码文件: .py, .js, .html, .css
配置文件: .json, .yaml, .xml, .ini, .cfg
数据文件: .csv, .log, .sql
脚本文件: .sh, .bat, .dockerfile
```

## 🎯 使用语法

### 文件路径指定方式

```bash
# 方式1: 明确指令
"读取文件 /path/to/file.txt 并回答问题"
"分析文件 config.json 的配置项"
"查看文件 readme.md 并总结内容"

# 方式2: 简化语法
"打开文件 example.py"
"文件: data.csv"
"文件路径: /home/<USER>/doc.txt"

# 方式3: 直接文件名（如果有扩展名）
"example.py 的功能是什么？"
"config.json 包含哪些配置？"
```

### 问题类型

```bash
# 内容总结
"读取文件 doc.txt 并总结主要内容"

# 功能解释
"读取文件 script.py 并解释代码功能"

# 配置分析
"分析文件 config.json 的配置项"

# 特定查询
"读取文件 manual.md 并回答：如何配置GPIO？"

# 对比分析
"读取文件 old.py 和 new.py 并比较差异"
```

## ⚙️ 配置选项

### 文件大小限制

```python
file_reader.configure_limits(
    max_file_size=100 * 1024 * 1024,      # 最大文件100MB
    large_file_threshold=50 * 1024 * 1024, # 50MB启用流式读取
    smart_sample_threshold=100000,         # 10万字符启用智能采样
    memory_limit=200 * 1024 * 1024        # 200MB内存限制
)
```

### 查看当前配置

```python
limits = file_reader.get_current_limits()
print(limits)
# 输出: {'max_file_size': '100MB', 'smart_sample_threshold': '100,000 字符', ...}
```

## 🔧 高级功能

### 1. 智能采样（大文件处理）

```python
# 超过10万字符的文件会自动采样
# 策略：开头 + 中间采样 + 结尾
response = await file_reader.handle_message("读取文件 large_doc.txt")
```

### 2. 流式读取（超大文件）

```python
# 超过50MB的文件使用流式读取
# 避免内存溢出
response = await file_reader.handle_message("读取文件 huge_file.log")
```

### 3. 自动编码检测

```python
# 自动尝试多种编码：utf-8, gbk, gb2312, latin1
# 无需手动指定编码
response = await file_reader.handle_message("读取文件 chinese.txt")
```

### 4. 批量处理

```python
queries = [
    "读取文件 file1.txt",
    "读取文件 file2.py", 
    "读取文件 file3.json"
]

for query in queries:
    response = await file_reader.handle_message(query)
    print(f"处理结果: {response[:100]}...")
```

## ❗ 注意事项

### 1. 文件路径
- 支持相对路径和绝对路径
- 相对路径基于当前工作目录
- 建议使用绝对路径避免混淆

### 2. 文件大小
- 默认最大支持100MB文件
- 超大文件会自动采样处理
- 可通过配置调整限制

### 3. 模型上下文
- 受LM Studio上下文长度限制
- 长文件会自动分块或采样
- 建议针对具体问题查询

### 4. 性能考虑
- 大文件处理需要更多时间
- 模型推理时间取决于内容长度
- 建议对大文件使用具体查询

## 🐛 故障排除

### 常见错误

```bash
# 文件不存在
❌ 文件不存在: /path/to/file.txt
解决: 检查文件路径是否正确

# 文件过大
❌ 文件过大: 150.0MB (最大支持100MB)
解决: 调整配置或使用文件片段

# 编码错误
❌ 无法识别文件编码
解决: 转换文件编码为UTF-8

# 上下文过长
❌ The number of tokens to keep from the initial prompt is greater than the context length
解决: 使用更具体的查询或调整max_context_chars
```

### 性能优化

```python
# 1. 减少上下文长度
file_reader.max_context_chars = 8000

# 2. 使用具体查询而非通用总结
"读取文件 config.py 并找到数据库配置"  # ✅ 具体
"读取文件 config.py 并总结所有内容"   # ❌ 通用

# 3. 对大文件使用采样
file_reader.configure_limits(smart_sample_threshold=50000)
```

## 📞 获取帮助

```bash
# 查看智能体信息
python -c "
from use_file_reader import FileReaderWrapper
import asyncio

async def info():
    reader = FileReaderWrapper()
    await reader.init()
    print(reader.get_info())

asyncio.run(info())
"
```
