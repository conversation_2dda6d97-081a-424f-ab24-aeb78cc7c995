#!/usr/bin/env python3
"""
测试知识图谱构建智能体
"""
import asyncio
import sys
import os

sys.path.append(os.path.dirname(os.path.abspath(__file__)))
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from config.models_config import get_agent_model_client
from config.agents_config import AGENT_SYSTEM_PROMPTS, AGENT_CAPABILITIES
from agents.knowledge_graph_builder import KnowledgeGraphBuilder

async def test_knowledge_graph_building():
    """测试知识图谱构建"""
    print("🧠 知识图谱构建智能体测试")
    print("=" * 50)
    
    # 初始化智能体
    print("🔧 初始化知识图谱构建智能体...")
    try:
        model_client = get_agent_model_client("KnowledgeGraphBuilder")
        system_message = AGENT_SYSTEM_PROMPTS["KnowledgeGraphBuilder"]
        capabilities = AGENT_CAPABILITIES["KnowledgeGraphBuilder"]
        
        kg_builder = KnowledgeGraphBuilder(model_client, system_message, capabilities)
        print("✅ 初始化完成")
        
    except Exception as e:
        print(f"❌ 初始化失败: {e}")
        return
    
    # 测试用例
    test_cases = [
        {
            "category": "构建知识图谱",
            "request": "构建知识图谱 路径: /Users/<USER>/Public/AutoGen/monkey_ocr_project/mineru_output",
            "description": "从Mineru输出构建完整知识图谱"
        },
        {
            "category": "分析知识图谱",
            "request": "分析知识图谱",
            "description": "分析已构建的知识图谱结构"
        },
        {
            "category": "导出HTML",
            "request": "导出知识图谱 html",
            "description": "导出交互式HTML可视化"
        },
        {
            "category": "导出JSON",
            "request": "导出知识图谱 json",
            "description": "导出JSON格式数据"
        }
    ]
    
    print(f"\n🧪 开始知识图谱测试 ({len(test_cases)} 个用例):")
    
    for i, test_case in enumerate(test_cases, 1):
        print(f"\n📝 测试 {i}: {test_case['category']}")
        print(f"📋 请求: {test_case['request']}")
        print(f"📄 描述: {test_case['description']}")
        print("-" * 60)
        
        try:
            start_time = asyncio.get_event_loop().time()
            response = await kg_builder.handle_message(test_case['request'])
            end_time = asyncio.get_event_loop().time()
            
            print(f"🧠 处理结果:")
            print(response)
            print(f"\n⏱️  总耗时: {end_time - start_time:.2f}秒")
            
            # 分析结果质量
            print(f"\n📊 结果分析:")
            print(f"  • 回答长度: {len(response)} 字符")
            
            # 检查特定指标
            if "实体总数" in response:
                print("  • ✅ 包含实体统计")
            if "关系总数" in response:
                print("  • ✅ 包含关系统计")
            if "图谱密度" in response:
                print("  • ✅ 包含密度分析")
            if "导出" in response:
                print("  • ✅ 包含导出信息")
            
        except Exception as e:
            print(f"❌ 测试失败: {e}")
        
        print("\n" + "=" * 60)
        
        # 添加延时
        await asyncio.sleep(2)

async def interactive_knowledge_graph():
    """交互式知识图谱操作"""
    print("\n🎯 交互式知识图谱操作")
    print("支持的操作:")
    print("- 构建知识图谱 [路径]")
    print("- 分析知识图谱")
    print("- 导出知识图谱 [格式]")
    print("输入 'quit' 退出")
    print("-" * 50)
    
    # 初始化
    try:
        model_client = get_agent_model_client("KnowledgeGraphBuilder")
        system_message = AGENT_SYSTEM_PROMPTS["KnowledgeGraphBuilder"]
        capabilities = AGENT_CAPABILITIES["KnowledgeGraphBuilder"]
        kg_builder = KnowledgeGraphBuilder(model_client, system_message, capabilities)
    except Exception as e:
        print(f"❌ 初始化失败: {e}")
        return
    
    while True:
        try:
            request = input("\n🧠 请输入知识图谱操作: ").strip()
            
            if request.lower() in ['quit', 'exit', 'q']:
                break
            
            if not request:
                continue
            
            print("🔨 正在处理知识图谱操作...")
            response = await kg_builder.handle_message(request)
            print(f"\n📋 处理结果:\n{response}")
            
        except KeyboardInterrupt:
            break
        except Exception as e:
            print(f"❌ 错误: {e}")
    
    print("👋 知识图谱测试结束")

async def demo_knowledge_graph_workflow():
    """演示完整的知识图谱工作流程"""
    print("\n🎬 知识图谱完整工作流程演示")
    print("-" * 50)
    
    # 初始化
    model_client = get_agent_model_client("KnowledgeGraphBuilder")
    system_message = AGENT_SYSTEM_PROMPTS["KnowledgeGraphBuilder"]
    capabilities = AGENT_CAPABILITIES["KnowledgeGraphBuilder"]
    kg_builder = KnowledgeGraphBuilder(model_client, system_message, capabilities)
    
    workflow_steps = [
        {
            "step": "1. 构建知识图谱",
            "request": "构建知识图谱 路径: /Users/<USER>/Public/AutoGen/monkey_ocr_project/mineru_output",
            "description": "从Mineru输出文件构建知识图谱"
        },
        {
            "step": "2. 分析图谱结构",
            "request": "分析知识图谱",
            "description": "分析图谱的统计信息和结构特征"
        },
        {
            "step": "3. 导出可视化",
            "request": "导出知识图谱 html",
            "description": "生成交互式HTML可视化"
        },
        {
            "step": "4. 导出数据",
            "request": "导出知识图谱 json",
            "description": "导出结构化数据格式"
        }
    ]
    
    print(f"📋 工作流程包含 {len(workflow_steps)} 个步骤:")
    
    for step_info in workflow_steps:
        print(f"\n🔄 {step_info['step']}")
        print(f"📝 描述: {step_info['description']}")
        print(f"🎯 执行: {step_info['request']}")
        print("-" * 40)
        
        try:
            start_time = asyncio.get_event_loop().time()
            response = await kg_builder.handle_message(step_info['request'])
            end_time = asyncio.get_event_loop().time()
            
            # 显示简化结果
            lines = response.split('\n')
            summary_lines = []
            for line in lines:
                if any(keyword in line for keyword in ['✅', '📊', '实体总数', '关系总数', '导出']):
                    summary_lines.append(line)
            
            if summary_lines:
                print("📊 关键结果:")
                for line in summary_lines[:5]:  # 只显示前5行关键信息
                    print(f"  {line}")
            else:
                print(f"📊 结果: {response[:200]}...")
            
            print(f"⏱️  耗时: {end_time - start_time:.2f}秒")
            
        except Exception as e:
            print(f"❌ 步骤失败: {e}")
        
        print()
    
    print("🎉 知识图谱工作流程演示完成！")
    print("\n📁 输出文件位置:")
    print("  • knowledge_graphs/knowledge_graph.html - 交互式可视化")
    print("  • knowledge_graphs/knowledge_graph.json - 结构化数据")
    print("  • knowledge_graphs/knowledge_graph.png - 静态图片")

async def check_mineru_output():
    """检查Mineru输出文件"""
    print("\n🔍 检查Mineru输出文件")
    print("-" * 30)
    
    mineru_path = "/Users/<USER>/Public/AutoGen/monkey_ocr_project/mineru_output"
    
    if not os.path.exists(mineru_path):
        print(f"❌ Mineru输出路径不存在: {mineru_path}")
        return False
    
    print(f"✅ Mineru输出路径存在: {mineru_path}")
    
    # 扫描文件
    content_files = []
    for root, dirs, files in os.walk(mineru_path):
        for file in files:
            if file.endswith('_content_list.json'):
                file_path = os.path.join(root, file)
                file_size = os.path.getsize(file_path)
                content_files.append((file, file_size))
    
    if content_files:
        print(f"📄 找到 {len(content_files)} 个content_list.json文件:")
        for file, size in content_files:
            print(f"  • {file}: {size:,} 字节")
        return True
    else:
        print("❌ 未找到content_list.json文件")
        return False

async def main():
    """主函数"""
    print("🧠 知识图谱构建智能体测试工具")
    
    # 首先检查Mineru输出
    has_mineru_output = await check_mineru_output()
    
    if not has_mineru_output:
        print("\n⚠️  警告: 未找到Mineru输出文件，某些测试可能失败")
    
    print("\n选择测试模式:")
    print("1. 预设测试用例")
    print("2. 交互式测试")
    print("3. 完整工作流程演示")
    
    try:
        choice = input("请选择 (1/2/3): ").strip()
        
        if choice == "1":
            await test_knowledge_graph_building()
        elif choice == "2":
            await interactive_knowledge_graph()
        elif choice == "3":
            await demo_knowledge_graph_workflow()
        else:
            print("无效选择，运行预设测试")
            await test_knowledge_graph_building()
            
    except KeyboardInterrupt:
        print("\n👋 再见！")

if __name__ == "__main__":
    asyncio.run(main())
