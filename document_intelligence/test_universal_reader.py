#!/usr/bin/env python3
"""
测试通用文件阅读智能体
"""
import asyncio
import sys
import os

sys.path.append(os.path.dirname(os.path.abspath(__file__)))
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from config.models_config import get_agent_model_client
from config.agents_config import AGENT_SYSTEM_PROMPTS, AGENT_CAPABILITIES
from agents.file_reader import FileReader

async def test_universal_file_reading():
    """测试通用文件阅读功能"""
    print("🌍 通用文件阅读智能体测试")
    print("=" * 50)
    
    # 初始化智能体
    print("🔧 初始化通用文件阅读智能体...")
    try:
        model_client = get_agent_model_client("DocumentReader")
        system_message = AGENT_SYSTEM_PROMPTS["DocumentReader"]
        capabilities = AGENT_CAPABILITIES["DocumentReader"]
        
        file_reader = FileReader(model_client, system_message, capabilities)
        print("✅ 初始化完成")
        
    except Exception as e:
        print(f"❌ 初始化失败: {e}")
        return
    
    # 创建测试文件
    test_files = await create_test_files()
    
    # 测试用例
    test_cases = [
        {
            "category": "默认文档",
            "query": "SYS_AFE_REG1寄存器的功能是什么？",
            "description": "测试默认full.md文档读取"
        },
        {
            "category": "Python代码",
            "query": f"读取文件 {test_files['python']} 并解释这个函数的功能",
            "description": "测试Python代码文件读取"
        },
        {
            "category": "JSON配置",
            "query": f"分析文件 {test_files['json']} 的结构和内容",
            "description": "测试JSON文件读取"
        },
        {
            "category": "文本文件",
            "query": f"读取文件 {test_files['txt']} 并总结主要内容",
            "description": "测试普通文本文件读取"
        },
        {
            "category": "Markdown文档",
            "query": f"查看文件 {test_files['md']} 并回答：项目的主要功能是什么？",
            "description": "测试Markdown文档读取"
        }
    ]
    
    print(f"\n🧪 开始通用文件阅读测试 ({len(test_cases)} 个用例):")
    
    for i, test_case in enumerate(test_cases, 1):
        print(f"\n📝 测试 {i}: {test_case['category']}")
        print(f"❓ 查询: {test_case['query']}")
        print(f"📋 描述: {test_case['description']}")
        print("-" * 60)
        
        try:
            start_time = asyncio.get_event_loop().time()
            response = await file_reader.handle_message(test_case['query'])
            end_time = asyncio.get_event_loop().time()
            
            print(f"💡 模型回答:\n{response}")
            print(f"⏱️  总耗时: {end_time - start_time:.2f}秒")
            
        except Exception as e:
            print(f"❌ 测试失败: {e}")
        
        print("\n" + "=" * 60)
        
        # 添加延时
        await asyncio.sleep(1)
    
    # 清理测试文件
    cleanup_test_files(test_files)

async def create_test_files():
    """创建测试文件"""
    print("📁 创建测试文件...")
    
    test_dir = "test_files"
    os.makedirs(test_dir, exist_ok=True)
    
    test_files = {}
    
    # Python文件
    python_content = '''def fibonacci(n):
    """
    计算斐波那契数列的第n项
    使用递归方法实现
    """
    if n <= 1:
        return n
    else:
        return fibonacci(n-1) + fibonacci(n-2)

def main():
    # 计算前10项斐波那契数列
    for i in range(10):
        print(f"fibonacci({i}) = {fibonacci(i)}")

if __name__ == "__main__":
    main()
'''
    
    python_file = os.path.join(test_dir, "fibonacci.py")
    with open(python_file, 'w', encoding='utf-8') as f:
        f.write(python_content)
    test_files['python'] = python_file
    
    # JSON文件
    json_content = '''{
    "project": {
        "name": "智能文档阅读系统",
        "version": "1.0.0",
        "description": "基于AutoGen的智能文档理解系统",
        "features": [
            "多格式文件读取",
            "智能内容理解",
            "自然语言问答",
            "上下文感知"
        ],
        "supported_formats": [
            ".txt", ".md", ".py", ".js", ".json", ".xml", ".yaml"
        ],
        "configuration": {
            "max_file_size": "10MB",
            "max_context_chars": 15000,
            "supported_encodings": ["utf-8", "gbk", "gb2312"]
        }
    }
}'''
    
    json_file = os.path.join(test_dir, "config.json")
    with open(json_file, 'w', encoding='utf-8') as f:
        f.write(json_content)
    test_files['json'] = json_file
    
    # 文本文件
    txt_content = '''智能文档阅读系统使用说明

本系统是基于AutoGen框架开发的智能文档理解工具，具有以下特点：

1. 多格式支持
   - 支持文本文件(.txt)
   - 支持Markdown文档(.md)
   - 支持代码文件(.py, .js等)
   - 支持配置文件(.json, .yaml等)

2. 智能理解
   - 模型真正读取文件内容
   - 基于内容回答问题
   - 支持上下文感知

3. 使用方法
   - 指定文件路径：读取文件 /path/to/file.txt 并回答问题
   - 默认文档：直接提问，使用默认文档
   - 交互模式：支持多轮对话

注意事项：
- 文件大小限制：最大10MB
- 支持自动编码检测
- 长文件会自动分块处理
'''
    
    txt_file = os.path.join(test_dir, "readme.txt")
    with open(txt_file, 'w', encoding='utf-8') as f:
        f.write(txt_content)
    test_files['txt'] = txt_file
    
    # Markdown文件
    md_content = '''# 智能文档阅读项目

## 项目概述

这是一个基于AutoGen框架的智能文档阅读理解系统，能够让AI模型真正读取和理解各种格式的文件。

## 主要功能

### 🔍 文件读取
- 支持多种文件格式
- 自动编码检测
- 文件大小验证

### 🧠 智能理解
- 模型真正阅读文件内容
- 基于内容生成回答
- 支持复杂推理

### 💬 自然交互
- 自然语言查询
- 多轮对话支持
- 上下文感知

## 技术特点

1. **简单直接**：不预处理，让模型直接读取
2. **通用性强**：支持任意文本格式文件
3. **智能分块**：自动处理长文件
4. **容错设计**：优雅处理各种异常

## 使用示例

```bash
# 读取Python文件
读取文件 example.py 并解释代码功能

# 分析配置文件
分析文件 config.json 的配置项

# 总结文档内容
查看文件 readme.md 并总结主要内容
```

## 支持格式

- 文档：.txt, .md
- 代码：.py, .js, .html, .css
- 配置：.json, .yaml, .xml, .ini
- 其他：.log, .csv, .sql
'''
    
    md_file = os.path.join(test_dir, "project.md")
    with open(md_file, 'w', encoding='utf-8') as f:
        f.write(md_content)
    test_files['md'] = md_file
    
    print(f"✅ 创建了 {len(test_files)} 个测试文件")
    return test_files

def cleanup_test_files(test_files):
    """清理测试文件"""
    print("\n🧹 清理测试文件...")
    
    for file_path in test_files.values():
        try:
            if os.path.exists(file_path):
                os.remove(file_path)
        except Exception as e:
            print(f"⚠️  删除文件失败 {file_path}: {e}")
    
    # 删除测试目录
    try:
        test_dir = "test_files"
        if os.path.exists(test_dir):
            os.rmdir(test_dir)
        print("✅ 测试文件清理完成")
    except Exception as e:
        print(f"⚠️  删除目录失败: {e}")

async def interactive_universal_reading():
    """交互式通用文件阅读"""
    print("\n🎯 交互式通用文件阅读")
    print("支持的格式: .txt, .md, .py, .js, .json, .xml, .yaml 等")
    print("使用方法: '读取文件 /path/to/file.txt 并回答问题'")
    print("输入 'quit' 退出")
    print("-" * 50)
    
    # 初始化
    try:
        model_client = get_agent_model_client("DocumentReader")
        system_message = AGENT_SYSTEM_PROMPTS["DocumentReader"]
        capabilities = AGENT_CAPABILITIES["DocumentReader"]
        file_reader = FileReader(model_client, system_message, capabilities)
    except Exception as e:
        print(f"❌ 初始化失败: {e}")
        return
    
    while True:
        try:
            query = input("\n📖 请输入文件读取请求: ").strip()
            
            if query.lower() in ['quit', 'exit', 'q']:
                break
            
            if not query:
                continue
            
            print("📖 正在处理文件读取请求...")
            response = await file_reader.handle_message(query)
            print(f"\n🤖 智能体回答:\n{response}")
            
        except KeyboardInterrupt:
            break
        except Exception as e:
            print(f"❌ 错误: {e}")
    
    print("👋 通用文件阅读测试结束")

async def main():
    """主函数"""
    print("🌍 通用文件阅读智能体测试工具")
    print("选择测试模式:")
    print("1. 预设测试用例")
    print("2. 交互式测试")
    
    try:
        choice = input("请选择 (1/2): ").strip()
        
        if choice == "1":
            await test_universal_file_reading()
        elif choice == "2":
            await interactive_universal_reading()
        else:
            print("无效选择，运行预设测试")
            await test_universal_file_reading()
            
    except KeyboardInterrupt:
        print("\n👋 再见！")

if __name__ == "__main__":
    asyncio.run(main())
