<html>
    <head>
        <meta charset="utf-8">
        
            <script src="lib/bindings/utils.js"></script>
            <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/vis-network/9.1.2/dist/dist/vis-network.min.css" integrity="sha512-WgxfT5LWjfszlPHXRmBWHkV2eceiWTOBvrKCNbdgDYTHrT2AeLCGbF4sZlZw3UMN3WtL0tGUoIAKsu8mllg/XA==" crossorigin="anonymous" referrerpolicy="no-referrer" />
            <script src="https://cdnjs.cloudflare.com/ajax/libs/vis-network/9.1.2/dist/vis-network.min.js" integrity="sha512-LnvoEWDFrqGHlHmDD2101OrLcbsfkrzoSpvtSQtxK3RMnRV0eOkhhBN2dXHKRrUU8p2DGRTk35n4O8nWSVe1mQ==" crossorigin="anonymous" referrerpolicy="no-referrer"></script>
            
        
<center>
<h1></h1>
</center>

<!-- <link rel="stylesheet" href="../node_modules/vis/dist/vis.min.css" type="text/css" />
<script type="text/javascript" src="../node_modules/vis/dist/vis.js"> </script>-->
        <link
          href="https://cdn.jsdelivr.net/npm/bootstrap@5.0.0-beta3/dist/css/bootstrap.min.css"
          rel="stylesheet"
          integrity="sha384-eOJMYsd53ii+scO/bJGFsiCZc+5NDVN2yr8+0RDqr0Ql0h+rP48ckxlpbzKgwra6"
          crossorigin="anonymous"
        />
        <script
          src="https://cdn.jsdelivr.net/npm/bootstrap@5.0.0-beta3/dist/js/bootstrap.bundle.min.js"
          integrity="sha384-JEW9xMcG8R+pH31jmWH6WWP0WintQrMb4s7ZOdauHnUtxwoG2vI5DkLtS3qm9Ekf"
          crossorigin="anonymous"
        ></script>


        <center>
          <h1></h1>
        </center>
        <style type="text/css">

             #mynetwork {
                 width: 100%;
                 height: 800px;
                 background-color: #222222;
                 border: 1px solid lightgray;
                 position: relative;
                 float: left;
             }

             

             

             
        </style>
    </head>


    <body>
        <div class="card" style="width: 100%">
            
            
            <div id="mynetwork" class="card-body"></div>
        </div>

        
        

        <script type="text/javascript">

              // initialize global variables.
              var edges;
              var nodes;
              var allNodes;
              var allEdges;
              var nodeColors;
              var originalNodes;
              var network;
              var container;
              var options, data;
              var filter = {
                  item : '',
                  property : '',
                  value : []
              };

              

              

              // This method is responsible for drawing the graph, returns the drawn network
              function drawGraph() {
                  var container = document.getElementById('mynetwork');

                  

                  // parsing and collecting nodes and edges from the python
                  nodes = new vis.DataSet([{"color": "#ff6b6b", "font": {"color": "white"}, "id": "register_sys_afe_reg1", "label": "SYS_AFE_REG1", "shape": "dot", "size": 20, "title": "\u7c7b\u578b: register\n\u63cf\u8ff0: \u6a21\u62df\u914d\u7f6e\u5bc4\u5b58\u56681"}, {"color": "#4ecdc4", "font": {"color": "white"}, "id": "module_adc", "label": "ADC", "shape": "dot", "size": 20, "title": "\u7c7b\u578b: module\n\u63cf\u8ff0: \u6a21\u6570\u8f6c\u6362\u5668\u6a21\u5757"}, {"color": "#45b7d1", "font": {"color": "white"}, "id": "function_conversion", "label": "\u6a21\u6570\u8f6c\u6362", "shape": "dot", "size": 20, "title": "\u7c7b\u578b: function\n\u63cf\u8ff0: \u5c06\u6a21\u62df\u4fe1\u53f7\u8f6c\u6362\u4e3a\u6570\u5b57\u4fe1\u53f7"}, {"color": "#96ceb4", "font": {"color": "white"}, "id": "parameter_resolution", "label": "\u5206\u8fa8\u7387", "shape": "dot", "size": 20, "title": "\u7c7b\u578b: parameter\n\u63cf\u8ff0: ADC\u8f6c\u6362\u7cbe\u5ea6"}, {"color": "#feca57", "font": {"color": "white"}, "id": "address_0x40010000", "label": "0x40010000", "shape": "dot", "size": 20, "title": "\u7c7b\u578b: address\n\u63cf\u8ff0: ADC\u57fa\u5730\u5740"}]);
                  edges = new vis.DataSet([{"from": "register_sys_afe_reg1", "label": "configures", "title": "\u5bc4\u5b58\u5668\u914d\u7f6eADC\u6a21\u5757", "to": "module_adc"}, {"from": "module_adc", "label": "implements", "title": "ADC\u6a21\u5757\u5b9e\u73b0\u6a21\u6570\u8f6c\u6362\u529f\u80fd", "to": "function_conversion"}, {"from": "module_adc", "label": "located_at", "title": "ADC\u6a21\u5757\u4f4d\u4e8e\u6307\u5b9a\u5730\u5740", "to": "address_0x40010000"}, {"from": "function_conversion", "label": "has_parameter", "title": "\u8f6c\u6362\u529f\u80fd\u5177\u6709\u5206\u8fa8\u7387\u53c2\u6570", "to": "parameter_resolution"}]);

                  nodeColors = {};
                  allNodes = nodes.get({ returnType: "Object" });
                  for (nodeId in allNodes) {
                    nodeColors[nodeId] = allNodes[nodeId].color;
                  }
                  allEdges = edges.get({ returnType: "Object" });
                  // adding nodes and edges to the graph
                  data = {nodes: nodes, edges: edges};

                  var options = {"physics": {"enabled": true, "stabilization": {"iterations": 100}}};

                  


                  

                  network = new vis.Network(container, data, options);

                  

                  

                  


                  

                  return network;

              }
              drawGraph();
        </script>
    </body>
</html>