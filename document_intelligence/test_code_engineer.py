#!/usr/bin/env python3
"""
代码工程师智能体专项测试
"""
import asyncio
import sys
import os

sys.path.append(os.path.dirname(os.path.abspath(__file__)))
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from config.models_config import get_agent_model_client
from config.agents_config import AGENT_SYSTEM_PROMPTS, AGENT_CAPABILITIES
from agents.code_engineer import CodeEngineer

async def test_code_engineer():
    """测试代码工程师"""
    print("💻 代码工程师智能体专项测试")
    print("=" * 50)
    
    # 初始化智能体
    print("🔧 初始化代码工程师...")
    model_client = get_agent_model_client("CodeEngineer")
    system_message = AGENT_SYSTEM_PROMPTS["CodeEngineer"]
    capabilities = AGENT_CAPABILITIES["CodeEngineer"]
    
    code_engineer = CodeEngineer(model_client, system_message, capabilities)
    print("✅ 初始化完成")
    
    # 显示代码模板信息
    print(f"\n📚 可用代码模板:")
    for template_name in code_engineer.code_templates.keys():
        print(f"  • {template_name}")
    
    # 测试用例
    test_cases = [
        {
            "category": "GPIO初始化",
            "requirement": "初始化GPIO，配置PA0为输出，PA1为输入并使能上拉",
            "type": "initialization",
            "expected": "应该生成GPIO初始化代码"
        },
        {
            "category": "ADC配置",
            "requirement": "配置ADC进行单通道采样，使用软件触发",
            "type": "initialization", 
            "expected": "应该生成ADC配置代码"
        },
        {
            "category": "UART通信",
            "requirement": "初始化UART0，波特率115200，生成发送接收函数",
            "type": "driver",
            "expected": "应该生成UART驱动代码"
        },
        {
            "category": "PWM控制",
            "requirement": "配置MCPWM生成20kHz PWM信号，占空比50%",
            "type": "initialization",
            "expected": "应该生成MCPWM配置代码"
        },
        {
            "category": "中断处理",
            "requirement": "生成ADC转换完成中断处理函数",
            "type": "interrupt",
            "expected": "应该生成中断处理代码"
        },
        {
            "category": "DMA传输",
            "requirement": "配置DMA将ADC数据传输到内存缓冲区",
            "type": "initialization",
            "expected": "应该生成DMA配置代码"
        }
    ]
    
    print(f"\n🧪 开始测试 ({len(test_cases)} 个用例):")
    
    for i, test_case in enumerate(test_cases, 1):
        print(f"\n📝 测试 {i}: {test_case['category']}")
        print(f"📋 需求: {test_case['requirement']}")
        print(f"🏷️  类型: {test_case['type']}")
        print(f"🎯 期望: {test_case['expected']}")
        print("-" * 40)
        
        try:
            start_time = asyncio.get_event_loop().time()
            response = await code_engineer.handle_message(test_case['requirement'])
            end_time = asyncio.get_event_loop().time()
            
            print(f"💻 生成的代码:\n{response}")
            print(f"⏱️  响应时间: {end_time - start_time:.2f}秒")
            
            # 简单的代码质量检查
            if "```c" in response and "```" in response:
                print("✅ 代码格式正确")
            else:
                print("⚠️  代码格式可能有问题")
            
        except Exception as e:
            print(f"❌ 测试失败: {e}")
        
        print()
    
    # 显示最终统计
    stats = code_engineer.get_status()["stats"]
    print(f"📊 最终统计:")
    print(f"  • 处理请求: {stats['requests_handled']}")
    print(f"  • 成功率: {stats['success_rate']:.1%}")
    print(f"  • 平均响应时间: {stats['average_response_time']:.2f}秒")

async def test_code_templates():
    """测试代码模板"""
    print("\n🧪 代码模板专项测试")
    print("-" * 40)
    
    # 初始化
    model_client = get_agent_model_client("CodeEngineer")
    system_message = AGENT_SYSTEM_PROMPTS["CodeEngineer"]
    capabilities = AGENT_CAPABILITIES["CodeEngineer"]
    code_engineer = CodeEngineer(model_client, system_message, capabilities)
    
    # 测试各种模块组合
    module_combinations = [
        ["gpio"],
        ["adc"],
        ["uart"],
        ["mcpwm"],
        ["dma"],
        ["gpio", "adc"],
        ["uart", "dma"],
        ["gpio", "adc", "uart"],
        ["mcpwm", "adc", "dma"]
    ]
    
    for i, modules in enumerate(module_combinations, 1):
        print(f"\n🔧 组合 {i}: {', '.join(modules)}")
        requirement = f"初始化{', '.join(modules)}模块"
        
        try:
            response = await code_engineer.handle_message(requirement)
            print(f"✅ 生成成功")
            
            # 检查是否包含所有模块的代码
            for module in modules:
                if module.upper() in response or f"{module}_" in response:
                    print(f"  ✓ 包含{module}模块代码")
                else:
                    print(f"  ✗ 缺少{module}模块代码")
                    
        except Exception as e:
            print(f"❌ 生成失败: {e}")

async def interactive_code_test():
    """交互式代码生成测试"""
    print("\n🎯 交互式代码生成测试")
    print("输入 'templates' 查看可用模板")
    print("输入 'quit' 退出")
    print("-" * 30)
    
    # 初始化
    model_client = get_agent_model_client("CodeEngineer")
    system_message = AGENT_SYSTEM_PROMPTS["CodeEngineer"]
    capabilities = AGENT_CAPABILITIES["CodeEngineer"]
    code_engineer = CodeEngineer(model_client, system_message, capabilities)
    
    while True:
        try:
            requirement = input("\n💻 请描述代码需求: ").strip()
            
            if requirement.lower() in ['quit', 'exit', 'q']:
                break
            
            if requirement.lower() == 'templates':
                print("\n📚 可用代码模板:")
                for template_name in code_engineer.code_templates.keys():
                    print(f"  • {template_name}")
                continue
            
            if not requirement:
                continue
            
            print("⚙️  正在生成代码...")
            response = await code_engineer.handle_message(requirement)
            print(f"\n💻 生成的代码:\n{response}")
            
            # 询问是否保存代码
            save = input("\n💾 是否保存代码到文件？(y/n): ").strip().lower()
            if save in ['y', 'yes']:
                filename = input("📁 请输入文件名 (如: test.c): ").strip()
                if filename:
                    try:
                        # 提取代码部分
                        if "```c" in response:
                            code_start = response.find("```c") + 4
                            code_end = response.find("```", code_start)
                            code_content = response[code_start:code_end].strip()
                        else:
                            code_content = response
                        
                        with open(filename, 'w', encoding='utf-8') as f:
                            f.write(code_content)
                        print(f"✅ 代码已保存到 {filename}")
                    except Exception as e:
                        print(f"❌ 保存失败: {e}")
            
        except KeyboardInterrupt:
            break
        except Exception as e:
            print(f"❌ 错误: {e}")
    
    print("👋 代码生成测试结束")

async def main():
    """主函数"""
    print("💻 代码工程师测试工具")
    print("选择测试模式:")
    print("1. 预设测试用例")
    print("2. 代码模板测试")
    print("3. 交互式测试")
    
    try:
        choice = input("请选择 (1/2/3): ").strip()
        
        if choice == "1":
            await test_code_engineer()
        elif choice == "2":
            await test_code_templates()
        elif choice == "3":
            await interactive_code_test()
        else:
            print("无效选择，运行预设测试")
            await test_code_engineer()
            
    except KeyboardInterrupt:
        print("\n👋 再见！")

if __name__ == "__main__":
    asyncio.run(main())
