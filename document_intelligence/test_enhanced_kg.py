#!/usr/bin/env python3
"""
测试增强型知识图谱构建智能体
"""
import asyncio
import sys
import os
import time

sys.path.append(os.path.dirname(os.path.abspath(__file__)))
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from config.models_config import get_agent_model_client
from config.agents_config import AGENT_SYSTEM_PROMPTS, AGENT_CAPABILITIES
from agents.enhanced_kg_builder import EnhancedKGBuilder

async def test_enhanced_kg_performance():
    """测试增强知识图谱性能"""
    print("🚀 增强型知识图谱性能测试")
    print("=" * 50)
    
    # 初始化智能体
    print("🔧 初始化增强型知识图谱构建智能体...")
    try:
        model_client = get_agent_model_client("KnowledgeGraphBuilder")
        system_message = AGENT_SYSTEM_PROMPTS["KnowledgeGraphBuilder"]
        capabilities = AGENT_CAPABILITIES["KnowledgeGraphBuilder"]
        
        enhanced_kg = EnhancedKGBuilder(model_client, system_message, capabilities)
        print("✅ 初始化完成")
        
    except Exception as e:
        print(f"❌ 初始化失败: {e}")
        return
    
    # 性能测试用例
    test_cases = [
        {
            "category": "小规模测试",
            "request": "构建知识图谱",
            "description": "测试基本功能和性能"
        },
        {
            "category": "图谱分析",
            "request": "分析知识图谱",
            "description": "测试分析功能"
        },
        {
            "category": "实体查询",
            "request": "查询实体 ADC模块",
            "description": "测试向量查询功能"
        },
        {
            "category": "导出测试",
            "request": "导出知识图谱 html",
            "description": "测试增强可视化导出"
        }
    ]
    
    print(f"\n🧪 开始性能测试 ({len(test_cases)} 个用例):")
    
    total_start_time = time.time()
    
    for i, test_case in enumerate(test_cases, 1):
        print(f"\n📝 测试 {i}: {test_case['category']}")
        print(f"📋 请求: {test_case['request']}")
        print(f"📄 描述: {test_case['description']}")
        print("-" * 60)
        
        try:
            start_time = time.time()
            response = await enhanced_kg.handle_message(test_case['request'])
            end_time = time.time()
            
            print(f"🚀 处理结果:")
            # 显示结果摘要
            lines = response.split('\n')
            summary_lines = []
            for line in lines:
                if any(keyword in line for keyword in ['✅', '📊', '节点数', '边数', '处理时间', '性能', '导出']):
                    summary_lines.append(line)
            
            if summary_lines:
                for line in summary_lines[:8]:  # 显示前8行关键信息
                    print(f"  {line}")
            else:
                print(f"  {response[:300]}...")
            
            print(f"\n⏱️  处理时间: {end_time - start_time:.2f}秒")
            
            # 性能指标
            if i == 1:  # 第一个测试记录基准性能
                if "节点数" in response:
                    import re
                    nodes_match = re.search(r'节点数.*?(\d+)', response)
                    edges_match = re.search(r'边数.*?(\d+)', response)
                    if nodes_match and edges_match:
                        nodes = int(nodes_match.group(1))
                        edges = int(edges_match.group(1))
                        processing_speed = nodes / (end_time - start_time) if (end_time - start_time) > 0 else 0
                        print(f"📈 处理速度: {processing_speed:.1f} 节点/秒")
            
        except Exception as e:
            print(f"❌ 测试失败: {e}")
        
        print("\n" + "=" * 60)
        
        # 添加延时
        await asyncio.sleep(1)
    
    total_time = time.time() - total_start_time
    print(f"\n🎉 性能测试完成！总耗时: {total_time:.2f}秒")

async def test_vector_search():
    """测试向量搜索功能"""
    print("\n🔍 向量搜索功能测试")
    print("-" * 40)
    
    # 初始化
    model_client = get_agent_model_client("KnowledgeGraphBuilder")
    system_message = AGENT_SYSTEM_PROMPTS["KnowledgeGraphBuilder"]
    capabilities = AGENT_CAPABILITIES["KnowledgeGraphBuilder"]
    enhanced_kg = EnhancedKGBuilder(model_client, system_message, capabilities)
    
    # 先构建一个小图谱
    print("📊 构建测试图谱...")
    await enhanced_kg.handle_message("构建知识图谱")
    
    # 测试查询
    queries = [
        "ADC",
        "寄存器",
        "模数转换",
        "GPIO",
        "配置"
    ]
    
    print(f"\n🔍 测试 {len(queries)} 个查询:")
    
    for query in queries:
        print(f"\n🔎 查询: '{query}'")
        try:
            start_time = time.time()
            result = await enhanced_kg.handle_message(f"查询实体 {query}")
            end_time = time.time()
            
            # 提取关键信息
            lines = result.split('\n')
            entity_count = 0
            for line in lines:
                if line.startswith('## '):
                    entity_count += 1
            
            print(f"  📊 找到 {entity_count} 个相关实体")
            print(f"  ⏱️  查询时间: {end_time - start_time:.3f}秒")
            
            # 显示第一个结果
            if entity_count > 0:
                for line in lines:
                    if line.startswith('## 1.'):
                        print(f"  🎯 最相关: {line[4:]}")
                        break
            
        except Exception as e:
            print(f"  ❌ 查询失败: {e}")

async def compare_performance():
    """对比原版和增强版性能"""
    print("\n⚖️  性能对比测试")
    print("-" * 40)
    
    # 导入原版
    from agents.knowledge_graph_builder import KnowledgeGraphBuilder
    
    # 初始化两个版本
    model_client = get_agent_model_client("KnowledgeGraphBuilder")
    system_message = AGENT_SYSTEM_PROMPTS["KnowledgeGraphBuilder"]
    capabilities = AGENT_CAPABILITIES["KnowledgeGraphBuilder"]
    
    original_kg = KnowledgeGraphBuilder(model_client, system_message, capabilities)
    enhanced_kg = EnhancedKGBuilder(model_client, system_message, capabilities)
    
    print("📊 对比构建性能...")
    
    # 测试原版
    print("\n🔵 原版知识图谱:")
    try:
        start_time = time.time()
        original_result = await original_kg.handle_message("构建知识图谱")
        original_time = time.time() - start_time
        
        # 提取统计信息
        original_nodes = 0
        original_edges = 0
        if "实体总数" in original_result:
            import re
            nodes_match = re.search(r'实体总数.*?(\d+)', original_result)
            edges_match = re.search(r'关系总数.*?(\d+)', original_result)
            if nodes_match:
                original_nodes = int(nodes_match.group(1))
            if edges_match:
                original_edges = int(edges_match.group(1))
        
        print(f"  ⏱️  时间: {original_time:.2f}秒")
        print(f"  📊 节点: {original_nodes}")
        print(f"  🔗 边: {original_edges}")
        print(f"  🚀 速度: {original_nodes/original_time:.1f} 节点/秒" if original_time > 0 else "  🚀 速度: N/A")
        
    except Exception as e:
        print(f"  ❌ 原版测试失败: {e}")
        original_time = float('inf')
        original_nodes = 0
        original_edges = 0
    
    # 测试增强版
    print("\n🟢 增强版知识图谱:")
    try:
        start_time = time.time()
        enhanced_result = await enhanced_kg.handle_message("构建知识图谱")
        enhanced_time = time.time() - start_time
        
        # 提取统计信息
        enhanced_nodes = 0
        enhanced_edges = 0
        if "节点数" in enhanced_result:
            import re
            nodes_match = re.search(r'节点数.*?(\d+)', enhanced_result)
            edges_match = re.search(r'边数.*?(\d+)', enhanced_result)
            if nodes_match:
                enhanced_nodes = int(nodes_match.group(1))
            if edges_match:
                enhanced_edges = int(edges_match.group(1))
        
        print(f"  ⏱️  时间: {enhanced_time:.2f}秒")
        print(f"  📊 节点: {enhanced_nodes}")
        print(f"  🔗 边: {enhanced_edges}")
        print(f"  🚀 速度: {enhanced_nodes/enhanced_time:.1f} 节点/秒" if enhanced_time > 0 else "  🚀 速度: N/A")
        
    except Exception as e:
        print(f"  ❌ 增强版测试失败: {e}")
        enhanced_time = float('inf')
        enhanced_nodes = 0
        enhanced_edges = 0
    
    # 对比分析
    print(f"\n📈 性能对比:")
    if original_time != float('inf') and enhanced_time != float('inf'):
        speed_improvement = original_time / enhanced_time if enhanced_time > 0 else 0
        node_improvement = enhanced_nodes / original_nodes if original_nodes > 0 else 0
        edge_improvement = enhanced_edges / original_edges if original_edges > 0 else 0
        
        print(f"  🚀 速度提升: {speed_improvement:.1f}x")
        print(f"  📊 节点提升: {node_improvement:.1f}x")
        print(f"  🔗 边提升: {edge_improvement:.1f}x")
        
        if speed_improvement > 1:
            print("  ✅ 增强版性能更优")
        elif speed_improvement < 1:
            print("  ⚠️  原版性能更优")
        else:
            print("  ➖ 性能相当")
    else:
        print("  ❌ 无法进行有效对比")

async def interactive_enhanced_kg():
    """交互式增强知识图谱操作"""
    print("\n🎯 交互式增强知识图谱操作")
    print("支持的操作:")
    print("- 构建知识图谱")
    print("- 分析知识图谱")
    print("- 查询实体 <内容>")
    print("- 导出知识图谱 [格式]")
    print("输入 'quit' 退出")
    print("-" * 50)
    
    # 初始化
    try:
        model_client = get_agent_model_client("KnowledgeGraphBuilder")
        system_message = AGENT_SYSTEM_PROMPTS["KnowledgeGraphBuilder"]
        capabilities = AGENT_CAPABILITIES["KnowledgeGraphBuilder"]
        enhanced_kg = EnhancedKGBuilder(model_client, system_message, capabilities)
    except Exception as e:
        print(f"❌ 初始化失败: {e}")
        return
    
    while True:
        try:
            request = input("\n🚀 请输入操作: ").strip()
            
            if request.lower() in ['quit', 'exit', 'q']:
                break
            
            if not request:
                continue
            
            print("⚡ 正在处理...")
            start_time = time.time()
            response = await enhanced_kg.handle_message(request)
            end_time = time.time()
            
            print(f"\n📋 处理结果:\n{response}")
            print(f"\n⏱️  处理时间: {end_time - start_time:.2f}秒")
            
        except KeyboardInterrupt:
            break
        except Exception as e:
            print(f"❌ 错误: {e}")
    
    print("👋 增强知识图谱测试结束")

async def main():
    """主函数"""
    print("🚀 增强型知识图谱构建智能体测试工具")
    print("选择测试模式:")
    print("1. 性能测试")
    print("2. 向量搜索测试")
    print("3. 性能对比")
    print("4. 交互式测试")
    print("5. 全部测试")
    
    try:
        choice = input("请选择 (1/2/3/4/5): ").strip()
        
        if choice == "1":
            await test_enhanced_kg_performance()
        elif choice == "2":
            await test_vector_search()
        elif choice == "3":
            await compare_performance()
        elif choice == "4":
            await interactive_enhanced_kg()
        elif choice == "5":
            await test_enhanced_kg_performance()
            await test_vector_search()
            await compare_performance()
        else:
            print("无效选择，运行性能测试")
            await test_enhanced_kg_performance()
            
    except KeyboardInterrupt:
        print("\n👋 再见！")

if __name__ == "__main__":
    asyncio.run(main())
