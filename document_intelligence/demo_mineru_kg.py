#!/usr/bin/env python3
"""
Mineru输出知识图谱构建演示
"""
import asyncio
import sys
import os
import json

sys.path.append(os.path.dirname(os.path.abspath(__file__)))
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from config.models_config import get_agent_model_client
from config.agents_config import AGENT_SYSTEM_PROMPTS, AGENT_CAPABILITIES
from agents.knowledge_graph_builder import KnowledgeGraphBuilder

async def demo_mineru_processing():
    """演示Mineru输出处理"""
    print("🧠 Mineru输出知识图谱构建演示")
    print("=" * 50)
    
    # 检查Mineru输出
    mineru_path = "/Users/<USER>/Public/AutoGen/monkey_ocr_project/mineru_output"
    
    if not os.path.exists(mineru_path):
        print(f"❌ Mineru输出路径不存在: {mineru_path}")
        return
    
    print(f"✅ Mineru输出路径: {mineru_path}")
    
    # 扫描文件
    content_files = []
    for root, dirs, files in os.walk(mineru_path):
        for file in files:
            if file.endswith('_content_list.json'):
                file_path = os.path.join(root, file)
                file_size = os.path.getsize(file_path)
                content_files.append((file, file_path, file_size))
    
    print(f"📄 找到 {len(content_files)} 个content_list.json文件:")
    for file, path, size in content_files:
        print(f"  • {file}: {size:,} 字节")
    
    if not content_files:
        print("❌ 未找到content_list.json文件")
        return
    
    # 初始化知识图谱构建器
    print("\n🔧 初始化知识图谱构建器...")
    model_client = get_agent_model_client("KnowledgeGraphBuilder")
    system_message = AGENT_SYSTEM_PROMPTS["KnowledgeGraphBuilder"]
    capabilities = AGENT_CAPABILITIES["KnowledgeGraphBuilder"]
    
    kg_builder = KnowledgeGraphBuilder(model_client, system_message, capabilities)
    print("✅ 初始化完成")
    
    # 处理小样本数据
    print("\n📊 处理小样本数据...")
    sample_file, sample_path, sample_size = content_files[0]
    
    # 读取前100条记录作为样本
    with open(sample_path, 'r', encoding='utf-8') as f:
        full_content = json.load(f)
    
    sample_content = full_content[:100]  # 只取前100条
    print(f"📝 样本数据: {len(sample_content)} 条记录")
    
    # 手动处理样本数据
    entities_count = 0
    relations_count = 0
    
    print("\n🔍 开始处理样本数据...")
    
    current_chapter = None
    current_section = None
    
    for i, item in enumerate(sample_content):
        if item.get('type') == 'text':
            text = item.get('text', '').strip()
            text_level = item.get('text_level', 0)
            page_idx = item.get('page_idx', 0)
            
            if not text:
                continue
            
            print(f"  处理第 {i+1}/{len(sample_content)} 条: {text[:50]}...")
            
            # 处理章节标题
            if text_level == 1:
                current_chapter = text
                chapter_id = f"chapter_{kg_builder._normalize_id(text)}"
                kg_builder._add_entity(chapter_id, text, 'chapter', {'page': page_idx})
                entities_count += 1
                
            elif text_level == 2:
                current_section = text
                section_id = f"section_{kg_builder._normalize_id(text)}"
                kg_builder._add_entity(section_id, text, 'section', {'page': page_idx})
                entities_count += 1
                
                # 建立章节-小节关系
                if current_chapter:
                    chapter_id = f"chapter_{kg_builder._normalize_id(current_chapter)}"
                    kg_builder._add_relation(chapter_id, section_id, 'contains')
                    relations_count += 1
            
            # 使用规则提取实体
            extracted = kg_builder._rule_based_extraction(text, page_idx)
            
            # 添加提取的实体
            for entity in extracted['entities']:
                kg_builder._add_entity(entity['id'], entity['name'], entity['type'], {
                    'description': entity.get('description', ''),
                    'page': page_idx
                })
                entities_count += 1
                
                # 建立与当前章节的关系
                if current_section:
                    section_id = f"section_{kg_builder._normalize_id(current_section)}"
                    if section_id in kg_builder.knowledge_graph.nodes:
                        kg_builder._add_relation(section_id, entity['id'], 'contains')
                        relations_count += 1
    
    print(f"\n✅ 样本处理完成:")
    print(f"  • 处理记录: {len(sample_content)} 条")
    print(f"  • 提取实体: {kg_builder.knowledge_graph.number_of_nodes()} 个")
    print(f"  • 建立关系: {kg_builder.knowledge_graph.number_of_edges()} 个")
    
    # 分析图谱
    print("\n📊 分析知识图谱...")
    if kg_builder.knowledge_graph.number_of_nodes() > 0:
        stats = kg_builder._calculate_graph_statistics()
        print("图谱统计:")
        print(stats[:500] + "..." if len(stats) > 500 else stats)
    else:
        print("⚠️  图谱为空")
    
    # 导出结果
    print("\n💾 导出知识图谱...")
    
    if kg_builder.knowledge_graph.number_of_nodes() > 0:
        # 导出JSON
        json_result = await kg_builder._export_knowledge_graph('json')
        print(f"📄 {json_result}")
        
        # 导出HTML
        html_result = await kg_builder._export_knowledge_graph('html')
        print(f"🌐 {html_result}")
        
        # 导出PNG
        png_result = await kg_builder._export_knowledge_graph('png')
        print(f"🖼️  {png_result}")
        
        # 显示输出文件信息
        output_dir = kg_builder.output_dir
        if os.path.exists(output_dir):
            files = os.listdir(output_dir)
            print(f"\n📁 输出文件 ({output_dir}):")
            for file in files:
                file_path = os.path.join(output_dir, file)
                file_size = os.path.getsize(file_path)
                print(f"  • {file}: {file_size:,} 字节")
    else:
        print("❌ 图谱为空，无法导出")
    
    print("\n🎉 Mineru知识图谱构建演示完成！")

async def analyze_mineru_structure():
    """分析Mineru输出结构"""
    print("\n🔍 分析Mineru输出结构")
    print("-" * 40)
    
    mineru_path = "/Users/<USER>/Public/AutoGen/monkey_ocr_project/mineru_output"
    
    if not os.path.exists(mineru_path):
        print(f"❌ 路径不存在: {mineru_path}")
        return
    
    # 扫描所有文件
    all_files = []
    for root, dirs, files in os.walk(mineru_path):
        for file in files:
            file_path = os.path.join(root, file)
            file_size = os.path.getsize(file_path)
            rel_path = os.path.relpath(file_path, mineru_path)
            all_files.append((rel_path, file_size))
    
    print(f"📁 总文件数: {len(all_files)}")
    
    # 按类型分组
    file_types = {}
    for file_path, file_size in all_files:
        ext = os.path.splitext(file_path)[1].lower()
        if ext not in file_types:
            file_types[ext] = []
        file_types[ext].append((file_path, file_size))
    
    print(f"\n📊 文件类型分布:")
    for ext, files in sorted(file_types.items()):
        total_size = sum(size for _, size in files)
        print(f"  • {ext or '无扩展名'}: {len(files)} 个文件, {total_size:,} 字节")
    
    # 分析content_list.json
    content_files = [(path, size) for path, size in all_files if path.endswith('_content_list.json')]
    
    if content_files:
        print(f"\n📄 Content List文件分析:")
        for file_path, file_size in content_files:
            print(f"  • {file_path}: {file_size:,} 字节")
            
            # 读取并分析内容
            full_path = os.path.join(mineru_path, file_path)
            try:
                with open(full_path, 'r', encoding='utf-8') as f:
                    content = json.load(f)
                
                print(f"    - 总记录数: {len(content)}")
                
                # 统计类型
                type_counts = {}
                text_level_counts = {}
                page_counts = {}
                
                for item in content:
                    item_type = item.get('type', 'unknown')
                    type_counts[item_type] = type_counts.get(item_type, 0) + 1
                    
                    if item_type == 'text':
                        text_level = item.get('text_level', 0)
                        text_level_counts[text_level] = text_level_counts.get(text_level, 0) + 1
                    
                    page_idx = item.get('page_idx', 0)
                    page_counts[page_idx] = page_counts.get(page_idx, 0) + 1
                
                print(f"    - 类型分布: {dict(type_counts)}")
                print(f"    - 文本级别分布: {dict(text_level_counts)}")
                print(f"    - 页面范围: {min(page_counts.keys())} - {max(page_counts.keys())}")
                
            except Exception as e:
                print(f"    - 读取失败: {e}")

async def main():
    """主函数"""
    print("🧠 Mineru知识图谱构建演示工具")
    print("选择操作:")
    print("1. 分析Mineru输出结构")
    print("2. 构建知识图谱演示")
    print("3. 全部执行")
    
    try:
        choice = input("请选择 (1/2/3): ").strip()
        
        if choice == "1":
            await analyze_mineru_structure()
        elif choice == "2":
            await demo_mineru_processing()
        elif choice == "3":
            await analyze_mineru_structure()
            await demo_mineru_processing()
        else:
            print("无效选择，执行全部操作")
            await analyze_mineru_structure()
            await demo_mineru_processing()
            
    except KeyboardInterrupt:
        print("\n👋 再见！")

if __name__ == "__main__":
    asyncio.run(main())
