#!/usr/bin/env python3
"""
单个智能体测试脚本
用于独立测试每个智能体的功能
"""
import asyncio
import sys
import os

# 添加路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from config.models_config import get_agent_model_client
from config.agents_config import AGENT_SYSTEM_PROMPTS, AGENT_CAPABILITIES
from agents import DocumentManager, TechnicalExpert, CodeEngineer, SystemArchitect, Coordinator

class AgentTester:
    """智能体测试器"""
    
    def __init__(self):
        self.agents = {}
        self.test_cases = {
            "DocumentManager": [
                "SYS_AFE_REG1寄存器的功能是什么？",
                "查找ADC相关的寄存器",
                "GPIO配置寄存器有哪些？",
                "FLASH模块的地址空间",
                "MCPWM相关章节"
            ],
            "TechnicalExpert": [
                "如何优化ADC采样精度？",
                "MCPWM死区时间如何设置？",
                "系统功耗优化策略",
                "电机控制算法选择",
                "CAN通信波特率配置问题"
            ],
            "CodeEngineer": [
                "生成GPIO初始化代码",
                "编写ADC中断处理函数",
                "实现UART发送接收函数",
                "配置MCPWM输出PWM信号",
                "创建DMA传输配置代码"
            ],
            "SystemArchitect": [
                "设计数据采集系统架构",
                "电机控制系统设计方案",
                "低功耗系统设计",
                "通信网关系统架构",
                "工业控制器整体设计"
            ],
            "Coordinator": [
                "如何实现完整的电机驱动系统？",
                "设计一个多传感器数据采集和处理系统",
                "开发工业自动化控制器",
                "实现智能电源管理系统",
                "构建实时通信网关"
            ]
        }
    
    async def initialize_agent(self, agent_name: str):
        """初始化指定智能体"""
        print(f"🔧 初始化 {agent_name}...")
        
        try:
            model_client = get_agent_model_client(agent_name)
            system_message = AGENT_SYSTEM_PROMPTS[agent_name]
            capabilities = AGENT_CAPABILITIES[agent_name]
            
            if agent_name == "DocumentManager":
                agent = DocumentManager(model_client, system_message, capabilities)
            elif agent_name == "TechnicalExpert":
                agent = TechnicalExpert(model_client, system_message, capabilities)
            elif agent_name == "CodeEngineer":
                agent = CodeEngineer(model_client, system_message, capabilities)
            elif agent_name == "SystemArchitect":
                agent = SystemArchitect(model_client, system_message, capabilities)
            elif agent_name == "Coordinator":
                agent = Coordinator(model_client, system_message, capabilities)
            else:
                raise ValueError(f"未知的智能体: {agent_name}")
            
            self.agents[agent_name] = agent
            print(f"✅ {agent_name} 初始化成功")
            return True
            
        except Exception as e:
            print(f"❌ {agent_name} 初始化失败: {e}")
            return False
    
    async def test_agent(self, agent_name: str, test_case: str = None):
        """测试指定智能体"""
        if agent_name not in self.agents:
            success = await self.initialize_agent(agent_name)
            if not success:
                return
        
        agent = self.agents[agent_name]
        
        print(f"\n🧪 测试 {agent_name}")
        print("=" * 50)
        
        # 显示智能体信息
        capabilities = agent.get_capabilities()
        print(f"📋 能力: {', '.join(capabilities['capabilities']['primary_skills'])}")
        print(f"🛠️  工具: {', '.join(capabilities['tools'])}")
        print(f"🎯 响应风格: {capabilities['capabilities']['response_style']}")
        
        # 选择测试用例
        if test_case:
            test_cases = [test_case]
        else:
            test_cases = self.test_cases.get(agent_name, ["默认测试"])
        
        print(f"\n📝 测试用例 ({len(test_cases)} 个):")
        
        for i, case in enumerate(test_cases, 1):
            print(f"\n🔍 测试 {i}: {case}")
            print("-" * 40)
            
            try:
                start_time = asyncio.get_event_loop().time()
                response = await agent.handle_message(case)
                end_time = asyncio.get_event_loop().time()
                
                print(f"💡 回答:\n{response}")
                print(f"⏱️  响应时间: {end_time - start_time:.2f}秒")
                
            except Exception as e:
                print(f"❌ 测试失败: {e}")
            
            print()
        
        # 显示统计信息
        stats = agent.get_status()["stats"]
        print(f"📊 统计信息:")
        print(f"  • 处理请求: {stats['requests_handled']}")
        print(f"  • 成功率: {stats['success_rate']:.1%}")
        print(f"  • 平均响应时间: {stats['average_response_time']:.2f}秒")
    
    async def test_all_agents(self):
        """测试所有智能体"""
        print("🚀 开始测试所有智能体")
        print("=" * 60)
        
        agent_names = ["DocumentManager", "TechnicalExpert", "CodeEngineer", "SystemArchitect", "Coordinator"]
        
        for agent_name in agent_names:
            await self.test_agent(agent_name)
            print("\n" + "=" * 60)
            
            # 添加延时避免请求过快
            await asyncio.sleep(1)
        
        print("✅ 所有智能体测试完成")
    
    async def interactive_test(self):
        """交互式测试"""
        print("🎯 交互式智能体测试")
        print("可用智能体: DocumentManager, TechnicalExpert, CodeEngineer, SystemArchitect, Coordinator")
        print("输入格式: <智能体名称> <测试问题>")
        print("输入 'list' 查看预设测试用例")
        print("输入 'quit' 退出")
        print("-" * 50)
        
        while True:
            try:
                user_input = input("\n💬 请输入测试命令: ").strip()
                
                if not user_input:
                    continue
                
                if user_input.lower() in ['quit', 'exit', 'q']:
                    break
                
                if user_input.lower() == 'list':
                    print("\n📋 预设测试用例:")
                    for agent_name, cases in self.test_cases.items():
                        print(f"\n🤖 {agent_name}:")
                        for i, case in enumerate(cases, 1):
                            print(f"  {i}. {case}")
                    continue
                
                # 解析输入
                parts = user_input.split(' ', 1)
                if len(parts) < 2:
                    print("❌ 请提供智能体名称和测试问题")
                    continue
                
                agent_name = parts[0]
                test_question = parts[1]
                
                if agent_name not in ["DocumentManager", "TechnicalExpert", "CodeEngineer", "SystemArchitect", "Coordinator"]:
                    print(f"❌ 未知的智能体: {agent_name}")
                    continue
                
                await self.test_agent(agent_name, test_question)
                
            except KeyboardInterrupt:
                break
            except Exception as e:
                print(f"❌ 错误: {e}")
        
        print("👋 交互式测试结束")
    
    async def benchmark_test(self):
        """性能基准测试"""
        print("⚡ 智能体性能基准测试")
        print("-" * 40)
        
        test_question = "如何配置GPIO作为输出？"
        agent_names = ["DocumentManager", "TechnicalExpert", "CodeEngineer", "SystemArchitect"]
        
        results = {}
        
        for agent_name in agent_names:
            print(f"\n🔍 测试 {agent_name}...")
            
            if agent_name not in self.agents:
                await self.initialize_agent(agent_name)
            
            agent = self.agents[agent_name]
            
            # 多次测试取平均值
            times = []
            for i in range(3):
                start_time = asyncio.get_event_loop().time()
                try:
                    await agent.handle_message(test_question)
                    end_time = asyncio.get_event_loop().time()
                    times.append(end_time - start_time)
                except Exception as e:
                    print(f"  测试 {i+1} 失败: {e}")
            
            if times:
                avg_time = sum(times) / len(times)
                results[agent_name] = avg_time
                print(f"  平均响应时间: {avg_time:.2f}秒")
        
        # 显示排名
        print(f"\n🏆 性能排名:")
        sorted_results = sorted(results.items(), key=lambda x: x[1])
        for i, (agent_name, time) in enumerate(sorted_results, 1):
            print(f"  {i}. {agent_name}: {time:.2f}秒")

async def main():
    """主函数"""
    tester = AgentTester()
    
    print("🧪 LKS32MC08x智能体测试工具")
    print("选择测试模式:")
    print("1. 测试单个智能体")
    print("2. 测试所有智能体")
    print("3. 交互式测试")
    print("4. 性能基准测试")
    
    try:
        choice = input("请选择 (1/2/3/4): ").strip()
        
        if choice == "1":
            print("\n可用智能体:")
            agents = ["DocumentManager", "TechnicalExpert", "CodeEngineer", "SystemArchitect", "Coordinator"]
            for i, agent in enumerate(agents, 1):
                print(f"  {i}. {agent}")
            
            agent_choice = input("请选择智能体编号: ").strip()
            try:
                agent_index = int(agent_choice) - 1
                if 0 <= agent_index < len(agents):
                    await tester.test_agent(agents[agent_index])
                else:
                    print("无效选择")
            except ValueError:
                print("请输入有效数字")
                
        elif choice == "2":
            await tester.test_all_agents()
        elif choice == "3":
            await tester.interactive_test()
        elif choice == "4":
            await tester.benchmark_test()
        else:
            print("无效选择")
            
    except KeyboardInterrupt:
        print("\n👋 再见！")

if __name__ == "__main__":
    asyncio.run(main())
