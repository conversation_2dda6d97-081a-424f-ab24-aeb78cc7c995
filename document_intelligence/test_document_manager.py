#!/usr/bin/env python3
"""
文档管理员智能体专项测试
"""
import asyncio
import sys
import os

sys.path.append(os.path.dirname(os.path.abspath(__file__)))
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from config.models_config import get_agent_model_client
from config.agents_config import AGENT_SYSTEM_PROMPTS, AGENT_CAPABILITIES
from agents.document_manager import DocumentManager

async def test_document_manager():
    """测试文档管理员"""
    print("📋 文档管理员智能体专项测试")
    print("=" * 50)
    
    # 初始化智能体
    print("🔧 初始化文档管理员...")
    model_client = get_agent_model_client("DocumentManager")
    system_message = AGENT_SYSTEM_PROMPTS["DocumentManager"]
    capabilities = AGENT_CAPABILITIES["DocumentManager"]
    
    doc_manager = DocumentManager(model_client, system_message, capabilities)
    print("✅ 初始化完成")
    
    # 显示文档统计
    doc_stats = doc_manager.get_document_stats()
    print(f"\n📊 文档统计:")
    print(f"  • 章节数量: {doc_stats['total_chapters']}")
    print(f"  • 寄存器数量: {doc_stats['total_registers']}")
    print(f"  • 文档已加载: {doc_stats['document_loaded']}")
    
    # 测试用例
    test_cases = [
        {
            "category": "寄存器查询",
            "query": "SYS_AFE_REG1寄存器的功能",
            "expected": "应该找到寄存器信息"
        },
        {
            "category": "模糊寄存器查询",
            "query": "ADC相关的寄存器有哪些",
            "expected": "应该列出ADC相关寄存器"
        },
        {
            "category": "章节搜索",
            "query": "GPIO配置相关章节",
            "expected": "应该找到GPIO相关章节"
        },
        {
            "category": "功能查询",
            "query": "FLASH模块的功能和配置",
            "expected": "应该提供FLASH相关信息"
        },
        {
            "category": "地址查询",
            "query": "MCPWM模块的基地址",
            "expected": "应该提供地址信息"
        }
    ]
    
    print(f"\n🧪 开始测试 ({len(test_cases)} 个用例):")
    
    for i, test_case in enumerate(test_cases, 1):
        print(f"\n📝 测试 {i}: {test_case['category']}")
        print(f"❓ 查询: {test_case['query']}")
        print(f"🎯 期望: {test_case['expected']}")
        print("-" * 40)
        
        try:
            start_time = asyncio.get_event_loop().time()
            response = await doc_manager.handle_message(test_case['query'])
            end_time = asyncio.get_event_loop().time()
            
            print(f"💡 回答:\n{response}")
            print(f"⏱️  响应时间: {end_time - start_time:.2f}秒")
            
        except Exception as e:
            print(f"❌ 测试失败: {e}")
        
        print()
    
    # 显示最终统计
    stats = doc_manager.get_status()["stats"]
    print(f"📊 最终统计:")
    print(f"  • 处理请求: {stats['requests_handled']}")
    print(f"  • 成功率: {stats['success_rate']:.1%}")
    print(f"  • 平均响应时间: {stats['average_response_time']:.2f}秒")

async def interactive_document_test():
    """交互式文档测试"""
    print("\n🎯 交互式文档查询测试")
    print("输入 'quit' 退出")
    print("-" * 30)
    
    # 初始化
    model_client = get_agent_model_client("DocumentManager")
    system_message = AGENT_SYSTEM_PROMPTS["DocumentManager"]
    capabilities = AGENT_CAPABILITIES["DocumentManager"]
    doc_manager = DocumentManager(model_client, system_message, capabilities)
    
    while True:
        try:
            query = input("\n📋 请输入文档查询: ").strip()
            
            if query.lower() in ['quit', 'exit', 'q']:
                break
            
            if not query:
                continue
            
            print("🔍 搜索中...")
            response = await doc_manager.handle_message(query)
            print(f"\n📄 查询结果:\n{response}")
            
        except KeyboardInterrupt:
            break
        except Exception as e:
            print(f"❌ 错误: {e}")
    
    print("👋 文档查询测试结束")

async def main():
    """主函数"""
    print("📋 文档管理员测试工具")
    print("选择测试模式:")
    print("1. 预设测试用例")
    print("2. 交互式测试")
    
    try:
        choice = input("请选择 (1/2): ").strip()
        
        if choice == "1":
            await test_document_manager()
        elif choice == "2":
            await interactive_document_test()
        else:
            print("无效选择，运行预设测试")
            await test_document_manager()
            
    except KeyboardInterrupt:
        print("\n👋 再见！")

if __name__ == "__main__":
    asyncio.run(main())
