from autogen_core.models import ModelInfo, ModelFamily, ChatCompletionClient
from autogen_core.models._types import CreateResult, SystemMessage, UserMessage, AssistantMessage, FunctionExecutionResultMessage, RequestUsage
from autogen_core.tools._base import Tool, ToolSchema
from autogen_core._cancellation_token import CancellationToken
from typing import Dict, Any, List, Sequence, Union, Mapping, Optional
import aiohttp
from pydantic import BaseModel

class LMStudioChatCompletionClient(ChatCompletionClient):
    def __init__(
        self,
        model: str,
        host: str = "http://localhost:1234/v1",
        model_info: ModelInfo = None,
        default_params: Dict[str, Any] = None,
    ):
        self.model = model
        self.host = host
        self._model_info = model_info or ModelInfo()
        self.default_params = default_params or {}

    @property
    def model_info(self) -> ModelInfo:
        return self._model_info

    @property
    def capabilities(self) -> ModelInfo:
        return self._model_info

    def _convert_messages(self, messages: Sequence[Union[SystemMessage, UserMessage, AssistantMessage, FunctionExecutionResultMessage]]) -> List[Dict[str, Any]]:
        """将AutoGen消息格式转换为OpenAI API格式"""
        converted = []
        for msg in messages:
            if isinstance(msg, SystemMessage):
                converted.append({
                    "role": "system",
                    "content": msg.content
                })
            elif isinstance(msg, UserMessage):
                converted.append({
                    "role": "user",
                    "content": msg.content
                })
            elif isinstance(msg, AssistantMessage):
                converted.append({
                    "role": "assistant",
                    "content": msg.content
                })
            elif isinstance(msg, FunctionExecutionResultMessage):
                # 将函数执行结果作为用户消息
                converted.append({
                    "role": "user",
                    "content": f"Function result: {msg.content}"
                })
            else:
                # 处理其他消息类型
                converted.append({
                    "role": "user",
                    "content": str(msg.content) if hasattr(msg, 'content') else str(msg)
                })
        return converted

    async def create(
        self,
        messages: Sequence[Union[SystemMessage, UserMessage, AssistantMessage, FunctionExecutionResultMessage]],
        *,
        tools: Sequence[Union[Tool, ToolSchema]] = [],
        json_output: Union[bool, type[BaseModel], None] = None,
        extra_create_args: Mapping[str, Any] = {},
        cancellation_token: Optional[CancellationToken] = None
    ) -> CreateResult:
        """异步创建聊天完成"""
        # 合并默认参数和传入参数
        final_params = {**self.default_params, **extra_create_args}

        # 转换消息格式
        api_messages = self._convert_messages(messages)

        payload = {
            "model": self.model,
            "messages": api_messages,
            **final_params
        }

        async with aiohttp.ClientSession() as session:
            async with session.post(
                f"{self.host}/chat/completions",
                headers={"Content-Type": "application/json"},
                json=payload
            ) as response:
                if response.status != 200:
                    error_text = await response.text()
                    raise ValueError(f"LM Studio API 请求失败: {error_text}")

                result = await response.json()

                # 返回AutoGen期望的格式
                content = result["choices"][0]["message"]["content"]

                # 创建使用情况对象
                usage_data = result.get("usage", {})
                usage = RequestUsage(
                    prompt_tokens=usage_data.get("prompt_tokens", 0),
                    completion_tokens=usage_data.get("completion_tokens", 0)
                )

                return CreateResult(
                    content=content,
                    usage=usage,
                    finish_reason=result["choices"][0].get("finish_reason", "stop"),
                    cached=False,  # LM Studio不支持缓存
                    logprobs=None
                )

    async def create_stream(self, messages, **kwargs):
        """流式创建聊天完成 - 简单实现"""
        result = await self.create(messages, **kwargs)
        yield result

    async def close(self):
        """关闭客户端"""
        pass

    def actual_usage(self):
        """返回实际使用情况"""
        return {}

    def total_usage(self):
        """返回总使用情况"""
        return {}

    def count_tokens(self, messages, tools=None):
        """计算token数量 - 简单估算"""
        total = 0
        for msg in messages:
            if hasattr(msg, 'content'):
                total += len(str(msg.content).split()) * 1.3  # 简单估算
        return int(total)

    def remaining_tokens(self, messages, tools=None):
        """剩余token数量"""
        return 4096 - self.count_tokens(messages, tools)

LMSTUDIO_HOST = "http://localhost:1234/v1"

# LM Studio 模型注册表
LMSTUDIO_MODEL_REGISTRY = {
    "Tqwen3-32b-mlx": {
        "aliases": ["qwen3-32b", "qwen3-32b-mlxl"],
        "model_info": ModelInfo(
            vision=False,
            function_calling=True,
            structured_output=True,
            json_output=True,
            family=ModelFamily.UNKNOWN,
        ),
        "default_params": {"temperature": 0.7, "max_tokens": 512},
    },
    "TheBloke/Llama-2-7B-Chat-GGUF": {
        "aliases": ["llama2-7b", "llama2"],
        "model_info": ModelInfo(
            vision=False,
            function_calling=True,
            structured_output=False,
            json_output=False,
            family=ModelFamily.UNKNOWN,
        ),
        "default_params": {"temperature": 0.6, "max_tokens": 1024},
    },
}

# 创建别名反查表
_lmstudio_alias_to_tag = {
    alias: tag
    for tag, config in LMSTUDIO_MODEL_REGISTRY.items()
    for alias in config.get("aliases", []) + [tag]
}

def get_lmstudio_model_config(name: str) -> dict:
    actual_tag = _lmstudio_alias_to_tag.get(name)
    if not actual_tag:
        raise ValueError(f"LM Studio 模型别名 '{name}' 未找到，请确认注册！")
    return LMSTUDIO_MODEL_REGISTRY[actual_tag]

def get_lmstudio_client(name: str) -> LMStudioChatCompletionClient:
    cfg = get_lmstudio_model_config(name)
    return LMStudioChatCompletionClient(
        model=_lmstudio_alias_to_tag[name],  # 实际 LM Studio 加载的模型名称
        host=LMSTUDIO_HOST,
        model_info=cfg["model_info"],
        default_params=cfg["default_params"],
    )
# curl http://localhost:1234/v1/chat/completions \
#   -H "Content-Type: application/json" \
#   -d '{
#     "model": "qwen3-32b-mlx",
#     "messages": [
#       { "role": "system", "content": "Always answer in rhymes. Today is Thursday" },
#       { "role": "user", "content": "What day is it today?" }
#     ],
#     "temperature": 0.7,
#     "max_tokens": -1,
#     "stream": false
# }'