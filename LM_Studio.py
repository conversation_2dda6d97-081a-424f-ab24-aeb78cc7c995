from autogen_core.models import ModelInfo, ModelFamily, ChatCompletionClient
from autogen_core.models._types import CreateResult, SystemMessage, UserMessage, AssistantMessage, FunctionExecutionResultMessage, RequestUsage
from autogen_core._types import FunctionCall
from autogen_core.tools._base import Tool, ToolSchema
from autogen_core._cancellation_token import CancellationToken
from typing import Dict, Any, List, Sequence, Union, Mapping, Optional
import aiohttp
from pydantic import BaseModel

class LMStudioChatCompletionClient(ChatCompletionClient):
    def __init__(
        self,
        model: str,
        host: str = "http://localhost:1234/v1",
        model_info: ModelInfo = None,
        default_params: Dict[str, Any] = None,
    ):
        self.model = model
        self.host = host
        self._model_info = model_info or ModelInfo()
        self.default_params = default_params or {}

    @property
    def model_info(self) -> ModelInfo:
        return self._model_info

    @property
    def capabilities(self) -> ModelInfo:
        return self._model_info

    def _convert_messages(self, messages: Sequence[Union[SystemMessage, UserMessage, AssistantMessage, FunctionExecutionResultMessage]]) -> List[Dict[str, Any]]:
        """将AutoGen消息格式转换为OpenAI API格式"""
        converted = []
        for msg in messages:
            if isinstance(msg, SystemMessage):
                converted.append({
                    "role": "system",
                    "content": msg.content
                })
            elif isinstance(msg, UserMessage):
                converted.append({
                    "role": "user",
                    "content": msg.content
                })
            elif isinstance(msg, AssistantMessage):
                # 处理助手消息，可能包含工具调用
                if hasattr(msg, 'content') and msg.content:
                    if isinstance(msg.content, list):
                        # 如果内容是FunctionCall列表，转换为工具调用格式
                        tool_calls = []
                        for item in msg.content:
                            if hasattr(item, 'id') and hasattr(item, 'name') and hasattr(item, 'arguments'):
                                # 这是一个FunctionCall对象
                                tool_calls.append({
                                    "id": item.id,
                                    "type": "function",
                                    "function": {
                                        "name": item.name,
                                        "arguments": item.arguments
                                    }
                                })

                        if tool_calls:
                            converted.append({
                                "role": "assistant",
                                "content": None,
                                "tool_calls": tool_calls
                            })
                        else:
                            # 如果不是工具调用，转换为字符串
                            converted.append({
                                "role": "assistant",
                                "content": str(msg.content)
                            })
                    else:
                        # 普通文本内容
                        converted.append({
                            "role": "assistant",
                            "content": str(msg.content)
                        })
                else:
                    converted.append({
                        "role": "assistant",
                        "content": ""
                    })
            elif hasattr(msg, 'call_id'):
                # 这是一个函数执行结果消息（可能是FunctionExecutionResult）
                converted.append({
                    "role": "tool",
                    "content": str(msg.content),
                    "tool_call_id": msg.call_id
                })
            else:
                # 处理其他消息类型
                content = str(msg.content) if hasattr(msg, 'content') else str(msg)
                converted.append({
                    "role": "user",
                    "content": content
                })
        return converted

    def _convert_tools(self, tools: Sequence[Union[Tool, ToolSchema]]) -> List[Dict[str, Any]]:
        """将AutoGen工具格式转换为OpenAI API格式"""
        converted_tools = []
        for tool in tools:
            if hasattr(tool, 'schema'):
                # 这是一个Tool对象
                schema = tool.schema
                converted_tools.append({
                    "type": "function",
                    "function": {
                        "name": schema.get("name", ""),
                        "description": schema.get("description", ""),
                        "parameters": schema.get("parameters", {})
                    }
                })
            elif isinstance(tool, dict):
                # 这是一个ToolSchema字典
                converted_tools.append({
                    "type": "function",
                    "function": {
                        "name": tool.get("name", ""),
                        "description": tool.get("description", ""),
                        "parameters": tool.get("parameters", {})
                    }
                })
        return converted_tools

    async def create(
        self,
        messages: Sequence[Union[SystemMessage, UserMessage, AssistantMessage, FunctionExecutionResultMessage]],
        *,
        tools: Sequence[Union[Tool, ToolSchema]] = [],
        json_output: Union[bool, type[BaseModel], None] = None,
        extra_create_args: Mapping[str, Any] = {},
        cancellation_token: Optional[CancellationToken] = None
    ) -> CreateResult:
        """异步创建聊天完成"""
        # 合并默认参数和传入参数
        final_params = {**self.default_params, **extra_create_args}

        # 转换消息格式
        api_messages = self._convert_messages(messages)

        # 构建基础payload
        payload = {
            "model": self.model,
            "messages": api_messages,
            **final_params
        }

        # 如果有工具，添加到payload中
        if tools:
            converted_tools = self._convert_tools(tools)
            if converted_tools:
                payload["tools"] = converted_tools
                payload["tool_choice"] = "auto"  # 让模型自动决定是否使用工具

        async with aiohttp.ClientSession() as session:
            async with session.post(
                f"{self.host}/chat/completions",
                headers={"Content-Type": "application/json"},
                json=payload
            ) as response:
                if response.status != 200:
                    error_text = await response.text()
                    raise ValueError(f"LM Studio API 请求失败: {error_text}")

                result = await response.json()

                # 处理响应
                choice = result["choices"][0]
                message = choice["message"]

                # 创建使用情况对象
                usage_data = result.get("usage", {})
                usage = RequestUsage(
                    prompt_tokens=usage_data.get("prompt_tokens", 0),
                    completion_tokens=usage_data.get("completion_tokens", 0)
                )

                # 检查是否有工具调用
                if "tool_calls" in message and message["tool_calls"]:
                    # 处理工具调用
                    tool_calls = []
                    for tool_call in message["tool_calls"]:
                        if tool_call["type"] == "function":
                            func = tool_call["function"]
                            tool_calls.append(FunctionCall(
                                id=tool_call["id"],
                                arguments=func["arguments"],
                                name=func["name"]
                            ))

                    return CreateResult(
                        content=tool_calls,
                        usage=usage,
                        finish_reason="function_calls",  # AutoGen期望的值
                        cached=False,
                        logprobs=None
                    )
                else:
                    # 普通文本响应
                    content = message.get("content", "")
                    return CreateResult(
                        content=content,
                        usage=usage,
                        finish_reason=choice.get("finish_reason", "stop"),
                        cached=False,
                        logprobs=None
                    )

    async def create_stream(self, messages, **kwargs):
        """流式创建聊天完成 - 简单实现"""
        result = await self.create(messages, **kwargs)
        yield result

    async def close(self):
        """关闭客户端"""
        pass

    def actual_usage(self):
        """返回实际使用情况"""
        return {}

    def total_usage(self):
        """返回总使用情况"""
        return {}

    def count_tokens(self, messages, tools=None):
        """计算token数量 - 简单估算"""
        total = 0
        for msg in messages:
            if hasattr(msg, 'content'):
                total += len(str(msg.content).split()) * 1.3  # 简单估算
        return int(total)

    def remaining_tokens(self, messages, tools=None):
        """剩余token数量"""
        return 4096 - self.count_tokens(messages, tools)

LMSTUDIO_HOST = "http://localhost:1234/v1"

# LM Studio 模型注册表
LMSTUDIO_MODEL_REGISTRY = {
    "qwen3-32b-mlx": {
        "aliases": ["qwen3-32b", "qwen3-32b-mlx", "qwen3"],
        "model_info": ModelInfo(
            vision=False,
            function_calling=True,
            structured_output=True,
            json_output=True,
            family=ModelFamily.UNKNOWN,
        ),
        "default_params": {"temperature": 0.7, "max_tokens": 2048},
    },
    "qwen3-235b-a22b": {
        "aliases": ["qwen3-235b", "qwen3-235b-a22b", "qwen3-large"],
        "model_info": ModelInfo(
            vision=False,
            function_calling=True,
            structured_output=True,
            json_output=True,
            family=ModelFamily.UNKNOWN,
        ),
        "default_params": {"temperature": 0.5, "max_tokens": 4096},
    },
    "qwen2.5-vl-72b-instruct": {
        "aliases": ["qwen2.5-vl", "qwen2.5-vl-72b", "qwen-vision"],
        "model_info": ModelInfo(
            vision=True,  # 这是视觉模型
            function_calling=True,
            structured_output=True,
            json_output=True,
            family=ModelFamily.UNKNOWN,
        ),
        "default_params": {"temperature": 0.6, "max_tokens": 3072},
    },
    "text-embedding-nomic-embed-text-v1.5": {
        "aliases": ["nomic-embed", "embedding", "text-embedding"],
        "model_info": ModelInfo(
            vision=False,
            function_calling=False,  # 嵌入模型通常不支持函数调用
            structured_output=False,
            json_output=False,
            family=ModelFamily.UNKNOWN,
        ),
        "default_params": {"temperature": 0.0, "max_tokens": 512},
    },
    # 保留原有的示例模型（如果需要）
    "TheBloke/Llama-2-7B-Chat-GGUF": {
        "aliases": ["llama2-7b", "llama2"],
        "model_info": ModelInfo(
            vision=False,
            function_calling=True,
            structured_output=False,
            json_output=False,
            family=ModelFamily.UNKNOWN,
        ),
        "default_params": {"temperature": 0.6, "max_tokens": 1024},
    },
}

# 创建别名反查表
_lmstudio_alias_to_tag = {
    alias: tag
    for tag, config in LMSTUDIO_MODEL_REGISTRY.items()
    for alias in config.get("aliases", []) + [tag]
}

def get_lmstudio_model_config(name: str) -> dict:
    actual_tag = _lmstudio_alias_to_tag.get(name)
    if not actual_tag:
        raise ValueError(f"LM Studio 模型别名 '{name}' 未找到，请确认注册！")
    return LMSTUDIO_MODEL_REGISTRY[actual_tag]

def get_lmstudio_client(name: str) -> LMStudioChatCompletionClient:
    cfg = get_lmstudio_model_config(name)
    return LMStudioChatCompletionClient(
        model=_lmstudio_alias_to_tag[name],  # 实际 LM Studio 加载的模型名称
        host=LMSTUDIO_HOST,
        model_info=cfg["model_info"],
        default_params=cfg["default_params"],
    )
# curl http://localhost:1234/v1/chat/completions \
#   -H "Content-Type: application/json" \
#   -d '{
#     "model": "qwen3-32b-mlx",
#     "messages": [
#       { "role": "system", "content": "Always answer in rhymes. Today is Thursday" },
#       { "role": "user", "content": "What day is it today?" }
#     ],
#     "temperature": 0.7,
#     "max_tokens": -1,
#     "stream": false
# }'