from autogen_core.models import ModelInfo, ModelFamily
from typing import Dict, Any
import requests
import json

class LMStudioChatCompletionClient:
    def __init__(
        self,
        model: str,
        host: str = "http://localhost:1234/v1",
        model_info: ModelInfo = None,
        default_params: Dict[str, Any] = None,
    ):
        self.model = model
        self.host = host
        self.model_info = model_info or ModelInfo()
        self.default_params = default_params or {}

    def create_chat_completion(self, messages, **params):
        # 合并默认参数和传入参数
        final_params = {**self.default_params, **params}

        payload = {
            "model": self.model,
            "messages": messages,
            **final_params
        }

        response = requests.post(
            f"{self.host}/chat/completions",
            headers={"Content-Type": "application/json"},
            data=json.dumps(payload)
        )

        if response.status_code != 200:
            raise ValueError(f"LM Studio API 请求失败: {response.text}")

        return response.json()
    LMSTUDIO_HOST = "http://localhost:1234/v1"

# LM Studio 模型注册表
LMSTUDIO_MODEL_REGISTRY = {
    "Tqwen3-32b-mlx": {
        "aliases": ["qwen3-32b", "qwen3-32b-mlxl"],
        "model_info": ModelInfo(
            vision=False,
            function_calling=True,
            structured_output=True,
            json_output=True,
            family=ModelFamily.UNKNOWN,
        ),
        "default_params": {"temperature": 0.7, "max_tokens": 512},
    },
    "TheBloke/Llama-2-7B-Chat-GGUF": {
        "aliases": ["llama2-7b", "llama2"],
        "model_info": ModelInfo(
            vision=False,
            function_calling=True,
            structured_output=False,
            json_output=False,
            family=ModelFamily.UNKNOWN,
        ),
        "default_params": {"temperature": 0.6, "max_tokens": 1024},
    },
}

# 创建别名反查表
_lmstudio_alias_to_tag = {
    alias: tag
    for tag, config in LMSTUDIO_MODEL_REGISTRY.items()
    for alias in config.get("aliases", []) + [tag]
}

def get_lmstudio_model_config(name: str) -> dict:
    actual_tag = _lmstudio_alias_to_tag.get(name)
    if not actual_tag:
        raise ValueError(f"LM Studio 模型别名 '{name}' 未找到，请确认注册！")
    return LMSTUDIO_MODEL_REGISTRY[actual_tag]

def get_lmstudio_client(name: str) -> LMStudioChatCompletionClient:
    cfg = get_lmstudio_model_config(name)
    return LMStudioChatCompletionClient(
        model=_lmstudio_alias_to_tag[name],  # 实际 LM Studio 加载的模型名称
        host=LMSTUDIO_HOST,
        model_info=cfg["model_info"],
        default_params=cfg["default_params"],
    )
# curl http://localhost:1234/v1/chat/completions \
#   -H "Content-Type: application/json" \
#   -d '{
#     "model": "qwen3-32b-mlx",
#     "messages": [
#       { "role": "system", "content": "Always answer in rhymes. Today is Thursday" },
#       { "role": "user", "content": "What day is it today?" }
#     ],
#     "temperature": 0.7,
#     "max_tokens": -1,
#     "stream": false
# }'