#!/usr/bin/env python3
"""
LKS32MC08x智能文档理解系统演示
"""
import asyncio
import sys
import os
sys.path.append(os.path.join(os.path.dirname(__file__), 'document_intelligence'))
from document_intelligence.main import DocumentIntelligenceSystem

async def demo():
    """系统演示"""
    print("🚀 LKS32MC08x智能文档理解系统演示")
    print("=" * 60)

    # 初始化系统
    system = DocumentIntelligenceSystem()
    await system.initialize()

    # 演示场景
    demos = [
        {
            "title": "📋 文档查询",
            "description": "查询寄存器信息",
            "query": "SYS_AFE_REG1寄存器的功能",
            "method": "search_document"
        },
        {
            "title": "💻 代码生成",
            "description": "生成GPIO初始化代码",
            "query": "初始化GPIO，配置PA0为输出，PA1为输入并使能上拉",
            "method": "generate_code"
        },
        {
            "title": "🔬 技术分析",
            "description": "分析ADC性能优化",
            "query": "如何优化ADC采样精度和速度？",
            "method": "analyze_technical"
        },
        {
            "title": "🏗️ 系统设计",
            "description": "设计电机控制系统",
            "query": "设计三相无刷电机控制系统",
            "method": "design_system"
        },
        {
            "title": "🎯 智能协调",
            "description": "复杂问题协调处理",
            "query": "如何实现一个完整的工业控制器，包括传感器接口、电机控制和通信功能？",
            "method": "ask"
        }
    ]

    for i, demo in enumerate(demos, 1):
        print(f"\n{demo['title']} (演示 {i}/5)")
        print(f"📝 {demo['description']}")
        print(f"❓ 查询: {demo['query']}")
        print("-" * 50)

        try:
            # 根据方法调用相应功能
            if demo['method'] == 'search_document':
                response = await system.search_document(demo['query'])
            elif demo['method'] == 'generate_code':
                response = await system.generate_code(demo['query'])
            elif demo['method'] == 'analyze_technical':
                response = await system.analyze_technical(demo['query'])
            elif demo['method'] == 'design_system':
                response = await system.design_system(demo['query'])
            else:
                response = await system.ask(demo['query'])

            print(f"💡 回答:\n{response}")

        except Exception as e:
            print(f"❌ 错误: {e}")

        print("\n" + "=" * 60)

        # 短暂延时
        await asyncio.sleep(1)

    # 显示系统统计
    print("\n📊 演示完成 - 系统统计:")
    status = system.get_system_status()

    print(f"🤖 智能体状态:")
    for agent_name, agent_status in status["agents"].items():
        stats = agent_status.get("stats", {})
        print(f"  • {agent_name}: 处理 {stats.get('requests_handled', 0)} 个请求")

    print(f"\n🎯 协调员统计:")
    coord_stats = status["coordinator"]["stats"]
    print(f"  • 总请求: {coord_stats.get('requests_handled', 0)}")
    print(f"  • 成功率: {coord_stats.get('success_rate', 0):.1%}")

    print("\n✅ 演示结束！")

if __name__ == "__main__":
    asyncio.run(demo())
