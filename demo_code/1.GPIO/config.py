#!/usr/bin/env python3
"""
AutoGen智能体配置文件
用于嵌入式开发工作流程
"""

import os
from typing import Dict, List

class EmbeddedDevConfig:
    """嵌入式开发配置类"""
    
    def __init__(self):
        self.setup_config()
    
    def setup_config(self):
        """设置配置参数"""
        
        # AutoGen LLM配置
        self.llm_config = {
            "config_list": [
                {
                    "model": "gpt-4",
                    "api_key": os.getenv("OPENAI_API_KEY", "your-api-key"),
                    "base_url": os.getenv("OPENAI_BASE_URL", "https://api.openai.com/v1")
                }
            ],
            "temperature": 0.1,
            "timeout": 120,
        }
        
        # 芯片支持列表
        self.supported_chips = {
            "LKS32MC081": {
                "family": "LKS32MC08x",
                "core": "ARM Cortex-M0+",
                "flash": "64KB",
                "ram": "8KB",
                "gpio_ports": ["GPIO0", "GPIO1", "GPIO2"],
                "peripherals": ["ADC", "PWM", "UART", "SPI", "I2C", "CAN"]
            },
            "LKS32MC084": {
                "family": "LKS32MC08x", 
                "core": "ARM Cortex-M0+",
                "flash": "128KB",
                "ram": "16KB",
                "gpio_ports": ["GPIO0", "GPIO1", "GPIO2", "GPIO3"],
                "peripherals": ["ADC", "PWM", "UART", "SPI", "I2C", "CAN", "HALL"]
            }
        }
        
        # 代码模板路径
        self.template_paths = {
            "gpio": "templates/gpio_template.c",
            "adc": "templates/adc_template.c", 
            "pwm": "templates/pwm_template.c",
            "uart": "templates/uart_template.c",
            "main": "templates/main_template.c"
        }
        
        # 输出目录配置
        self.output_dirs = {
            "source": "generated/Source",
            "include": "generated/Include", 
            "docs": "generated/Documentation",
            "tests": "generated/Tests"
        }
        
        # 编码规范配置
        self.coding_standards = {
            "max_line_length": 100,
            "indent_size": 4,
            "use_tabs": False,
            "comment_style": "/* */",
            "naming_convention": {
                "functions": "PascalCase",
                "variables": "camelCase", 
                "constants": "UPPER_CASE",
                "types": "PascalCase_t"
            }
        }
        
        # 测试配置
        self.test_config = {
            "unit_test_framework": "Unity",
            "coverage_threshold": 80,
            "static_analysis_tools": ["PC-lint", "Cppcheck"],
            "test_categories": [
                "functional",
                "boundary", 
                "performance",
                "reliability"
            ]
        }

    def get_chip_config(self, chip_model: str) -> Dict:
        """获取芯片配置信息"""
        return self.supported_chips.get(chip_model, {})
    
    def get_template_path(self, template_type: str) -> str:
        """获取模板文件路径"""
        return self.template_paths.get(template_type, "")
    
    def create_output_dirs(self):
        """创建输出目录"""
        for dir_path in self.output_dirs.values():
            os.makedirs(dir_path, exist_ok=True)

# 全局配置实例
config = EmbeddedDevConfig()

# 智能体角色定义
AGENT_ROLES = {
    "requirement_analyst": {
        "name": "需求分析师",
        "expertise": ["需求分析", "技术规格", "可行性评估"],
        "responsibilities": [
            "解析用户需求",
            "提取技术指标", 
            "评估实现难度",
            "生成功能规格"
        ]
    },
    
    "datasheet_expert": {
        "name": "芯片专家",
        "expertise": ["芯片手册", "寄存器配置", "硬件设计"],
        "responsibilities": [
            "解析芯片手册",
            "提供配置建议",
            "优化硬件资源",
            "解决技术问题"
        ]
    },
    
    "code_generator": {
        "name": "代码生成器",
        "expertise": ["C语言编程", "嵌入式开发", "代码优化"],
        "responsibilities": [
            "生成高质量代码",
            "遵循编码规范",
            "实现模块化设计",
            "添加详细注释"
        ]
    },
    
    "test_validator": {
        "name": "测试工程师", 
        "expertise": ["软件测试", "代码审查", "质量保证"],
        "responsibilities": [
            "设计测试用例",
            "执行代码审查",
            "性能分析",
            "生成测试报告"
        ]
    },
    
    "project_manager": {
        "name": "项目经理",
        "expertise": ["项目管理", "团队协调", "进度控制"],
        "responsibilities": [
            "制定开发计划",
            "协调团队工作",
            "跟踪项目进度", 
            "风险管理"
        ]
    }
}

# 工作流程定义
WORKFLOW_STEPS = [
    {
        "step": 1,
        "name": "需求分析",
        "agent": "requirement_analyst",
        "inputs": ["用户需求描述"],
        "outputs": ["技术规格文档", "功能清单", "风险评估"]
    },
    {
        "step": 2, 
        "name": "芯片配置",
        "agent": "datasheet_expert",
        "inputs": ["技术规格文档", "芯片型号"],
        "outputs": ["硬件配置方案", "寄存器配置", "引脚分配"]
    },
    {
        "step": 3,
        "name": "代码生成", 
        "agent": "code_generator",
        "inputs": ["硬件配置方案", "功能清单"],
        "outputs": ["源代码文件", "头文件", "配置文件"]
    },
    {
        "step": 4,
        "name": "测试验证",
        "agent": "test_validator", 
        "inputs": ["源代码文件"],
        "outputs": ["测试用例", "测试报告", "代码审查报告"]
    },
    {
        "step": 5,
        "name": "项目管理",
        "agent": "project_manager",
        "inputs": ["所有输出文档"],
        "outputs": ["项目报告", "部署指南", "维护文档"]
    }
]

# 常用提示词模板
PROMPT_TEMPLATES = {
    "requirement_analysis": """
    请分析以下嵌入式开发需求：
    
    需求描述：{requirement}
    目标芯片：{chip_model}
    
    请提供：
    1. 功能分解和技术指标
    2. 硬件资源需求分析
    3. 实现难度评估
    4. 潜在风险识别
    5. 开发时间估算
    
    请用中文回复，格式清晰。
    """,
    
    "code_generation": """
    请基于以下规格生成嵌入式C代码：
    
    技术规格：{specification}
    芯片配置：{chip_config}
    
    代码要求：
    1. 遵循MISRA C标准
    2. 包含详细中文注释
    3. 模块化设计
    4. 错误处理机制
    5. 性能优化
    
    请生成完整的.c和.h文件。
    """,
    
    "test_design": """
    请为以下代码设计全面的测试方案：
    
    代码功能：{functionality}
    测试范围：{test_scope}
    
    测试要求：
    1. 功能测试用例
    2. 边界条件测试
    3. 异常处理测试
    4. 性能基准测试
    5. 长期稳定性测试
    
    请提供具体的测试步骤和预期结果。
    """
}

def get_prompt_template(template_name: str, **kwargs) -> str:
    """获取格式化的提示词模板"""
    template = PROMPT_TEMPLATES.get(template_name, "")
    return template.format(**kwargs)
