lks32mc08x_periph_driver V2.3
对V2.2的（lks32mc08x_exti.c，void EXTI_Trigger_Config(GPIO_TypeDef *GPIOx, uint32_t GPIO_PinSource, uint16_t EXTI_Trigger)中关于EXTI_CR1寄存器选择bug修复。

lks32mc08x_periph_driver V2.2
对V2.1的（lks32mc08x_sys.c内增加电源检查函数，实际使用在void Clock_Init(void)和void PWRDN_IRQHandler(void)函数内）这些内容做更新，之前的有问题。

lks32mc08x_periph_driver V2.1
lks32mc08x_sys.c内增加电源检查函数，实际使用在void Clock_Init(void)和void PWRDN_IRQHandler(void)函数内。

lks32mc08x_periph_driver V2.0
增加定时器初始化使能接口，FuncState EN;      /*定时器模块使能*/（lks32mc08x.tim.h）。

lks32mc08x_periph_driver V1.9
将void SYS_SoftResetModule(uint32_t nModule)函数内延时函数去掉，防止在中断内执行该函数时间过长。

lks32mc08x_periph_driver V1.8
修正u8 CMP_GetIRQFlag(u8 CMPx)函数返回值，返回0和1反了，现以修正。

lks32mc08x_periph_driver V1.7
增加ADC库函数内void ADC_StructInit(ADC_InitTypeDef* ADC_InitStruct)函数ADC_InitStruct->ADC_CLK_DIV = 0;对ADC_InitStruct->ADC_CLK_DIV进行初始化。

lks32mc08x_periph_driver V1.6
1、更新库函数注释。

lks32mc08x_periph_driver V1.5
1、修改UART初始化函数电平取反接口，RX和TX位操作反了。2、修改CMP0的IP通道宏定义，之前宏定义有误。

lks32mc08x_periph_driver V1.4
修改DSP的头文件商和除数定义数据类型

lks32mc08x_periph_driver V1.3
lks32mc08x_adc.c内增加ADC时钟分频接口。

lks32mc08x_periph_driver V1.2
修正lks32mc08x_adc.c库函数内一倍增益的校正参数读取地址（之前的有误），增加lks32mc08x_adc.c库函数可以配置ADC基准电压接口。

lks32mc08x_periph_driver V1.1
修正lks32mc08x_iwdg.c内喂狗函数，没修正前看门狗喂狗后下一次复位时间一直为最大64S，因为SYS_WDT_CLR寄存器为只写寄存器，无法读取。

lks32mc08x_periph_driver V1.0
1、（1）lks32mc08x_uart.c文件波特率计算的系统时钟宏定义从原来的hardware_config.h的
#define MCU_MCLK          (96000000LL)       /* PWM模块运行主频 */
改为lks32mc08x_uart.h的 #define UART_MCU_MCLK       (96000000LL)       /* PWM模块运行主频 */
（2）UARTx->CTRL的bit7位是没有的，给配置bit7的语句删除了。
2、lks32mc08x_uart.c对几个函数添加了SYS_WR_PROTECT = 0x7a83; /*开启寄存器写使能*/  和SYS_WR_PROTECT = 0x0;关闭写使能语句。
3、更改部分头文件引用。
