<?xml version="1.0" encoding="UTF-8" standalone="no" ?>
<Project xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:noNamespaceSchemaLocation="project_projx.xsd">

  <SchemaVersion>2.1</SchemaVersion>

  <Header>### uVision Project, (C) Keil Software</Header>

  <Targets>
    <Target>
      <TargetName>LK_StdPeriph_GPIO</TargetName>
      <ToolsetNumber>0x4</ToolsetNumber>
      <ToolsetName>ARM-ADS</ToolsetName>
      <pCCUsed>5060960::V5.06 update 7 (build 960)::.\ARMCC</pCCUsed>
      <uAC6>0</uAC6>
      <TargetOption>
        <TargetCommonOption>
          <Device>LKS32MC087M6S8</Device>
          <Vendor>Linko</Vendor>
          <PackID>Linko.LKS08x.1.1.4</PackID>
          <PackURL>https://www.lksmcu.com/index.php/LKS08Series</PackURL>
          <Cpu>IRAM(0x20000000,0x2000) IROM(0x00000000,0x8000) CPUTYPE("Cortex-M0") CLOCK(12000000) ELITTLE</Cpu>
          <FlashUtilSpec></FlashUtilSpec>
          <StartupFile></StartupFile>
          <FlashDriverDll>UL2CM3(-S0 -C0 -P0 -********** -FC1000 -FN1 -FF0LKS32MC08x -FS00 -FL08000 -FP0($$Device:LKS32MC087M6S8$Flash\LKS32MC08x.FLM))</FlashDriverDll>
          <DeviceId>0</DeviceId>
          <RegisterFile>$$Device:LKS32MC087M6S8$Device\Include\lks32mc08x.h</RegisterFile>
          <MemoryEnv></MemoryEnv>
          <Cmp></Cmp>
          <Asm></Asm>
          <Linker></Linker>
          <OHString></OHString>
          <InfinionOptionDll></InfinionOptionDll>
          <SLE66CMisc></SLE66CMisc>
          <SLE66AMisc></SLE66AMisc>
          <SLE66LinkerMisc></SLE66LinkerMisc>
          <SFDFile>$$Device:LKS32MC087M6S8$SVD\LKS32MC08x.svd</SFDFile>
          <bCustSvd>0</bCustSvd>
          <UseEnv>0</UseEnv>
          <BinPath></BinPath>
          <IncludePath></IncludePath>
          <LibPath></LibPath>
          <RegisterFilePath></RegisterFilePath>
          <DBRegisterFilePath></DBRegisterFilePath>
          <TargetStatus>
            <Error>0</Error>
            <ExitCodeStop>0</ExitCodeStop>
            <ButtonStop>0</ButtonStop>
            <NotGenerated>0</NotGenerated>
            <InvalidFlash>1</InvalidFlash>
          </TargetStatus>
          <OutputDirectory>.\Objects\</OutputDirectory>
          <OutputName>LK_StdPeriph_GPIO</OutputName>
          <CreateExecutable>1</CreateExecutable>
          <CreateLib>0</CreateLib>
          <CreateHexFile>1</CreateHexFile>
          <DebugInformation>1</DebugInformation>
          <BrowseInformation>1</BrowseInformation>
          <ListingPath>.\Listings\</ListingPath>
          <HexFormatSelection>1</HexFormatSelection>
          <Merge32K>0</Merge32K>
          <CreateBatchFile>1</CreateBatchFile>
          <BeforeCompile>
            <RunUserProg1>0</RunUserProg1>
            <RunUserProg2>0</RunUserProg2>
            <UserProg1Name></UserProg1Name>
            <UserProg2Name></UserProg2Name>
            <UserProg1Dos16Mode>0</UserProg1Dos16Mode>
            <UserProg2Dos16Mode>0</UserProg2Dos16Mode>
            <nStopU1X>0</nStopU1X>
            <nStopU2X>0</nStopU2X>
          </BeforeCompile>
          <BeforeMake>
            <RunUserProg1>0</RunUserProg1>
            <RunUserProg2>0</RunUserProg2>
            <UserProg1Name></UserProg1Name>
            <UserProg2Name></UserProg2Name>
            <UserProg1Dos16Mode>0</UserProg1Dos16Mode>
            <UserProg2Dos16Mode>0</UserProg2Dos16Mode>
            <nStopB1X>0</nStopB1X>
            <nStopB2X>0</nStopB2X>
          </BeforeMake>
          <AfterMake>
            <RunUserProg1>0</RunUserProg1>
            <RunUserProg2>0</RunUserProg2>
            <UserProg1Name></UserProg1Name>
            <UserProg2Name></UserProg2Name>
            <UserProg1Dos16Mode>0</UserProg1Dos16Mode>
            <UserProg2Dos16Mode>0</UserProg2Dos16Mode>
            <nStopA1X>0</nStopA1X>
            <nStopA2X>0</nStopA2X>
          </AfterMake>
          <SelectedForBatchBuild>0</SelectedForBatchBuild>
          <SVCSIdString></SVCSIdString>
        </TargetCommonOption>
        <CommonProperty>
          <UseCPPCompiler>0</UseCPPCompiler>
          <RVCTCodeConst>0</RVCTCodeConst>
          <RVCTZI>0</RVCTZI>
          <RVCTOtherData>0</RVCTOtherData>
          <ModuleSelection>0</ModuleSelection>
          <IncludeInBuild>1</IncludeInBuild>
          <AlwaysBuild>0</AlwaysBuild>
          <GenerateAssemblyFile>0</GenerateAssemblyFile>
          <AssembleAssemblyFile>0</AssembleAssemblyFile>
          <PublicsOnly>0</PublicsOnly>
          <StopOnExitCode>3</StopOnExitCode>
          <CustomArgument></CustomArgument>
          <IncludeLibraryModules></IncludeLibraryModules>
          <ComprImg>1</ComprImg>
        </CommonProperty>
        <DllOption>
          <SimDllName>SARMCM3.DLL</SimDllName>
          <SimDllArguments>  </SimDllArguments>
          <SimDlgDll>DARMCM1.DLL</SimDlgDll>
          <SimDlgDllArguments>-pCM0</SimDlgDllArguments>
          <TargetDllName>SARMCM3.DLL</TargetDllName>
          <TargetDllArguments> </TargetDllArguments>
          <TargetDlgDll>TARMCM1.DLL</TargetDlgDll>
          <TargetDlgDllArguments>-pCM0</TargetDlgDllArguments>
        </DllOption>
        <DebugOption>
          <OPTHX>
            <HexSelection>1</HexSelection>
            <HexRangeLowAddress>0</HexRangeLowAddress>
            <HexRangeHighAddress>0</HexRangeHighAddress>
            <HexOffset>0</HexOffset>
            <Oh166RecLen>16</Oh166RecLen>
          </OPTHX>
        </DebugOption>
        <Utilities>
          <Flash1>
            <UseTargetDll>1</UseTargetDll>
            <UseExternalTool>0</UseExternalTool>
            <RunIndependent>0</RunIndependent>
            <UpdateFlashBeforeDebugging>1</UpdateFlashBeforeDebugging>
            <Capability>1</Capability>
            <DriverSelection>4096</DriverSelection>
          </Flash1>
          <bUseTDR>1</bUseTDR>
          <Flash2>BIN\UL2CM3.DLL</Flash2>
          <Flash3>"" ()</Flash3>
          <Flash4></Flash4>
          <pFcarmOut></pFcarmOut>
          <pFcarmGrp></pFcarmGrp>
          <pFcArmRoot></pFcArmRoot>
          <FcArmLst>0</FcArmLst>
        </Utilities>
        <TargetArmAds>
          <ArmAdsMisc>
            <GenerateListings>0</GenerateListings>
            <asHll>1</asHll>
            <asAsm>1</asAsm>
            <asMacX>1</asMacX>
            <asSyms>1</asSyms>
            <asFals>1</asFals>
            <asDbgD>1</asDbgD>
            <asForm>1</asForm>
            <ldLst>0</ldLst>
            <ldmm>1</ldmm>
            <ldXref>1</ldXref>
            <BigEnd>0</BigEnd>
            <AdsALst>1</AdsALst>
            <AdsACrf>1</AdsACrf>
            <AdsANop>0</AdsANop>
            <AdsANot>0</AdsANot>
            <AdsLLst>1</AdsLLst>
            <AdsLmap>1</AdsLmap>
            <AdsLcgr>1</AdsLcgr>
            <AdsLsym>1</AdsLsym>
            <AdsLszi>1</AdsLszi>
            <AdsLtoi>1</AdsLtoi>
            <AdsLsun>1</AdsLsun>
            <AdsLven>1</AdsLven>
            <AdsLsxf>1</AdsLsxf>
            <RvctClst>0</RvctClst>
            <GenPPlst>0</GenPPlst>
            <AdsCpuType>"Cortex-M0"</AdsCpuType>
            <RvctDeviceName></RvctDeviceName>
            <mOS>0</mOS>
            <uocRom>0</uocRom>
            <uocRam>0</uocRam>
            <hadIROM>1</hadIROM>
            <hadIRAM>1</hadIRAM>
            <hadXRAM>0</hadXRAM>
            <uocXRam>0</uocXRam>
            <RvdsVP>0</RvdsVP>
            <RvdsMve>0</RvdsMve>
            <RvdsCdeCp>0</RvdsCdeCp>
            <hadIRAM2>0</hadIRAM2>
            <hadIROM2>0</hadIROM2>
            <StupSel>8</StupSel>
            <useUlib>1</useUlib>
            <EndSel>0</EndSel>
            <uLtcg>0</uLtcg>
            <nSecure>0</nSecure>
            <RoSelD>3</RoSelD>
            <RwSelD>3</RwSelD>
            <CodeSel>0</CodeSel>
            <OptFeed>0</OptFeed>
            <NoZi1>0</NoZi1>
            <NoZi2>0</NoZi2>
            <NoZi3>0</NoZi3>
            <NoZi4>0</NoZi4>
            <NoZi5>0</NoZi5>
            <Ro1Chk>0</Ro1Chk>
            <Ro2Chk>0</Ro2Chk>
            <Ro3Chk>0</Ro3Chk>
            <Ir1Chk>1</Ir1Chk>
            <Ir2Chk>0</Ir2Chk>
            <Ra1Chk>0</Ra1Chk>
            <Ra2Chk>0</Ra2Chk>
            <Ra3Chk>0</Ra3Chk>
            <Im1Chk>1</Im1Chk>
            <Im2Chk>0</Im2Chk>
            <OnChipMemories>
              <Ocm1>
                <Type>0</Type>
                <StartAddress>0x0</StartAddress>
                <Size>0x0</Size>
              </Ocm1>
              <Ocm2>
                <Type>0</Type>
                <StartAddress>0x0</StartAddress>
                <Size>0x0</Size>
              </Ocm2>
              <Ocm3>
                <Type>0</Type>
                <StartAddress>0x0</StartAddress>
                <Size>0x0</Size>
              </Ocm3>
              <Ocm4>
                <Type>0</Type>
                <StartAddress>0x0</StartAddress>
                <Size>0x0</Size>
              </Ocm4>
              <Ocm5>
                <Type>0</Type>
                <StartAddress>0x0</StartAddress>
                <Size>0x0</Size>
              </Ocm5>
              <Ocm6>
                <Type>0</Type>
                <StartAddress>0x0</StartAddress>
                <Size>0x0</Size>
              </Ocm6>
              <IRAM>
                <Type>0</Type>
                <StartAddress>0x20000000</StartAddress>
                <Size>0x2000</Size>
              </IRAM>
              <IROM>
                <Type>1</Type>
                <StartAddress>0x0</StartAddress>
                <Size>0x8000</Size>
              </IROM>
              <XRAM>
                <Type>0</Type>
                <StartAddress>0x0</StartAddress>
                <Size>0x0</Size>
              </XRAM>
              <OCR_RVCT1>
                <Type>1</Type>
                <StartAddress>0x0</StartAddress>
                <Size>0x0</Size>
              </OCR_RVCT1>
              <OCR_RVCT2>
                <Type>1</Type>
                <StartAddress>0x0</StartAddress>
                <Size>0x0</Size>
              </OCR_RVCT2>
              <OCR_RVCT3>
                <Type>1</Type>
                <StartAddress>0x0</StartAddress>
                <Size>0x0</Size>
              </OCR_RVCT3>
              <OCR_RVCT4>
                <Type>1</Type>
                <StartAddress>0x0</StartAddress>
                <Size>0x8000</Size>
              </OCR_RVCT4>
              <OCR_RVCT5>
                <Type>1</Type>
                <StartAddress>0x0</StartAddress>
                <Size>0x0</Size>
              </OCR_RVCT5>
              <OCR_RVCT6>
                <Type>0</Type>
                <StartAddress>0x0</StartAddress>
                <Size>0x0</Size>
              </OCR_RVCT6>
              <OCR_RVCT7>
                <Type>0</Type>
                <StartAddress>0x0</StartAddress>
                <Size>0x0</Size>
              </OCR_RVCT7>
              <OCR_RVCT8>
                <Type>0</Type>
                <StartAddress>0x0</StartAddress>
                <Size>0x0</Size>
              </OCR_RVCT8>
              <OCR_RVCT9>
                <Type>0</Type>
                <StartAddress>0x20000000</StartAddress>
                <Size>0x2000</Size>
              </OCR_RVCT9>
              <OCR_RVCT10>
                <Type>0</Type>
                <StartAddress>0x0</StartAddress>
                <Size>0x0</Size>
              </OCR_RVCT10>
            </OnChipMemories>
            <RvctStartVector></RvctStartVector>
          </ArmAdsMisc>
          <Cads>
            <interw>1</interw>
            <Optim>1</Optim>
            <oTime>0</oTime>
            <SplitLS>0</SplitLS>
            <OneElfS>1</OneElfS>
            <Strict>0</Strict>
            <EnumInt>0</EnumInt>
            <PlainCh>0</PlainCh>
            <Ropi>0</Ropi>
            <Rwpi>0</Rwpi>
            <wLevel>2</wLevel>
            <uThumb>0</uThumb>
            <uSurpInc>0</uSurpInc>
            <uC99>0</uC99>
            <uGnu>0</uGnu>
            <useXO>0</useXO>
            <v6Lang>3</v6Lang>
            <v6LangP>3</v6LangP>
            <vShortEn>1</vShortEn>
            <vShortWch>1</vShortWch>
            <v6Lto>0</v6Lto>
            <v6WtE>0</v6WtE>
            <v6Rtti>0</v6Rtti>
            <VariousControls>
              <MiscControls></MiscControls>
              <Define></Define>
              <Undefine></Undefine>
              <IncludePath>.\FreeMaster;.\Include;.\LKS32MC08x_Periph_Driver\Include;.\Source;.\USER\LED;.\USER\KEY;.\USER\Delay</IncludePath>
            </VariousControls>
          </Cads>
          <Aads>
            <interw>1</interw>
            <Ropi>0</Ropi>
            <Rwpi>0</Rwpi>
            <thumb>0</thumb>
            <SplitLS>0</SplitLS>
            <SwStkChk>0</SwStkChk>
            <NoWarn>0</NoWarn>
            <uSurpInc>0</uSurpInc>
            <useXO>0</useXO>
            <ClangAsOpt>4</ClangAsOpt>
            <VariousControls>
              <MiscControls></MiscControls>
              <Define></Define>
              <Undefine></Undefine>
              <IncludePath></IncludePath>
            </VariousControls>
          </Aads>
          <LDads>
            <umfTarg>1</umfTarg>
            <Ropi>0</Ropi>
            <Rwpi>0</Rwpi>
            <noStLib>0</noStLib>
            <RepFail>1</RepFail>
            <useFile>0</useFile>
            <TextAddressRange>0x00000000</TextAddressRange>
            <DataAddressRange>0x20000000</DataAddressRange>
            <pXoBase></pXoBase>
            <ScatterFile></ScatterFile>
            <IncludeLibs></IncludeLibs>
            <IncludeLibsPath></IncludeLibsPath>
            <Misc></Misc>
            <LinkerInputFile></LinkerInputFile>
            <DisabledWarnings></DisabledWarnings>
          </LDads>
        </TargetArmAds>
      </TargetOption>
      <Groups>
        <Group>
          <GroupName>Source_Code</GroupName>
          <Files>
            <File>
              <FileName>main.c</FileName>
              <FileType>1</FileType>
              <FilePath>.\Source\main.c</FilePath>
            </File>
          </Files>
        </Group>
        <Group>
          <GroupName>AppFunction</GroupName>
          <Files>
            <File>
              <FileName>hardware_init.c</FileName>
              <FileType>1</FileType>
              <FilePath>.\AppFunction\hardware_init.c</FilePath>
            </File>
            <File>
              <FileName>interrupt.c</FileName>
              <FileType>1</FileType>
              <FilePath>.\AppFunction\interrupt.c</FilePath>
            </File>
          </Files>
        </Group>
        <Group>
          <GroupName>Periph_Driver</GroupName>
          <Files>
            <File>
              <FileName>lks32mc08x_adc.c</FileName>
              <FileType>1</FileType>
              <FilePath>.\LKS32MC08x_Periph_Driver\Source\lks32mc08x_adc.c</FilePath>
            </File>
            <File>
              <FileName>lks32mc08x_can.c</FileName>
              <FileType>1</FileType>
              <FilePath>.\LKS32MC08x_Periph_Driver\Source\lks32mc08x_can.c</FilePath>
            </File>
            <File>
              <FileName>lks32mc08x_cmp.c</FileName>
              <FileType>1</FileType>
              <FilePath>.\LKS32MC08x_Periph_Driver\Source\lks32mc08x_cmp.c</FilePath>
            </File>
            <File>
              <FileName>lks32mc08x_dac.c</FileName>
              <FileType>1</FileType>
              <FilePath>.\LKS32MC08x_Periph_Driver\Source\lks32mc08x_dac.c</FilePath>
            </File>
            <File>
              <FileName>lks32mc08x_dma.c</FileName>
              <FileType>1</FileType>
              <FilePath>.\LKS32MC08x_Periph_Driver\Source\lks32mc08x_dma.c</FilePath>
            </File>
            <File>
              <FileName>lks32mc08x_dsp.c</FileName>
              <FileType>1</FileType>
              <FilePath>.\LKS32MC08x_Periph_Driver\Source\lks32mc08x_dsp.c</FilePath>
            </File>
            <File>
              <FileName>lks32mc08x_exti.c</FileName>
              <FileType>1</FileType>
              <FilePath>.\LKS32MC08x_Periph_Driver\Source\lks32mc08x_exti.c</FilePath>
            </File>
            <File>
              <FileName>lks32MC08x_flash.c</FileName>
              <FileType>1</FileType>
              <FilePath>.\LKS32MC08x_Periph_Driver\Source\lks32MC08x_flash.c</FilePath>
            </File>
            <File>
              <FileName>lks32mc08x_gpio.c</FileName>
              <FileType>1</FileType>
              <FilePath>.\LKS32MC08x_Periph_Driver\Source\lks32mc08x_gpio.c</FilePath>
            </File>
            <File>
              <FileName>lks32mc08x_hall.c</FileName>
              <FileType>1</FileType>
              <FilePath>.\LKS32MC08x_Periph_Driver\Source\lks32mc08x_hall.c</FilePath>
            </File>
            <File>
              <FileName>lks32mc08x_i2c.c</FileName>
              <FileType>1</FileType>
              <FilePath>.\LKS32MC08x_Periph_Driver\Source\lks32mc08x_i2c.c</FilePath>
            </File>
            <File>
              <FileName>lks32mc08x_iwdg.c</FileName>
              <FileType>1</FileType>
              <FilePath>.\LKS32MC08x_Periph_Driver\Source\lks32mc08x_iwdg.c</FilePath>
            </File>
            <File>
              <FileName>lks32mc08x_mcpwm.c</FileName>
              <FileType>1</FileType>
              <FilePath>.\LKS32MC08x_Periph_Driver\Source\lks32mc08x_mcpwm.c</FilePath>
            </File>
            <File>
              <FileName>lks32mc08x_opa.c</FileName>
              <FileType>1</FileType>
              <FilePath>.\LKS32MC08x_Periph_Driver\Source\lks32mc08x_opa.c</FilePath>
            </File>
            <File>
              <FileName>lks32mc08x_spi.c</FileName>
              <FileType>1</FileType>
              <FilePath>.\LKS32MC08x_Periph_Driver\Source\lks32mc08x_spi.c</FilePath>
            </File>
            <File>
              <FileName>lks32mc08x_sys.c</FileName>
              <FileType>1</FileType>
              <FilePath>.\LKS32MC08x_Periph_Driver\Source\lks32mc08x_sys.c</FilePath>
            </File>
            <File>
              <FileName>lks32mc08x_tim.c</FileName>
              <FileType>1</FileType>
              <FilePath>.\LKS32MC08x_Periph_Driver\Source\lks32mc08x_tim.c</FilePath>
            </File>
            <File>
              <FileName>lks32mc08x_uart.c</FileName>
              <FileType>1</FileType>
              <FilePath>.\LKS32MC08x_Periph_Driver\Source\lks32mc08x_uart.c</FilePath>
            </File>
            <File>
              <FileName>lks32mc08x_tmp.c</FileName>
              <FileType>1</FileType>
              <FilePath>.\LKS32MC08x_Periph_Driver\Source\lks32mc08x_tmp.c</FilePath>
            </File>
            <File>
              <FileName>lks32mc08x_nvr.lib</FileName>
              <FileType>4</FileType>
              <FilePath>.\LKS32MC08x_Periph_Driver\Source\lks32mc08x_nvr.lib</FilePath>
            </File>
            <File>
              <FileName>startup_lks32mc08x.s</FileName>
              <FileType>2</FileType>
              <FilePath>.\LKS32MC08x_Periph_Driver\Source\startup_lks32mc08x.s</FilePath>
            </File>
          </Files>
        </Group>
        <Group>
          <GroupName>::CMSIS</GroupName>
        </Group>
      </Groups>
    </Target>
  </Targets>

  <RTE>
    <apis/>
    <components>
      <component Cclass="CMSIS" Cgroup="CORE" Cvendor="ARM" Cversion="5.5.0" condition="ARMv6_7_8-M Device">
        <package name="CMSIS" schemaVersion="1.3" url="http://www.keil.com/pack/" vendor="ARM" version="5.8.0"/>
        <targetInfos>
          <targetInfo name="LK_StdPeriph_GPIO"/>
        </targetInfos>
      </component>
    </components>
    <files>
      <file attr="config" category="source" condition="Startup ARM" name="Device\Source\ARM\startup_lks32mc08x.s" version="1.0.0">
        <instance index="0" removed="1">RTE\Device\LKS32MC081C8T8\startup_lks32mc08x.s</instance>
        <component Cclass="Device" Cgroup="Startup" Cvendor="Linko" Cversion="1.0.0" condition="LKS08x CMSIS-CORE"/>
        <package name="LKS08x" schemaVersion="1.2" url="" vendor="Linko" version="1.0.4"/>
        <targetInfos/>
      </file>
    </files>
  </RTE>

  <LayerInfo>
    <Layers>
      <Layer>
        <LayName>LK_StdPeriph_GPIO</LayName>
        <LayPrjMark>1</LayPrjMark>
      </Layer>
    </Layers>
  </LayerInfo>

</Project>
