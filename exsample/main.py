import asyncio, json, ast, textwrap
from autogen_agentchat.agents import Assistant<PERSON><PERSON>
from autogen_ext.tools.mcp import StdioServerParams, mcp_server_tools
from autogen_core.models import ModelInfo, ModelFamily
from autogen_ext.models.ollama import OllamaChatCompletionClient

MONGO_URI  = "mongodb://localhost:27017/chip_manual"
OLLAMA_TAG = "qwen3:14b-fp16"

async def main():
    tools = await mcp_server_tools(
        StdioServerParams(
            command="npx",
            args=["-y", "mcp-mongo-server", MONGO_URI, "--log-level", "debug"],
            read_timeout_seconds=90,
        )
    )

    model = OllamaChatCompletionClient(
        model=OLLAMA_TAG,
        host="http://localhost:11434",
        model_info=ModelInfo(
            vision=False, function_calling=True,
            structured_output=True, json_output=True,
            family=ModelFamily.UNKNOWN),
        default_params={"temperature": 0},
    )

    assistant = AssistantAgent(
        name="mongo_helper",
        model_client=model,
        tools=tools,
        system_message=textwrap.dedent("""
            你有 listCollections() 工具，可列出 chip_manual 数据库集合。
            当用户询问集合时，必须调用该工具（arguments={}）。
            获取到原始返回后，只需要把 "name" 字段组成数组回复。
        """).strip(),
        reflect_on_tool_use=True,
    )

    res = await assistant.run(task="no thinking,列出 chip_manual 数据库有哪些集合？（只给数组）")
    print("\n🟢 LLM 回复：\n", res.messages[-1].content)

asyncio.run(main())
