from autogen import ConversableAgent

ollama_config = {
    "model": "mistral-small3.1:24b-instruct-2503-fp16",
    "base_url": "http://localhost:11434/v1",
    "api_key": "ollama",
    "price": [0, 0]
}

assistant = ConversableAgent(
    name="OllamaAssistant",
    llm_config={
        "config_list": [ollama_config],
        "temperature": 0.7,
        "max_tokens": 500,
    },
    description="A helpful assistant powered by Ollama's Mistral model."
)

user_proxy = ConversableAgent(
    name="User",
    human_input_mode="ALWAYS",
    max_consecutive_auto_reply=0,
)

user_proxy.initiate_chat(
    assistant,
    message="你好，介绍一下自己"
)