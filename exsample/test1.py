import asyncio
from autogen_agentchat.agents import AssistantAgent
from autogen_core.models import ModelInfo, ModelFamily
from autogen_ext.models.ollama import OllamaChatCompletionClient
from autogen_ext.tools.mcp import StdioServerParams, mcp_server_tools

async def main():
    tools = await mcp_server_tools(
        StdioServerParams(
            command="npx",
            args=["-y", "mcp-mongo-server",
                  "mongodb://localhost:27017/chip_manual"],
            read_timeout_seconds=60,
        )
    )

    model_client = OllamaChatCompletionClient(
        model="mistral-small3.1:24b-instruct-2503-fp16",
        host="http://localhost:11434",
        model_info=ModelInfo(
            family=ModelFamily.UNKNOWN,
            vision=False,
            function_calling=True,
            json_output=True,
            structured_output=True,
        ),
    )

    assistant = AssistantAgent(
        name="mongo_helper",
        model_client=model_client,
        tools=tools,
        system_message=(
            "你是数据库助手，拥有 list_collections(db) 工具。\n"
            "凡是用户想知道数据库有哪些集合，必须调用该工具。"
        ),
        reflect_on_tool_use=True,   # 在控制台显示 [TOOL] 调用
    )

    final = await assistant.run(task="列出 chip_manual 数据库的所有集合")

    print("\n🟢 模型最终回复：\n", final.messages[-1].content)

if __name__ == "__main__":
    asyncio.run(main())
