import asyncio, json, ast
from autogen_ext.tools.mcp import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>bench, StdioServerParams

MONGO_URI = "mongodb://localhost:27017/chip_manual"

async def main():
    async with McpWorkbench(
        server_params=StdioServerParams(
            command="npx",
            args=["-y", "mcp-mongo-server", MONGO_URI, "--log-level", "debug"],
            read_timeout_seconds=60,
        )
    ) as wb:
        res = await wb.call_tool("listCollections", arguments={}, cancellation_token=None)

        if res.is_error:
            print("❌ 工具执行失败:", res.to_text())
            return

        raw = res.to_text()
        try:
            collections = json.loads(raw)
        except json.JSONDecodeError:
            collections = ast.literal_eval(raw)

        print("🟢 集合列表:", collections)

asyncio.run(main())
