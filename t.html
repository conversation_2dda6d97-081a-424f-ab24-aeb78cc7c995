<!DOCTYPE html>
<html lang="zh-CN">
<head>
  <meta charset="UTF-8" />
  <title>STM32 GPIO LED 配置课件</title>
  <style>
    body {
      font-family: Arial, sans-serif;
      padding: 20px;
      background-color: #f5f5f5;
    }
    h1 { color: #2c3e50; text-align: center; }
    .container {
      max-width: 800px;
      margin: auto;
      background: white;
      padding: 25px;
      border-radius: 10px;
      box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
    }
    .config-section {
      display: grid;
      grid-template-columns: repeat(2, 1fr);
      gap: 15px;
    }
    .config-section label {
      font-weight: bold;
    }
    select, button {
      padding: 8px;
      border-radius: 5px;
      border: 1px solid #ccc;
    }
    .code-section {
      background-color: #2e3440;
      color: #f8f8f2;
      padding: 15px;
      border-radius: 5px;
      font-family: Consolas, monospace;
      white-space: pre-wrap;
      overflow-x: auto;
    }
    .led {
      width: 100px;
      height: 100px;
      background-color: #34495e;
      border-radius: 50%;
      margin-top: 20px;
      text-align: center;
      line-height: 100px;
      font-size: 24px;
      color: white;
      transition: background-color 0.3s ease;
    }
    .led.on { background-color: #2ecc71; }
  </style>
</head>
<body>
  <div class="container">
    <h1>STM32 GPIO LED 配置</h1>
    
    <!-- 配置选项 -->
    <div class="config-section">
      <label for="mode">GPIO Mode</label>
      <select id="mode" onchange="updateCode()">
        <option value="GPIO_MODE_OUTPUT_PP">Output (Push-Pull)</option>
        <option value="GPIO_MODE_INPUT">Input</option>
      </select>

      <label for="pull">Pull-up/Pull-down</label>
      <select id="pull" onchange="updateCode()">
        <option value="GPIO_NOPULL">无上下拉</option>
        <option value="GPIO_PULLUP">上拉</option>
        <option value="GPIO_PULLDOWN">下拉</option>
      </select>

      <label for="speed">Speed</label>
      <select id="speed" onchange="updateCode()">
        <option value="GPIO_SPEED_FREQ_LOW">低速</option>
        <option value="GPIO_SPEED_FREQ_MEDIUM">中速</option>
        <option value="GPIO_SPEED_FREQ_HIGH">高速</option>
      </select>

      <label for="pin">引脚编号</label>
      <select id="pin" onchange="updateCode()">
        <option value="GPIO_PIN_0">PA0</option>
        <option value="GPIO_PIN_1">PA1</option>
      </select>
    </div>

    <!-- 代码生成 -->
    <h3>配置代码 (STM32 HAL)</h3>
    <div class="code-section" id="codeOutput">
/* 示例代码将自动更新 */
    </div>

    <!-- LED 模拟 -->
    <h3>LED 效果模拟</h3>
    <div class="led" id="led"></div>
    <button onclick="toggleLED()">Toggle LED</button>
  </div>

  <!-- JavaScript 动态逻辑 -->
  <script>
    const pinOptions = {
      "GPIO_PIN_0": "LED连接到PA0",
      "GPIO_PIN_1": "LED连接到PA1"
    };

    function updateCode() {
      const mode = document.getElementById("mode").value;
      const pull = document.getElementById("pull").value;
      const speed = document.getElementById("speed").value;
      const pin = document.getElementById("pin").value;

      let codeTemplate = `// GPIO配置\nGPIO_InitTypeDef GPIO_InitStruct = {0};\n\n/* 配置引脚: ${pin} */\nGPIO_InitStruct.Pin = ${pin};\nGPIO_InitStruct.Mode = ${mode};\nGPIO_InitStruct.Pull = ${pull};\nGPIO_InitStruct.Speed = ${speed};\nHAL_GPIO_Init(GPIOA, &GPIO_InitStruct);\n\n/* 点亮LED（输出模式下） */\nHAL_GPIO_WritePin(GPIOA, ${pin}, GPIO_PIN_SET);`;

      document.getElementById("codeOutput").textContent = codeTemplate;
    }

    let ledOn = false;
    function toggleLED() {
      ledOn = !ledOn;
      const ledElm = document.getElementById("led");
      if (ledOn) {
        ledElm.classList.add("on");
        ledElm.textContent = "ON";
      } else {
        ledElm.classList.remove("on");
        ledElm.textContent = "OFF";
      }
    }

    // 初始化代码
    window.onload = updateCode;
  </script>
</body>
</html>
