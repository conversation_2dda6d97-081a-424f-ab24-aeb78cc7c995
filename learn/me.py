import asyncio
from autogen_agentchat.agents import Assistant<PERSON><PERSON>
from autogen_agentchat.teams import MagenticOneGroupChat
from autogen_agentchat.ui import Console
from model_registry import get_model_client

async def main() -> None:
    # 初始化模型客户端
    try:
        model_client = get_model_client("qwen3:14b")
    except Exception as e:
        print(f"错误：无法初始化模型客户端 - {e}")
        return

    # 创建助手代理
    assistant = AssistantAgent(
        "Assistant",
        model_client=model_client,
    )
    team = MagenticOneGroupChat([assistant], model_client=model_client)

    # 读取 full.md 文件内容
    file_path = "/Users/<USER>/Public/AutoGen/learn/full.md"
    try:
        with open(file_path, 'r', encoding='utf-8') as file:
            task_content = file.read()
    except FileNotFoundError:
        print(f"错误：文件 {file_path} 未找到")
        return
    except Exception as e:
        print(f"错误：无法读取文件 {file_path} - {e}")
        return

    # 将文件内容作为任务传递
    try:
        await <PERSON>sole(team.run_stream(task=task_content))
    except Exception as e:
        print(f"错误：执行任务失败 - {e}")
    finally:
        # 关闭模型客户端
        try:
            await model_client.close()
        except Exception as e:
            print(f"错误：关闭模型客户端失败 - {e}")

if __name__ == "__main__":
    asyncio.run(main())