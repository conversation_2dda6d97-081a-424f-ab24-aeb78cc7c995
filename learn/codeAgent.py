import asyncio

from autogen_ext.code_executors.docker import Docker<PERSON>ommandLineCodeExecutor
#from autogen_ext.models.openai import OpenAIChatCompletionClient

from autogen_agentchat.agents import AssistantAgent, CodeExecutorAgent
from autogen_agentchat.conditions import MaxMessageTermination
from autogen_agentchat.teams import RoundRobinGroupChat
from autogen_agentchat.ui import <PERSON>sole
from model_registry import get_model_client
termination_condition = MaxMessageTermination(3)


async def main() -> None:
   #model_client = OpenAIChatCompletionClient(model="gpt-4o")
    model_client = get_model_client("mistral-small3.1:24b")
    # define the Docker CLI Code Executor
    code_executor = DockerCommandLineCodeExecutor(work_dir="coding")

    # start the execution container
    await code_executor.start()

    code_executor_agent = CodeExecutorAgent("code_executor_agent", code_executor=code_executor)
    coder_agent = AssistantAgent("coder_agent", model_client=model_client)

    groupchat = RoundRobinGroupChat(
        participants=[coder_agent, code_executor_agent], termination_condition=termination_condition
    )

    task = "Write python code to print Hello World!"
    await Console(groupchat.run_stream(task=task))

    # stop the execution container
    await code_executor.stop()


asyncio.run(main())
