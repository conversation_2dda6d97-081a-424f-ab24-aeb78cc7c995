
import asyncio
import sys
import os
# 添加父目录到路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from autogen_agentchat.agents import AssistantAgent
from autogen_ext.tools.mcp import StdioServerParams, McpWorkbench
from autogen_agentchat.ui import Console
from mcp_registry import get_mcp_params
from LM_Studio import get_lmstudio_client

async def main() -> None:
    try:
        # 创建LM Studio客户端
        lm_studio_client = get_lmstudio_client("qwen3-32b")
        print(f"✅ LM Studio客户端创建成功: {lm_studio_client.model}")

        # 获取MCP参数
        params = get_mcp_params("mongo_chip")
        print(f"✅ MCP参数获取成功: {params.command}")

        # 使用LM Studio客户端作为模型客户端
        model_client = lm_studio_client

        # 创建MCP工作台和助手
        async with McpWorkbench(server_params=params) as workbench:
            print("✅ MCP工作台创建成功")

            assistant = AssistantAgent(
                name="Assistant",
                model_client=model_client,
                system_message="你是数据库助手，拥有 list_collections(db) 工具。\n",
                workbench=workbench,
                reflect_on_tool_use=True,
            )
            print("✅ 助手创建成功")

            # 运行对话
            print("🚀 开始运行对话...")
            await Console(assistant.run_stream(task="列出集合"))

    except Exception as e:
        print(f"❌ 错误: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    asyncio.run(main())
