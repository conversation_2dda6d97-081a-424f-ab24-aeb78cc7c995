
import asyncio, json, ast, textwrap
from autogen_agentchat.agents import Assistant<PERSON><PERSON>
from autogen_ext.tools.mcp import StdioServerParams, mcp_server_tools
from autogen_core.models import ModelInfo, ModelFamily
from model_registry import get_model_client
from autogen_ext.tools.mcp import StdioServerParams, Mcp<PERSON>orkbench
from autogen_agentchat.ui import <PERSON>sole
from mcp_registry import get_mcp_params
from LM_Studio import get_lmstudio_client

async def get_current_time() -> str:
    return "The current time is 12:00 PM."



async def main()-> None:

    lm_studio_client = get_lmstudio_client("qwen3-32b")  
    params = get_mcp_params("mongo_chip")
    model_client = lm_studio_client#get_model_client("qwen3:14b")
    async with <PERSON>c<PERSON><PERSON>orkben<PERSON>(server_params=params) as workbench:
     assistant = AssistantAgent(
            name="Assistant",
            model_client=model_client,
            system_message=( "你是数据库助手，拥有 list_collections(db) 工具。\n" ),
            workbench=workbench,
            reflect_on_tool_use=True,
        )
     await <PERSON><PERSON><PERSON>(  assistant.run_stream(task="列出集合")

        )


asyncio.run(main())
