import asyncio
import os
from pathlib import Path
from autogen_agentchat.agents import AssistantAgent
from autogen_agentchat.teams import MagenticOneGroupChat
from autogen_agentchat.ui import Console
from autogen_agentchat.messages import TextMessage
from model_registry import get_model_client


class DocumentAnalyzerAgent:
    """文档分析代理类，专门用于阅读和理解文档内容"""
    
    def __init__(self, model_name: str = "qwen3:14b"):
        self.model_name = model_name
        self.model_client = None
        self.assistant = None
        self.team = None
    
    async def initialize(self):
        """初始化模型客户端和代理"""
        try:
            self.model_client = get_model_client(self.model_name)
            print(f"✅ 成功初始化模型客户端: {self.model_name}")
            
            # 创建专门的文档分析助手
            self.assistant = AssistantAgent(
                name="DocumentAnalyzer",
                model_client=self.model_client,
                system_message="""
                你是一个专业的文档分析助手。你的任务是：
                1. 仔细阅读提供的文档内容
                2. 理解文档的主要内容和结构
                3. 提取关键信息和要点
                4. 回答关于文档内容的问题
                5. 提供文档内容的摘要和分析
                
                请用清晰、结构化的方式回应，确保理解准确且全面。
                """
            )
            
            self.team = MagenticOneGroupChat([self.assistant], model_client=self.model_client)
            print("✅ 成功创建文档分析团队")
            
        except Exception as e:
            print(f"❌ 初始化失败: {e}")
            raise
    
    async def read_document(self, file_path: str) -> str:
        """读取文档内容"""
        try:
            path = Path(file_path)
            if not path.exists():
                raise FileNotFoundError(f"文件不存在: {file_path}")
            
            with open(path, 'r', encoding='utf-8') as file:
                content = file.read()
            
            print(f"✅ 成功读取文档: {file_path}")
            print(f"📄 文档大小: {len(content)} 字符")
            return content
            
        except Exception as e:
            print(f"❌ 读取文档失败: {e}")
            raise
    
    async def analyze_document(self, file_path: str):
        """分析文档内容"""
        try:
            # 读取文档
            content = await self.read_document(file_path)
            
            # 构建分析任务
            analysis_task = f"""
            请分析以下文档内容：

            文档路径: {file_path}
            文档内容:
            {content}

            请提供以下分析结果：
            1. 文档概述和主要目的
            2. 文档结构和章节划分
            3. 关键信息和要点提取
            4. 技术细节（如果有的话）
            5. 总结和建议

            请用中文回答，格式清晰易读。
            """
            
            print("🔍 开始分析文档...")
            await Console(self.team.run_stream(task=analysis_task))
            
        except Exception as e:
            print(f"❌ 分析文档失败: {e}")
            raise
    
    async def ask_question(self, question: str, document_content: str = None):
        """基于文档内容回答问题"""
        try:
            if document_content:
                task = f"""
                基于以下文档内容回答问题：

                文档内容：
                {document_content}

                问题：{question}

                请基于文档内容给出准确、详细的回答。
                """
            else:
                task = question
            
            print(f"💭 回答问题: {question}")
            await Console(self.team.run_stream(task=task))
            
        except Exception as e:
            print(f"❌ 回答问题失败: {e}")
            raise
    
    async def interactive_mode(self, file_path: str):
        """交互模式，允许用户提问"""
        try:
            # 先读取文档
            content = await self.read_document(file_path)
            
            # 初始分析
            await self.analyze_document(file_path)
            
            print("\n" + "="*50)
            print("📚 进入交互模式 - 你可以询问关于文档的任何问题")
            print("输入 'quit' 或 'exit' 退出")
            print("="*50 + "\n")
            
            while True:
                try:
                    question = input("❓ 请输入你的问题: ").strip()
                    
                    if question.lower() in ['quit', 'exit', '退出']:
                        print("👋 再见！")
                        break
                    
                    if not question:
                        continue
                    
                    await self.ask_question(question, content)
                    print("\n" + "-"*30 + "\n")
                    
                except KeyboardInterrupt:
                    print("\n👋 再见！")
                    break
                except Exception as e:
                    print(f"❌ 处理问题时出错: {e}")
        
        except Exception as e:
            print(f"❌ 交互模式失败: {e}")
            raise
    
    async def close(self):
        """关闭资源"""
        try:
            if self.model_client:
                await self.model_client.close()
                print("✅ 成功关闭模型客户端")
        except Exception as e:
            print(f"❌ 关闭资源失败: {e}")


async def main():
    """主函数"""
    file_path = "/Users/<USER>/Public/AutoGen/learn/full.md"
    
    # 创建文档分析器
    analyzer = DocumentAnalyzerAgent("qwen3:14b")
    
    try:
        # 初始化
        await analyzer.initialize()
        
        # 检查文件是否存在
        if not os.path.exists(file_path):
            print(f"❌ 文件不存在: {file_path}")
            return
        
        print(f"📖 准备分析文档: {file_path}\n")
        
        # 选择运行模式
        print("请选择运行模式:")
        print("1. 仅分析文档")
        print("2. 交互模式（分析后可提问）")
        
        try:
            choice = input("请输入选择 (1/2): ").strip()
        except KeyboardInterrupt:
            choice = "1"
        
        if choice == "2":
            # 交互模式
            await analyzer.interactive_mode(file_path)
        else:
            # 仅分析模式
            await analyzer.analyze_document(file_path)
    
    except Exception as e:
        print(f"❌ 执行失败: {e}")
    
    finally:
        # 清理资源
        await analyzer.close()


if __name__ == "__main__":
    try:
        asyncio.run(main())
    except KeyboardInterrupt:
        print("\n👋 程序被用户中断")
    except Exception as e:
        print(f"❌ 程序执行失败: {e}")