import asyncio
from autogen_agentchat.agents import Assistant<PERSON><PERSON>
from autogen_agentchat.teams import MagenticOneGroupChat
from autogen_agentchat.ui import Console
from model_registry import get_model_client

async def main() -> None:
    # 初始化模型客户端
    try:
        model_client = get_model_client("qwen3:14b")
    except Exception as e:
        print(f"错误：无法初始化模型客户端 - {e}")
        return

    # 创建助手 Agent
    assistant = AssistantAgent(
        name="Assistant",
        model_client=model_client,
    )

    # 创建团队
    team = MagenticOneGroupChat([assistant], model_client=model_client)

    # 读取 full.md 文件内容
    file_path = "/Users/<USER>/Public/AutoGen/learn/full.md"
    try:
        with open(file_path, 'r', encoding='utf-8') as file:
            file_content = file.read()
    except FileNotFoundError:
        print(f"错误：文件 {file_path} 未找到")
        return
    except Exception as e:
        print(f"错误：无法读取文件 {file_path} - {e}")
        return

    # 设置任务内容为：让 agent 理解文档内容
    task_prompt = f"""
你将接收一个 Markdown 文档内容，请阅读并理解其结构与要点。

你的任务是总结这个文档的主要内容、结构划分和关键概念，不需要输出原文。

文档内容如下：
---
{file_content}
---
请开始你的总结与分析。
"""

    try:
        # 执行任务，但不打印全文，而是展示 agent 的理解
        await Console(team.run_stream(task=task_prompt))
    except Exception as e:
        print(f"错误：执行任务失败 - {e}")
    finally:
        try:
            await model_client.close()
        except Exception as e:
            print(f"错误：关闭模型客户端失败 - {e}")

if __name__ == "__main__":
    asyncio.run(main())
