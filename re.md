📚 AutoGen 项目中使用多个 MCP 工具的指南
📁 推荐项目结构
bash
复制
编辑
AutoGen/
├── learn/
│   ├── __init__.py
│   └── my_agent.py
├── mcp_registry.py         # ✅ MCP 服务统一注册
├── model_registry.py       # ✅ 模型统一注册
├── run.py                  # ✅ 启动入口（选模型 + 工具）
🔧 MCP 服务注册（mcp_registry.py）
python
复制
编辑
from autogen_ext.tools.mcp import StdioServerParams

MCP_SERVER_REGISTRY = {
    "mongo_chip": {
        "command": "npx",
        "args": ["-y", "mcp-mongo-server", "mongodb://localhost:27017/chip_manual", "--log-level", "debug"],
        "timeout": 90,
    },
    "filesystem_docs": {
        "command": "npx",
        "args": ["-y", "@modelcontextprotocol/server-filesystem", "/Users/<USER>/Public/AutoGen/rdfiles/doc"],
        "timeout": 90,
    },
    "fetch": {
        "command": "uvx",
        "args": ["mcp-server-fetch"],
        "timeout": 60,
    },
}

def get_mcp_params(name: str) -> StdioServerParams:
    cfg = MCP_SERVER_REGISTRY[name]
    return StdioServerParams(
        command=cfg["command"],
        args=cfg["args"],
        read_timeout_seconds=cfg["timeout"]
    )

def list_mcp_servers():
    return list(MCP_SERVER_REGISTRY.keys())
🤖 使用方式：两种调用模式
✅ 模式一：tools（推荐用于多个 MCP 工具组合）
python
复制
编辑
from mcp_registry import get_mcp_params
from autogen_ext.tools.mcp import mcp_server_tools

params_mongo = get_mcp_params("mongo_chip")
params_fs = get_mcp_params("filesystem_docs")

tools = await mcp_server_tools(params_mongo) + await mcp_server_tools(params_fs)

assistant = AssistantAgent(
    name="Assistant",
    model_client=get_model_client("qwen3"),
    tools=tools,
    reflect_on_tool_use=True,
)
✅ 模式二：workbench（推荐用于单个交互式 MCP）
python
复制
编辑
from mcp_registry import get_mcp_params
from autogen_ext.tools.mcp import McpWorkbench

params = get_mcp_params("filesystem_docs")

async with McpWorkbench(server_params=params) as workbench:
    assistant = AssistantAgent(
        name="Assistant",
        model_client=get_model_client("qwen3"),
        workbench=workbench,
        reflect_on_tool_use=True,
    )
🛠 调用 MCP 工具任务示例
python
复制
编辑
await Console(assistant.run_stream(
    task="请列出 /docs/ 目录中的所有 Markdown 文件，并返回每个文件前100个字符"
))
只要你正确配置了 tools 或 workbench，模型就能自动调用 MCP 工具进行操作。

🔄 封装工具组合器（可选）
python
复制
编辑
async def get_combined_tools(*names):
    from mcp_registry import get_mcp_params
    from autogen_ext.tools.mcp import mcp_server_tools

    tools = []
    for name in names:
        tools += await mcp_server_tools(get_mcp_params(name))
    return tools
使用方式：

python
复制
编辑
tools = await get_combined_tools("mongo_chip", "filesystem_docs")
✅ 场景推荐表
目标用途	推荐方式	原因说明
同时加载多个服务	tools	支持多个 MCP 工具组合，灵活、通用
单一服务交互密集任务	workbench	保持状态更高效，适用于文件系统、UVX 等
多服务自动注入切换	tools	工具形式便于动态组合、扩展性强