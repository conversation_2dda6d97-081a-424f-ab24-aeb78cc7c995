from autogen_ext.tools.mcp import StdioServerParams

MCP_SERVER_REGISTRY = {
    "fetch": {
        "command": "uvx",
        "args": ["mcp-server-fetch"],
        "timeout": 60,
    },
    "mongo_chip": {
        "command": "npx",
        "args": ["-y", "mcp-mongo-server", "mongodb://localhost:27017/chip_manual", "--log-level", "debug"],
        "timeout": 90,
    },
    "filesystem_docs": {
        "command": "npx",
        "args": ["-y", "@modelcontextprotocol/server-filesystem", "/Users/<USER>/Public/AutoGen/rdfiles/doc"],
        "timeout": 90,
    },
    # 你可以继续添加更多服务
}

def get_mcp_params(name: str) -> StdioServerParams:
    if name not in MCP_SERVER_REGISTRY:
        raise ValueError(f"未找到名称为 '{name}' 的 MCP 配置")
    cfg = MCP_SERVER_REGISTRY[name]
    return StdioServerParams(
        command=cfg["command"],
        args=cfg["args"],
        read_timeout_seconds=cfg["timeout"]
    )
async def get_combined_mcp_tools(*server_names):
    from mcp_registry import get_mcp_params
    from autogen_ext.tools.mcp import mcp_server_tools
    tools = []
    for name in server_names:
        params = get_mcp_params(name)
        tools += await mcp_server_tools(params)
    return tools


def list_mcp_servers():
    return list(MCP_SERVER_REGISTRY.keys())
