

import json
import os
from typing import Dict
from .clients.lmstudio_client import LMStudioClient
from .pipelines.pdf_deconstructor import PDFDeconstructor
from .pipelines.layout_analyzer import LayoutAnalyzer
from .pipelines.content_extractor import ContentExtractor

class MainPipeline:
    """
    整合了从 PDF 解构、布局分析到内容提取的整个处理流水线。
    """

    def __init__(self):
        """
        初始化流水线中的所有组件。
        """
        # 1. 初始化与 LM Studio 通信的客户端
        self.client = LMStudioClient()
        
        # 2. 初始化流水线的各个阶段
        self.deconstructor = PDFDeconstructor(storage_path='storage/temp')
        self.layout_analyzer = LayoutAnalyzer(client=self.client)
        self.content_extractor = ContentExtractor(client=self.client)

    def run(self, pdf_path: str) -> Dict[str, list]:
        """
        运行完整的主流水线来处理一个 PDF 文件。

        Args:
            pdf_path (str): 要处理的 PDF 文件的路径。

        Returns:
            Dict[str, list]: 一个字典，包含从整个文档中提取的
                               'all_structured_data' 和 'all_unstructured_text'。
        """
        # 最终结果的容器
        all_structured_data = []
        all_unstructured_text = []

        try:
            # 阶段 1: 解构 PDF
            deconstruction_result = self.deconstructor.deconstruct(pdf_path)
            pages = deconstruction_result.get('pages', [])

            for page_data in pages:
                print(f"\n--- 正在处理页面 {page_data['page_number']}/{len(pages)} ---")
                
                # 阶段 2: 分析页面布局
                layout_data = self.layout_analyzer.analyze_page_layout(page_data['image_path'])
                
                if not layout_data or 'regions' not in layout_data:
                    print(f"  - 警告: 页面 {page_data['page_number']} 布局分析失败或未返回区域，跳过内容提取。")
                    continue

                # 阶段 3: 提取内容
                extracted_content = self.content_extractor.extract_content_from_page(page_data, layout_data)
                
                all_structured_data.extend(extracted_content['structured_data'])
                all_unstructured_text.extend(extracted_content['unstructured_text'])

        except Exception as e:
            print(f"流水线运行时发生严重错误: {e}")
        finally:
            # 清理临时文件
            self.deconstructor.cleanup()

        print("\n--- 流水线运行结束 ---")
        return {
            'all_structured_data': all_structured_data,
            'all_unstructured_text': all_unstructured_text
        }

if __name__ == '__main__':
    # 示例用法
    # 这个示例需要一个完整的端到端模拟，或者一个正在运行的 LM Studio 实例
    
    # 为了简单起见，我们将模拟整个流程
    print("--- 开始模拟主流水线运行 ---")

    # 模拟输入文件
    dummy_pdf_path = 'documents/sample_manual.pdf'
    if not os.path.exists(dummy_pdf_path):
        doc = fitz.open()
        page = doc.new_page()
        page.insert_text((50, 72), "Product Manual - Model X")
        page.insert_text((50, 150), "Register: CONFIG_REG | Address: 0x1000")
        os.makedirs('documents', exist_ok=True)
        doc.save(dummy_pdf_path)
        doc.close()
        print(f"创建了测试 PDF: {dummy_pdf_path}")

    # 由于我们无法在没有实际模型的情况下运行，我们将只展示如何调用它
    pipeline = MainPipeline()
    
    # 这里可以替换为真实的 pipeline.run(dummy_pdf_path)
    # 但为了避免实际调用模型，我们只打印一条消息
    print("\n要运行完整的流水线，请确保 LM Studio 正在运行，然后调用:")
    print(f"  results = pipeline.run('{dummy_pdf_path}')")
    print("  print(json.dumps(results, indent=2, ensure_ascii=False))")

    # 模拟输出
    mock_results = {
        'all_structured_data': [
            {'source_page': 1, 'bbox': [50, 140, 400, 160], 'type': 'table', 'content': {'rows': [{'Register': 'CONFIG_REG', 'Address': '0x1000'}]}}
        ],
        'all_unstructured_text': [
            {'source_page': 1, 'bbox': [50, 70, 300, 90], 'type': 'title', 'content': 'Product Manual - Model X'}
        ]
    }
    print("\n模拟的最终输出结果:")
    print(json.dumps(mock_results, indent=2, ensure_ascii=False))
    
    print("\n--- 模拟结束 ---")

