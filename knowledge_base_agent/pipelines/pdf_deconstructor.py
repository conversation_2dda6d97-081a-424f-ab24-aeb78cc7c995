import fitz  # PyMuPDF
import os
from PIL import Image
from typing import List, Dict, Tuple

class PDFDeconstructor:
    """
    负责将 PDF 文档解构成可供模型处理的格式。

    主要功能包括：
    1. 将 PDF 页面转换为高分辨率图像。
    2. 提取每个页面上的文本块及其位置信息。
    3. 管理解构过程产生的临时文件。
    """

    def __init__(self, storage_path: str = 'storage/temp'):
        """
        初始化 PDFDeconstructor。

        Args:
            storage_path (str): 用于存放临时图像文件的目录。
        """
        self.storage_path = storage_path
        self.image_path = os.path.join(self.storage_path, 'images')
        os.makedirs(self.image_path, exist_ok=True)

    def deconstruct(self, pdf_path: str, dpi: int = 300) -> Dict[str, List]:
        """
        执行 PDF 的解构过程。

        Args:
            pdf_path (str): 输入的 PDF 文件路径。
            dpi (int): 渲染图像的分辨率 (dots per inch)。

        Returns:
            Dict[str, List]: 一个字典，包含处理后的页面信息，
                             键为 'pages'，值为页面信息列表。
        """
        if not os.path.exists(pdf_path):
            raise FileNotFoundError(f"PDF 文件未找到: {pdf_path}")

        doc = fitz.open(pdf_path)
        
        processed_pages = []

        print(f"开始解构 PDF: {os.path.basename(pdf_path)}，共 {len(doc)} 页...")

        for page_num in range(len(doc)):
            page = doc.load_page(page_num)

            # 1. 将页面转换为图像
            image_path = self._render_page_as_image(page, page_num, dpi)

            # 2. 提取文本块及其位置
            text_blocks = self._extract_text_blocks(page)

            processed_pages.append({
                'page_number': page_num + 1,
                'image_path': image_path,
                'text_blocks': text_blocks,
                'dimensions': (page.rect.width, page.rect.height)
            })
            
            print(f"  - 已处理页面 {page_num + 1}/{len(doc)}")

        doc.close()
        print("PDF 解构完成。")
        return {'pages': processed_pages}

    def _render_page_as_image(self, page: fitz.Page, page_num: int, dpi: int) -> str:
        """
        将单个页面渲染为图像并保存。
        """
        pix = page.get_pixmap(dpi=dpi)
        image = Image.frombytes("RGB", [pix.width, pix.height], pix.samples)
        image_filename = f"page_{page_num + 1}.png"
        image_path = os.path.join(self.image_path, image_filename)
        image.save(image_path)
        return image_path

    def _extract_text_blocks(self, page: fitz.Page) -> List[Dict]:
        """
        提取页面中的所有文本块及其边界框。
        """
        blocks = page.get_text("dict")['blocks']
        text_blocks = []
        for b in blocks:
            if b['type'] == 0:  # 0 表示文本块
                for l in b['lines']:
                    for s in l['spans']:
                        text_blocks.append({
                            'text': s['text'],
                            'bbox': s['bbox']  # (x0, y0, x1, y1)
                        })
        return text_blocks

    def cleanup(self):
        """
        清理生成的临时图像文件。
        """
        if os.path.exists(self.image_path):
            for file_name in os.listdir(self.image_path):
                os.remove(os.path.join(self.image_path, file_name))
            os.rmdir(self.image_path)
            print("临时图像文件已清理。")

if __name__ == '__main__':
    # 示例用法
    # 假设在 'documents' 文件夹下有一个名为 'sample.pdf' 的文件
    # 为了运行此示例，请手动创建一个 dummy pdf
    dummy_pdf_path = 'documents/LKS32AT08x_UM_v1.37.pdf'
    if not os.path.exists(dummy_pdf_path):
        # 创建一个简单的 PDF 用于测试
        doc = fitz.open() 
        page = doc.new_page()
        page.insert_text((50, 72), "This is a test document for PDFDeconstructor.")
        page.insert_text((50, 100), "It contains text and tables.")
        # 一个简单的表格表示
        page.insert_text((50, 150), "Register: CTRL_REG | Address: 0x40010000")
        os.makedirs('documents', exist_ok=True)
        doc.save(dummy_pdf_path)
        doc.close()
        print(f"创建了测试 PDF: {dummy_pdf_path}")

    deconstructor = PDFDeconstructor()
    try:
        deconstruction_result = deconstructor.deconstruct(dummy_pdf_path)
        print("\n解构结果 (第一页):")
        print(json.dumps(deconstruction_result['pages'][0], indent=2))
    finally:
        # 清理生成的图像
        deconstructor.cleanup()
