import base64
import json
from typing import Dict, Any, List
from ..clients.lmstudio_client import LMStudioClient # 使用相对导入

class LayoutAnalyzer:
    """
    使用多模态大语言模型（通过 LM Studio）分析页面图像的布局。

    该分析器将页面图像发送给模型，并要求模型识别出不同类型的区域，
    例如段落、表格、图表等，并以结构化的 JSON 格式返回结果。
    """

    def __init__(self, client: LMStudioClient):
        """
        初始化 LayoutAnalyzer。

        Args:
            client (LMStudioClient): 用于与 LM Studio 服务器通信的客户端实例。
        """
        self.client = client
        self.system_message = (
            "You are an expert document layout analyzer. "
            "Your task is to analyze the provided image of a document page and identify distinct regions. "
            "These regions can be paragraphs, tables, diagrams, titles, or code snippets. "
            "For each identified region, provide its bounding box coordinates (x0, y0, x1, y1) and its type. "
            "The origin (0,0) is the top-left corner of the image. "
            "Respond ONLY with a valid JSON object that contains a single key 'regions', "
            "which is a list of identified region objects. Each object must have 'bbox' and 'type' keys."
        )

    def _encode_image_to_base64(self, image_path: str) -> str:
        """
        将图像文件编码为 Base64 字符串。
        """
        with open(image_path, "rb") as image_file:
            return base64.b64encode(image_file.read()).decode('utf-8')

    def analyze_page_layout(self, image_path: str) -> Dict[str, Any]:
        """
        分析单个页面图像的布局。

        Args:
            image_path (str): 要分析的页面图像的路径。

        Returns:
            Dict[str, Any]: 模型返回的包含区域信息的 JSON 对象。
                             如果分析失败，则返回一个空的字典。
        """
        print(f"  - 正在分析页面布局: {image_path}")
        base64_image = self._encode_image_to_base64(image_path)

        messages = [
            {
                "role": "system",
                "content": self.system_message
            },
            {
                "role": "user",
                "content": [
                    {
                        "type": "image_url",
                        "image_url": {
                            "url": f"data:image/png;base64,{base64_image}"
                        }
                    },
                    {
                        "type": "text",
                        "text": "Please analyze this document page layout."
                    }
                ]
            }
        ]

        # 调用模型，这里假设模型是多模态的
        response_content = self.client.get_completion(
            messages=messages,
            model="monkeyocr-recognition", # 指定你的多模态模型
            temperature=0.2, # 低温以获得更确定的输出
            max_tokens=4096 # 增加 max_tokens 以处理复杂的 JSON 输出
        )

        if response_content and "choices" in response_content and len(response_content["choices"]) > 0:
            try:
                # 清理和解析 JSON
                json_string = response_content["choices"][0]["message"]["content"]
                # 模型有时会返回被 markdown 包围的 json，需要清理
                if json_string.strip().startswith("```json"):
                    json_string = json_string.strip()[7:-3].strip()
                
                parsed_json = json.loads(json_string)
                print(f"  - 页面布局分析成功: {image_path}")
                return parsed_json
            except (json.JSONDecodeError, KeyError) as e:
                print(f"  - 错误: 解析模型返回的 JSON 失败 - {e}")
                raw_response = response_content["choices"][0]["message"]["content"]
                print(f"  - 原始响应: {raw_response}")
                return {}
        else:
            print(f"  - 错误: 未能从模型获取有效的布局分析响应。")
            return {}

if __name__ == '__main__':
    # 示例用法
    # 这个示例需要 LM Studio 正在运行，并且加载了 monkeyocr-recognition 模型。
    # 它还需要一个由 pdf_deconstructor.py 生成的示例页面图像。

    # 1. 准备环境
    # 确保 lmstudio_client.py 在正确的路径下
    # 确保有一个示例图像文件，例如在 storage/temp/images/page_1.png
    
    # 创建一个虚拟的 client 和 image 用于测试
    class MockLMStudioClient(LMStudioClient):
        def get_completion(self, messages: list, model: str, temperature: float, max_tokens: int) -> Dict[str, Any]:
            # 模拟一个成功的 API 返回
            mock_response = {
                "regions": [
                    {"bbox": [50, 70, 400, 90], "type": "title"},
                    {"bbox": [50, 100, 600, 200], "type": "paragraph"},
                    {"bbox": [50, 220, 700, 350], "type": "table"}
                ]
            }
            return {
                "choices": [
                    {
                        "message": {
                            "content": f"```json\n{json.dumps(mock_response, indent=2)}\n```"
                        }
                    }
                ]
            }

    # 创建一个虚拟的图像文件
    if not os.path.exists('storage/temp/images'):
        os.makedirs('storage/temp/images')
    dummy_image_path = 'storage/temp/images/page_1.png'
    try:
        Image.new('RGB', (800, 600), color = 'white').save(dummy_image_path)
        print(f"创建了测试图像: {dummy_image_path}")

        # 2. 运行分析器
        mock_client = MockLMStudioClient()
        analyzer = LayoutAnalyzer(client=mock_client)
        layout_data = analyzer.analyze_page_layout(dummy_image_path)

        # 3. 打印结果
        print("\n模拟布局分析结果:")
        print(json.dumps(layout_data, indent=2))

    finally:
        # 4. 清理
        if os.path.exists(dummy_image_path):
            os.remove(dummy_image_path)
