import json
from typing import Dict, Any, List, Tuple
from PIL import Image
from ..clients.lmstudio_client import LMStudioClient # 相对导入
import io
import base64

class ContentExtractor:
    """
    根据布局分析的结果，从页面中提取和结构化内容。

    它将布局区域（如段落、表格）与原始文本块进行匹配，并使用
    专门的提示（prompt）来处理复杂区域（如表格和图表），以提取
    结构化数据或详细描述。
    """

    def __init__(self, client: LMStudioClient):
        """
        初始化 ContentExtractor。

        Args:
            client (LMStudioClient): 用于与 LM Studio 服务器通信的客户端实例。
        """
        self.client = client
        self.table_extraction_prompt = (
            "You are a data extraction expert. The provided image is a cropped view of a table, "
            "likely a register map from a technical manual. Your task is to extract all information "
            "from this table and represent it as a structured JSON object. The JSON should have a key 'rows', "
            "which is a list of objects, where each object represents a row in the table. Use the table headers as keys."
        )
        self.diagram_description_prompt = (
            "You are a technical documentation expert. The provided image is a diagram from a chip manual. "
            "Your task is to describe this diagram in clear, detailed, and accessible text. "
            "Explain what the diagram shows, its main components, and how they are connected. "
            "Focus on providing a comprehensive textual representation of the visual information."
        )

    def _get_text_in_bbox(self, bbox: Tuple[float, float, float, float], all_text_blocks: List[Dict]) -> str:
        """
        从所有文本块中，聚合位于给定边界框内的文本。
        """
        contained_text = []
        for block in all_text_blocks:
            # 简单的重叠检查
            if not (bbox[2] < block['bbox'][0] or bbox[0] > block['bbox'][2] or \
                    bbox[3] < block['bbox'][1] or bbox[1] > block['bbox'][3]):
                contained_text.append(block['text'])
        return " ".join(contained_text).strip()

    def _extract_from_region_image(self, image_path: str, region_bbox: Tuple[float, float, float, float], prompt: str) -> str:
        """
        裁剪图像区域，并发送给模型以进行专门的内容提取。
        """
        with Image.open(image_path) as img:
            # 裁剪图像
            cropped_img = img.crop(region_bbox)
            # 将裁剪后的图像转换为 base64
            buffered = io.BytesIO()
            cropped_img.save(buffered, format="PNG")
            base64_image = base64.b64encode(buffered.getvalue()).decode('utf-8')

        messages = [
            {"role": "system", "content": prompt},
            {"role": "user", "content": [
                {"type": "image_url", "image_url": {"url": f"data:image/png;base64,{base64_image}"}},
                {"type": "text", "text": "Extract the information from this image."}
            ]}
        ]
        
        response = self.client.get_model_response(
            prompt=json.dumps(messages), # 注意：这里简化了调用，实际应适配 client
            system_message=prompt
        )
        return response if response else ""

    def extract_content_from_page(self, page_data: Dict[str, Any], layout_data: Dict[str, Any]) -> Dict[str, List]:
        """
        从单个页面提取所有内容。

        Args:
            page_data (Dict[str, Any]): 来自 PDFDeconstructor 的单页数据。
            layout_data (Dict[str, Any]): 来自 LayoutAnalyzer 的单页布局数据。

        Returns:
            Dict[str, List]: 包含 'structured_data' 和 'unstructured_text' 的字典。
        """
        structured_data = []
        unstructured_text = []
        
        if 'regions' not in layout_data:
            return {'structured_data': [], 'unstructured_text': []}

        print(f"  - 正在提取内容: {page_data['image_path']}")

        for region in layout_data['regions']:
            region_type = region.get('type', 'unknown').lower()
            bbox = region.get('bbox')

            if not bbox:
                continue

            if region_type in ['paragraph', 'title', 'text']:
                text = self._get_text_in_bbox(bbox, page_data['text_blocks'])
                if text:
                    unstructured_text.append({
                        'source_page': page_data['page_number'],
                        'bbox': bbox,
                        'type': region_type,
                        'content': text
                    })
            
            elif region_type == 'table':
                # 对于表格，我们使用模型从图像区域提取结构化数据
                table_json_str = self._extract_from_region_image(page_data['image_path'], bbox, self.table_extraction_prompt)
                try:
                    table_data = json.loads(table_json_str)
                    structured_data.append({
                        'source_page': page_data['page_number'],
                        'bbox': bbox,
                        'type': 'table',
                        'content': table_data
                    })
                except json.JSONDecodeError:
                    print(f"  - 警告: 无法解析表格的 JSON 输出。将文本作为非结构化数据处理。")
                    unstructured_text.append({'source_page': page_data['page_number'], 'bbox': bbox, 'type': 'table_text', 'content': table_json_str})

            elif region_type == 'diagram':
                # 对于图表，我们使用模型生成描述
                diagram_desc = self._extract_from_region_image(page_data['image_path'], bbox, self.diagram_description_prompt)
                if diagram_desc:
                    unstructured_text.append({
                        'source_page': page_data['page_number'],
                        'bbox': bbox,
                        'type': 'diagram_description',
                        'content': diagram_desc
                    })

        print(f"  - 内容提取完成: {page_data['image_path']}")
        return {'structured_data': structured_data, 'unstructured_text': unstructured_text}

if __name__ == '__main__':
    # 示例用法
    # 此示例需要一个模拟的 client, page_data, 和 layout_data

    class MockLMStudioClient(LMStudioClient):
        def get_model_response(self, prompt: str, system_message: str) -> str:
            if "table" in system_message:
                return json.dumps({"rows": [{"col1": "data1", "col2": "data2"}]})
            elif "diagram" in system_message:
                return "This is a detailed description of the diagram."
            return ""

    # 模拟输入数据
    mock_page_data = {
        'page_number': 1,
        'image_path': 'storage/temp/images/page_1.png', # 需要一个虚拟文件
        'text_blocks': [
            {'text': 'This is a title', 'bbox': (50, 70, 400, 90)},
            {'text': 'This is a paragraph.', 'bbox': (50, 100, 600, 200)}
        ],
        'dimensions': (800, 600)
    }
    mock_layout_data = {
        'regions': [
            {'bbox': [50, 70, 400, 90], 'type': 'title'},
            {'bbox': [50, 100, 600, 200], 'type': 'paragraph'},
            {'bbox': [50, 220, 700, 350], 'type': 'table'},
            {'bbox': [50, 400, 700, 550], 'type': 'diagram'}
        ]
    }

    # 创建虚拟图像文件
    if not os.path.exists('storage/temp/images'):
        os.makedirs('storage/temp/images')
    dummy_image_path = mock_page_data['image_path']
    Image.new('RGB', (800, 600), color = 'white').save(dummy_image_path)

    # 运行提取器
    extractor = ContentExtractor(client=MockLMStudioClient())
    extracted_content = extractor.extract_content_from_page(mock_page_data, mock_layout_data)

    print("\n提取内容结果:")
    print(json.dumps(extracted_content, indent=2, ensure_ascii=False))

    # 清理
    os.remove(dummy_image_path)