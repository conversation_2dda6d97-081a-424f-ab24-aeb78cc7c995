import chromadb
import networkx as nx
from sentence_transformers import SentenceTransformer
from typing import List, Dict, Any, Tuple
import hashlib
import json

class DatabaseManager:
    """
    管理知识图谱和向量数据库的存储。

    负责将提取出的结构化和非结构化数据存入相应的数据库，
    并确保两者之间的链接。
    """

    def __init__(self, storage_path: str = 'storage/db'):
        """
        初始化数据库管理器。

        Args:
            storage_path (str): 数据库文件的存储路径。
        """
        # 1. 初始化向量数据库 (ChromaDB)
        self.chroma_client = chromadb.PersistentClient(path=f"{storage_path}/chroma")
        self.embedding_model = SentenceTransformer('all-MiniLM-L6-v2') # 一个轻量且高效的模型
        self.collection = self.chroma_client.get_or_create_collection(name="documents")

        # 2. 初始化知识图谱 (NetworkX)
        self.graph = nx.DiGraph()
        self.graph_path = f"{storage_path}/knowledge_graph.gml"

    def _generate_unique_id(self, content: str) -> str:
        """
        为内容生成一个确定性的唯一 ID。
        """
        return hashlib.md5(content.encode()).hexdigest()

    def _generate_block_id(self, content: str, page_id: str) -> str:
        """
        为内容生成一个确定性的唯一 ID，包含页面 ID。
        """
        unique_string = f"{page_id}-{content}"
        return hashlib.md5(unique_string.encode()).hexdigest()

    def _add_node_and_edge(self, parent_id: str, child_id: str, child_type: str, edge_type: str, **kwargs):
        """
        辅助函数：添加节点和边。
        """
        if not self.graph.has_node(child_id):
            # 确保 kwargs 中不包含 'type' 键，因为它已经作为 child_type 传递了
            node_attrs = {k: v for k, v in kwargs.items() if k != 'type'}
            self.graph.add_node(child_id, type=child_type, **node_attrs)
        self.graph.add_edge(parent_id, child_id, type=edge_type)

    def process_mineru_middle_json(self, middle_json_path: str):
        """
        解析 MinerU 生成的 _middle.json 文件，并构建知识图谱和向量数据库。
        """
        print(f"--- 开始处理 MinerU _middle.json 文件: {middle_json_path} ---")
        with open(middle_json_path, 'r', encoding='utf-8') as f:
            data = json.load(f)

        doc_id = self._generate_unique_id(middle_json_path) # 文档的唯一ID
        self.graph.add_node(doc_id, type='Document', path=middle_json_path)

        all_unstructured_text_for_db = []

        for page_data in data['pdf_info']:
            page_idx = page_data.get('page_idx')
            page_id = f"{doc_id}_page_{page_idx}"
            self.graph.add_node(page_id, type='Page', page_number=page_idx + 1, page_size=page_data.get('page_size'))
            self.graph.add_edge(doc_id, page_id, type='HAS_PAGE')

            # 处理文本块 (para_blocks)
            for block in page_data.get('para_blocks', []):
                block_type = block.get('type', 'text')
                content = " ".join([span.get('content', '') for line in block.get('lines', []) for span in line.get('spans', [])]).strip()
                if not content: continue

                block_id = self._generate_block_id(content, page_id)
                self._add_node_and_edge(page_id, block_id, block_type, 'CONTAINS_BLOCK', 
                                        content=content, bbox=block.get('bbox'))
                
                # 将文本内容添加到向量数据库的队列中
                all_unstructured_text_for_db.append({
                    'id': block_id,
                    'content': content,
                    'source_page': page_idx + 1,
                    'type': block_type,
                    'bbox': block.get('bbox')
                })

            # 处理表格
            for table_data in page_data.get('tables', []):
                table_content = table_data.get('html', '') # MinerU 可能提供 HTML 格式的表格
                table_id = self._generate_block_id(table_content, page_id)
                self._add_node_and_edge(page_id, table_id, 'Table', 'CONTAINS_TABLE', 
                                        content=table_content, bbox=table_data.get('bbox'))
                
                # 尝试从表格中提取寄存器信息 (简化示例)
                # 这是一个高度定制化的部分，需要根据实际手册的表格格式来编写
                # 假设表格内容是 HTML，我们可以用 BeautifulSoup 解析，但这里简化为字符串匹配
                if "Register" in table_content and "Address" in table_content:
                    # 这是一个非常简化的提取，实际需要更复杂的解析
                    reg_name_match = re.search(r'Register: (\w+)', table_content)
                    addr_match = re.search(r'Address: (0x[0-9a-fA-F]+)', table_content)
                    
                    if reg_name_match and addr_match:
                        reg_name = reg_name_match.group(1)
                        reg_addr = addr_match.group(1)
                        reg_id = self._generate_block_id(f"{reg_name}_{reg_addr}", page_idx + 1, table_data.get('bbox'))
                        self._add_node_and_edge(table_id, reg_id, 'Register', 'HAS_REGISTER', 
                                                name=reg_name, address=reg_addr, full_table_html=table_content)
                        
                        # 也可以将表格内容作为非结构化文本添加到向量数据库
                        all_unstructured_text_for_db.append({
                            'id': table_id,
                            'content': f"Table content: {table_content}",
                            'source_page': page_idx + 1,
                            'type': 'table_html',
                            'bbox': table_data.get('bbox')
                        })

            # 处理图像
            for image_data in page_data.get('images', []):
                image_id = self._generate_block_id(f"image_{image_data.get('path')}", page_idx + 1, image_data.get('bbox'))
                self._add_node_and_edge(page_id, image_id, 'Image', 'CONTAINS_IMAGE', 
                                        bbox=image_data.get('bbox'), path=image_data.get('path'))
                # 图像描述可以作为非结构化文本
                if image_data.get('caption'):
                    all_unstructured_text_for_db.append({
                        'id': image_id,
                        'content': image_data['caption'],
                        'source_page': page_idx + 1,
                        'type': 'image_caption',
                        'bbox': image_data.get('bbox')
                    })

        # 将所有收集到的非结构化文本添加到向量数据库
        if all_unstructured_text_for_db:
            documents = [item['content'] for item in all_unstructured_text_for_db]
            metadatas = [{k: v for k, v in item.items() if k != 'content'} for item in all_unstructured_text_for_db]
            ids = [item['id'] for item in all_unstructured_text_for_db]
            
            embeddings = self.embedding_model.encode(documents, show_progress_bar=True)
            
            self.collection.add(
                embeddings=embeddings.tolist(),
                documents=documents,
                metadatas=metadatas,
                ids=ids
            )
            print(f"已将 {len(all_unstructured_text_for_db)} 个文本块/描述存入向量数据库。")

        # 保存知识图谱
        nx.write_gml(self.graph, self.graph_path)
        print(f"知识图谱已保存到: {self.graph_path}")
        print("--- MinerU _middle.json 处理完成 ---")

    def save_to_database(self, structured_data: List[Dict], unstructured_text: List[Dict]):
        """
        此方法现在主要用于兼容旧的流水线输出，或作为直接添加数据的接口。
        对于 MinerU 的输出，请使用 process_mineru_middle_json。
        """
        print("--- 开始将数据存入数据库 (通用方法) ---")
        
        # 1. 处理和存储非结构化文本到向量数据库
        if unstructured_text:
            documents = [item['content'] for item in unstructured_text]
            metadatas = [{k: v for k, v in item.items() if k != 'content'} for item in unstructured_text]
            ids = [self._generate_block_id(item['content'], item['source_page'], item['bbox']) for item in unstructured_text]
            
            embeddings = self.embedding_model.encode(documents, show_progress_bar=True)
            
            self.collection.add(
                embeddings=embeddings.tolist(),
                documents=documents,
                metadatas=metadatas,
                ids=ids
            )
            print(f"已将 {len(unstructured_text)} 个文本块存入向量数据库。")

        # 2. 处理和存储结构化数据到知识图谱
        if structured_data:
            for item in structured_data:
                if item.get('type') == 'table' and 'content' in item:
                    table_content = item['content'] # 假设 content 是表格的原始数据
                    table_id = self._generate_id(json.dumps(table_content), item['source_page'], item['bbox'])
                    self.graph.add_node(table_id, type='table', page=item['source_page'], content=table_content)
                    
                    # 示例：可以根据内容创建更复杂的图结构
                    # 例如，如果表格是寄存器表，可以创建寄存器节点
                    # 这里需要更具体的逻辑来解析 table_content
                    # ...

            print(f"已处理 {len(structured_data)} 个结构化数据项到知识图谱。")
            # 保存图到文件
            nx.write_gml(self.graph, self.graph_path)
            print(f"知识图谱已保存到: {self.graph_path}")

        print("--- 数据存储完成 (通用方法) ---")

    def query_vector_db(self, query_text: str, n_results: int = 5) -> Dict:
        """
        查询向量数据库。
        """
        query_embedding = self.embedding_model.encode([query_text])[0]
        results = self.collection.query(
            query_embeddings=[query_embedding.tolist()],
            n_results=n_results
        )
        return results

    def query_knowledge_graph(self, query: str) -> Any:
        """
        查询知识图谱。
        """
        # 这是一个非常简化的查询，实际应用中会更复杂
        # 例如，使用图查询语言或更复杂的遍历算法
        # 遍历所有节点，查找内容或属性中包含查询字符串的节点
        matching_nodes = []
        for node_id, attrs in self.graph.nodes(data=True):
            # 检查节点ID本身
            if query.lower() in str(node_id).lower():
                matching_nodes.append(node_id)
                continue
            # 检查节点属性
            for key, value in attrs.items():
                if query.lower() in str(value).lower():
                    matching_nodes.append(node_id)
                    break
        return matching_nodes

if __name__ == '__main__':
    # 示例用法
    db_manager = DatabaseManager()

    # 假设 MinerU 的 middle.json 文件在这里
    mineru_json_path = '../../monkey_ocr_project/mineru_output/LKS32AT08x_UM_v1.37/auto/LKS32AT08x_UM_v1.37_middle.json'
    
    # 确保文件存在
    if not os.path.exists(mineru_json_path):
        print(f"错误: MinerU 的 middle.json 文件未找到: {mineru_json_path}")
        print("请确保您已运行 MinerU 并将文件放置在正确的位置。")
    else:
        db_manager.process_mineru_middle_json(mineru_json_path)

        # 查询数据
        print("\n--- 查询测试 ---")
        query = "SYS_CTRL_REG1"
        vector_results = db_manager.query_vector_db(query)
        print(f"\n向量数据库查询 '{query}' 的结果:")
        print(json.dumps(vector_results, indent=2))

        kg_query = "CONFIG_REG"
        kg_results = db_manager.query_knowledge_graph(kg_query)
        print(f"\n知识图谱查询 '{kg_query}' 的结果:")
        print(kg_results)