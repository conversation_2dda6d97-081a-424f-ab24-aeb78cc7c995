import requests
import json
from typing import Dict, Any, Optional

class LMStudioClient:
    """
    一个用于与本地 LM Studio 服务器交互的客户端。

    该客户端简化了向本地运行的大语言模型（如 monkeyocr-recognition）
    发送请求的过程，并处理响应。
    """
    def __init__(self, base_url: str = "http://localhost:1234/v1"):
        """
        初始化 LMStudioClient。

        Args:
            base_url (str): LM Studio API 的基础 URL。
        """
        self.base_url = base_url
        self.chat_completions_url = f"{self.base_url}/chat/completions"

    def send_request(self, payload: Dict[str, Any]) -> Optional[Dict[str, Any]]:
        """
        向 LM Studio API 发送一个原始请求。

        Args:
            payload (Dict[str, Any]): 要发送到 API 的请求体。

        Returns:
            Optional[Dict[str, Any]]: API 返回的 JSON 响应，如果请求失败则返回 None。
        """
        headers = {"Content-Type": "application/json"}
        try:
            response = requests.post(
                self.chat_completions_url,
                headers=headers,
                data=json.dumps(payload),
                timeout=120  # 设置一个合理的超时时间
            )
            response.raise_for_status()  # 如果响应状态码不是 2xx，则抛出异常
            return response.json()
        except requests.exceptions.RequestException as e:
            print(f"请求 LM Studio 时发生错误: {e}")
            return None

    def get_completion(
        self,
        messages: list,
        model: str = "local-model",
        temperature: float = 0.7,
        max_tokens: int = 2048,
        stream: bool = False
    ) -> Optional[Dict[str, Any]]:
        """
        获取模型的聊天补全响应。

        Args:
            messages (list): 一个消息列表，遵循 OpenAI 的格式。
            model (str): 要使用的模型名称。对于 LM Studio，通常可以保留默认值。
            temperature (float): 控制生成文本的随机性。
            max_tokens (int): 生成的最大 token 数量。
            stream (bool): 是否以流式方式接收响应。

        Returns:
            Optional[Dict[str, Any]]: API 返回的 JSON 响应，如果请求失败则返回 None。
        """
        payload = {
            "model": model,
            "messages": messages,
            "temperature": temperature,
            "max_tokens": max_tokens,
            "stream": stream
        }
        return self.send_request(payload)

    def get_model_response(self, prompt: str, system_message: str = "You are a helpful assistant.") -> Optional[str]:
        """
        一个简化的方法，用于获取单个提示的响应。

        Args:
            prompt (str): 用户的提示。
            system_message (str): 系统消息，用于设定助手的角色。

        Returns:
            Optional[str]: 模型返回的内容字符串，如果失败则返回 None。
        """
        messages = [
            {"role": "system", "content": system_message},
            {"role": "user", "content": prompt}
        ]
        response = self.get_completion(messages)
        if response and "choices" in response and len(response["choices"]) > 0:
            return response["choices"][0]["message"]["content"]
        return None

if __name__ == '__main__':
    # 示例用法
    client = LMStudioClient()

    # 确保你的 LM Studio 服务器正在运行，并且加载了模型
    test_prompt = "你好，你是谁？"
    print(f"向模型发送提示: {test_prompt}")

    response_content = client.get_model_response(test_prompt)

    if response_content:
        print("\n模型响应:")
        print(response_content)
    else:
        print("\n未能从模型获取响应。请检查：")
        print("1. LM Studio 是否正在运行？")
        print("2. 是否已从顶部下拉菜单中选择并加载了模型？")
        print("3. 服务器是否在 http://localhost:1234 上运行？")
