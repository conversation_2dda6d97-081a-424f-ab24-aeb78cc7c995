import json
from typing import Dict, Any, List
from ..clients.lmstudio_client import LMStudioClient
from ..storage.database_manager import DatabaseManager

class RetrievalAgent:
    """
    一个 AutoGen 风格的代理，负责处理用户查询，从混合数据库中检索信息，
    并使用大语言模型生成最终答案。
    """

    def __init__(self, db_manager: DatabaseManager, client: LMStudioClient):
        """
        初始化检索代理。

        Args:
            db_manager (DatabaseManager): 用于访问数据库的管理器。
            client (LMStudioClient): 用于与 LM Studio 通信的客户端。
        """
        self.db_manager = db_manager
        self.client = client
        self.system_message = (
            "You are an expert technical assistant for embedded systems. "
            "Your task is to answer user questions based on the provided context, which is extracted from a chip's technical manual. "
            "The context may include text snippets, table data, and diagram descriptions. "
            "Synthesize this information to provide a clear, accurate, and comprehensive answer. "
            "If the context contains tables (especially register maps), format them nicely using Markdown for readability."
        )

    def _synthesize_context(self, vector_results: Dict, kg_results: List) -> str:
        """
        将从数据库检索到的信息整合成一个发送给大语言模型的上下文提示。
        """
        context = """# Context from Document

"""

        # 1. 添加来自向量数据库的文本
        context += "## Relevant Text Snippets\n\n"
        if vector_results and vector_results.get('documents'):
            for doc_list in vector_results['documents']:
                for doc in doc_list:
                    context += f"- {doc}\n"
        else:
            context += "No relevant text snippets found.\n"
        
        context += "\n---\n"

        # 2. 添加来自知识图谱的结构化数据
        context += "## Relevant Structured Data (from Knowledge Graph)\n\n"
        if kg_results:
            for node_id in kg_results:
                node_data = self.db_manager.graph.nodes[node_id]
                context += f"### Node: {node_id} (Type: {node_data.get('type', 'N/A')})\n"
                # 如果是表格，用 Markdown 格式化
                if node_data.get('type') == 'table' and 'content' in node_data:
                    headers = list(node_data['content']['rows'][0].keys()) if node_data['content']['rows'] else []
                    context += f"| {' | '.join(headers)} |\n"
                    context += f"| {' | '.join(['---'] * len(headers))} |\n"
                    for row in node_data['content']['rows']:
                        context += f"| {' | '.join([str(v) for v in row.values()])} |\n"
                else:
                    context += f"- Data: {json.dumps(node_data, indent=2)}\n"
                context += "\n"
        else:
            context += "No relevant structured data found.\n"

        return context

    def answer_query(self, query: str) -> str:
        """
        处理用户查询并返回答案。

        Args:
            query (str): 用户的问题。

        Returns:
            str: 模型生成的最终答案。
        """
        print(f"--- 收到查询: '{query}' ---")

        # 1. 从数据库检索信息
        print("  - 正在查询向量数据库...")
        vector_results = self.db_manager.query_vector_db(query_text=query, n_results=5)
        
        print("  - 正在查询知识图谱...")
        # 简化：使用查询中的关键词进行图查询
        kg_results = self.db_manager.query_knowledge_graph(query=query)

        # 2. 综合上下文
        print("  - 正在综合上下文...")
        context = self._synthesize_context(vector_results, kg_results)

        # 3. 构建最终的提示
        final_prompt = f"{context}\n\n---\n\n**User Question:** {query}"

        # 4. 调用大语言模型生成答案
        print("  - 正在调用大语言模型生成答案...")
        answer = self.client.get_model_response(
            prompt=final_prompt,
            system_message=self.system_message
        )

        print("--- 查询处理完成 ---")
        return answer if answer else "Sorry, I could not generate an answer based on the available information."

if __name__ == '__main__':
    # 示例用法
    # 此示例需要一个已填充数据的数据库和正在运行的 LM Studio

    # 1. 模拟环境
    class MockDBManager(DatabaseManager):
        def query_vector_db(self, query_text: str, n_results: int = 5) -> Dict:
            return {'documents': [['This register controls the main configuration.']]}
        def query_knowledge_graph(self, query: str) -> List:
            # 模拟返回一个包含寄存器信息的节点
            self.graph.add_node('CONFIG_REG', type='table', content={'rows': [{'Register': 'CONFIG_REG', 'Address': '0x1000'}]})
            return ['CONFIG_REG']

    class MockLMStudioClient(LMStudioClient):
        def get_model_response(self, prompt: str, system_message: str) -> str:
            return f"This is the final answer based on the provided context about: {prompt[:100]}..."

    # 2. 初始化代理
    db_manager = MockDBManager()
    client = MockLMStudioClient()
    agent = RetrievalAgent(db_manager=db_manager, client=client)

    # 3. 运行查询
    user_question = "How do I configure the device?"
    final_answer = agent.answer_query(user_question)

    # 4. 打印结果
    print(f"\n最终答案:\n{final_answer}")
