

import json
import os
import numpy as np
# 猴子补丁：在 chromadb 加载前，手动修复 numpy 的版本兼容性问题
# 将已移除的 np.float_ 指向 np.float64
np.float_ = np.float64

from knowledge_base_agent.storage.database_manager import DatabaseManager
from knowledge_base_agent.agents.retrieval_agent import RetrievalAgent
from knowledge_base_agent.clients.lmstudio_client import LMStudioClient # 确保导入 LMStudioClient

def run_end_to_end_test():
    """
    执行完整的端到端测试。
    """
    # MinerU 输出的 middle.json 文件路径
    mineru_json_path = 'monkey_ocr_project/mineru_output/LKS32AT08x_UM_v1.37/auto/LKS32AT08x_UM_v1.37_middle.json'
    test_query = "What is the function of the SYS_CTRL_REG1 register?"

    print("--- [1/2] 开始处理 MinerU 输出并存储数据 ---")
    if not os.path.exists(mineru_json_path):
        print(f"错误: MinerU 的 middle.json 文件未找到: {mineru_json_path}")
        print("请确保您已手动运行 MinerU 并将文件放置在正确的位置。")
        return

    # 1. 初始化数据库管理器并处理 MinerU 输出
    db_manager = DatabaseManager()
    db_manager.process_mineru_middle_json(mineru_json_path)
    print("--- 数据存储完成 ---")

    print("\n--- [2/2] 开始查询与回答 ---")
    # 2. 使用检索代理进行查询
    # 确保 LMStudioClient 被正确初始化并传递给 RetrievalAgent
    lm_client = LMStudioClient() # 实例化 LMStudioClient
    retrieval_agent = RetrievalAgent(db_manager=db_manager, client=lm_client)
    final_answer = retrieval_agent.answer_query(test_query)

    print("\n--- 测试完成 --- \n")
    print(f"测试问题: {test_query}")
    print("\n最终答案:")
    print(final_answer)

if __name__ == '__main__':
    # 确保您的 LM Studio 服务器正在运行，并且加载了 monkeyocr-recognition 模型
    print("=================================================")
    print("          开始端到端知识库代理测试          ")
    print("=================================================")
    print("重要提示: 此过程将调用本地大模型进行大量处理，")
    print("可能需要很长时间。请确保 LM Studio 已正确配置。\n")
    
    run_end_to_end_test()
