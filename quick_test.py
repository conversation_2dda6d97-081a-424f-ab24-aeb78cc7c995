#!/usr/bin/env python3
"""
快速单智能体测试脚本
"""
import asyncio
import sys
import os

sys.path.append(os.path.dirname(os.path.abspath(__file__)))
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from config.models_config import get_agent_model_client
from config.agents_config import AGENT_SYSTEM_PROMPTS, AGENT_CAPABILITIES
from agents import DocumentManager, TechnicalExpert, CodeEngineer, SystemArchitect, Coordinator

async def quick_test_agent(agent_name: str, test_query: str):
    """快速测试单个智能体"""
    print(f"🧪 快速测试 {agent_name}")
    print(f"❓ 查询: {test_query}")
    print("-" * 50)
    
    try:
        # 初始化智能体
        model_client = get_agent_model_client(agent_name)
        system_message = AGENT_SYSTEM_PROMPTS[agent_name]
        capabilities = AGENT_CAPABILITIES[agent_name]
        
        if agent_name == "DocumentManager":
            agent = DocumentManager(model_client, system_message, capabilities)
        elif agent_name == "TechnicalExpert":
            agent = TechnicalExpert(model_client, system_message, capabilities)
        elif agent_name == "CodeEngineer":
            agent = CodeEngineer(model_client, system_message, capabilities)
        elif agent_name == "SystemArchitect":
            agent = SystemArchitect(model_client, system_message, capabilities)
        elif agent_name == "Coordinator":
            agent = Coordinator(model_client, system_message, capabilities)
        else:
            print(f"❌ 未知智能体: {agent_name}")
            return
        
        # 执行测试
        start_time = asyncio.get_event_loop().time()
        response = await agent.handle_message(test_query)
        end_time = asyncio.get_event_loop().time()
        
        print(f"💡 回答:\n{response}")
        print(f"\n⏱️  响应时间: {end_time - start_time:.2f}秒")
        print(f"✅ 测试完成")
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")

async def main():
    """主函数"""
    if len(sys.argv) < 3:
        print("使用方法: python quick_test.py <智能体名称> <测试问题>")
        print("\n可用智能体:")
        print("  DocumentManager   - 文档管理员")
        print("  TechnicalExpert   - 技术专家")
        print("  CodeEngineer      - 代码工程师")
        print("  SystemArchitect   - 系统架构师")
        print("  Coordinator       - 协调员")
        print("\n示例:")
        print("  python quick_test.py CodeEngineer '生成GPIO初始化代码'")
        print("  python quick_test.py TechnicalExpert '如何优化ADC性能？'")
        return
    
    agent_name = sys.argv[1]
    test_query = sys.argv[2]
    
    await quick_test_agent(agent_name, test_query)

if __name__ == "__main__":
    asyncio.run(main())
