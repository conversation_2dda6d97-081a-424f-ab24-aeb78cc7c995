#!/usr/bin/env python3
"""
LM Studio 模型管理工具
用于添加、查看和管理LM Studio模型配置
"""
import asyncio
import aiohttp
from typing import Dict, Any, List
from LM_Studio import LMSTUDIO_MODEL_REGISTRY, get_lmstudio_client

async def list_available_models():
    """列出LM Studio中可用的模型"""
    try:
        async with aiohttp.ClientSession() as session:
            async with session.get("http://localhost:1234/v1/models") as response:
                if response.status == 200:
                    models = await response.json()
                    return [model['id'] for model in models.get('data', [])]
                else:
                    print(f"❌ 无法获取模型列表: HTTP {response.status}")
                    return []
    except Exception as e:
        print(f"❌ 连接LM Studio失败: {e}")
        return []

def list_registered_models():
    """列出已注册的模型"""
    print("📋 已注册的模型:")
    print("=" * 60)

    for model_id, config in LMSTUDIO_MODEL_REGISTRY.items():
        aliases = config.get("aliases", [])
        model_info = config.get("model_info")
        params = config.get("default_params", {})

        print(f"🤖 模型ID: {model_id}")
        print(f"   别名: {', '.join(aliases)}")
        if hasattr(model_info, 'vision'):
            print(f"   视觉支持: {'✅' if model_info.vision else '❌'}")
            print(f"   函数调用: {'✅' if model_info.function_calling else '❌'}")
            print(f"   结构化输出: {'✅' if model_info.structured_output else '❌'}")
        else:
            print(f"   模型信息: {model_info}")
        print(f"   默认参数: {params}")
        print()

def generate_model_config(model_id: str, aliases: List[str] = None, **kwargs) -> str:
    """生成新模型的配置代码"""
    if aliases is None:
        aliases = [model_id.replace("-", "_"), model_id.split("-")[0]]

    # 根据模型名称推断特性
    vision = "vl" in model_id.lower() or "vision" in model_id.lower()
    function_calling = "embed" not in model_id.lower()  # 嵌入模型通常不支持函数调用
    structured_output = function_calling  # 通常与函数调用能力相关
    json_output = function_calling

    # 根据模型大小设置默认参数
    if "235b" in model_id or "72b" in model_id:
        max_tokens = 4096
        temperature = 0.5
    elif "32b" in model_id:
        max_tokens = 2048
        temperature = 0.7
    elif "embed" in model_id:
        max_tokens = 512
        temperature = 0.0
    else:
        max_tokens = 1024
        temperature = 0.6

    config_code = f'''    "{model_id}": {{
        "aliases": {aliases},
        "model_info": ModelInfo(
            vision={vision},
            function_calling={function_calling},
            structured_output={structured_output},
            json_output={json_output},
            family=ModelFamily.UNKNOWN,
        ),
        "default_params": {{"temperature": {temperature}, "max_tokens": {max_tokens}}},
    }},'''

    return config_code

async def check_model_compatibility(model_id: str):
    """检查模型兼容性"""
    try:
        print(f"🔍 测试模型: {model_id}")
        client = get_lmstudio_client(model_id)

        from autogen_core.models._types import UserMessage
        messages = [UserMessage(content="你好", source="user")]

        result = await client.create(messages)
        print(f"✅ 模型 {model_id} 测试成功")
        print(f"   响应: {result.content[:100]}...")
        return True

    except Exception as e:
        print(f"❌ 模型 {model_id} 测试失败: {e}")
        return False

async def main():
    """主函数"""
    print("🚀 LM Studio 模型管理工具")
    print("=" * 50)

    # 1. 列出LM Studio中可用的模型
    print("\n1️⃣ LM Studio中可用的模型:")
    available_models = await list_available_models()
    if available_models:
        for i, model in enumerate(available_models, 1):
            status = "✅ 已注册" if model in LMSTUDIO_MODEL_REGISTRY else "⚠️  未注册"
            print(f"   {i}. {model} - {status}")
    else:
        print("   无可用模型或连接失败")

    print("\n" + "=" * 50)

    # 2. 列出已注册的模型
    print("\n2️⃣ 已注册的模型配置:")
    list_registered_models()

    # 3. 为未注册的模型生成配置
    unregistered = [m for m in available_models if m not in LMSTUDIO_MODEL_REGISTRY]
    if unregistered:
        print("3️⃣ 未注册模型的建议配置:")
        print("=" * 50)
        print("将以下代码添加到 LMSTUDIO_MODEL_REGISTRY 中:")
        print()
        for model in unregistered:
            print(generate_model_config(model))
            print()

    # 4. 测试已注册的模型
    print("4️⃣ 测试已注册的模型:")
    print("=" * 50)
    for model_id in LMSTUDIO_MODEL_REGISTRY.keys():
        if model_id in available_models:
            await check_model_compatibility(model_id)
        else:
            print(f"⚠️  模型 {model_id} 在LM Studio中不可用")

if __name__ == "__main__":
    asyncio.run(main())
