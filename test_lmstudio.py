#!/usr/bin/env python3
"""
测试LM Studio连接的简单脚本
"""
import asyncio
import aiohttp
from LM_Studio import get_lmstudio_client
from autogen_core.models._types import UserMessage

async def test_lm_studio_connection():
    """测试LM Studio连接"""
    try:
        print("🔍 测试LM Studio连接...")
        
        # 测试LM Studio服务器是否运行
        async with aiohttp.ClientSession() as session:
            try:
                async with session.get("http://localhost:1234/v1/models") as response:
                    if response.status == 200:
                        models = await response.json()
                        print(f"✅ LM Studio服务器运行正常")
                        print(f"📋 可用模型: {[model['id'] for model in models.get('data', [])]}")
                    else:
                        print(f"⚠️  LM Studio服务器响应异常: {response.status}")
                        return False
            except Exception as e:
                print(f"❌ 无法连接到LM Studio服务器: {e}")
                print("💡 请确保:")
                print("   1. LM Studio已启动")
                print("   2. 已加载模型")
                print("   3. 服务器运行在 http://localhost:1234")
                return False
        
        # 测试客户端创建
        print("\n🔧 测试客户端创建...")
        client = get_lmstudio_client("qwen3-32b")
        print(f"✅ 客户端创建成功: {client.model}")
        
        # 测试简单对话
        print("\n💬 测试简单对话...")
        messages = [UserMessage(content="你好，请简单介绍一下你自己。", source="user")]
        
        result = await client.create(messages)
        print(f"✅ 对话测试成功!")
        print(f"📝 回复: {result.content}")
        print(f"📊 使用情况: {result.usage}")
        
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

async def main():
    """主函数"""
    print("🚀 开始LM Studio连接测试\n")
    
    success = await test_lm_studio_connection()
    
    if success:
        print("\n🎉 所有测试通过! LM Studio集成正常工作。")
    else:
        print("\n💥 测试失败，请检查LM Studio配置。")

if __name__ == "__main__":
    asyncio.run(main())
