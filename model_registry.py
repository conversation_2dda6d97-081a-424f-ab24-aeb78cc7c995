from autogen_ext.models.ollama import OllamaChatCompletionClient
from autogen_core.models import ModelInfo, ModelFamily

OLLAMA_HOST = "http://localhost:11434"

# 用 Ollama 实际 tag 做 key
MODEL_REGISTRY = {
    "qwen3:14b-fp16": {
        "aliases": ["qwen3", "qwen3:14b"],  # ← 支持别名映射
        "model_info": ModelInfo(
            vision=False,
            function_calling=True,
            structured_output=True,
            json_output=True,
            family=ModelFamily.UNKNOWN,
        ),
        "default_params": {"temperature": 0.2},
    },
    "mistral-small3.1:24b-instruct-2503-fp16": {
        "aliases": ["mistral", "mistral-small3.1:24b"],
        "model_info": ModelInfo(
            vision=True,
            function_calling=True,
            structured_output=False,
            json_output=False,
            family=ModelFamily.UNKNOWN,
        ),
        "default_params": {"temperature": 0.7},
    },
}

# 创建别名反查表（别名 → 实际 tag）
_alias_to_tag = {
    alias: tag
    for tag, config in MODEL_REGISTRY.items()
    for alias in config.get("aliases", []) + [tag]
}

def get_model_config(name: str = "qwen3") -> dict:
    actual_tag = _alias_to_tag.get(name)
    if not actual_tag:
        raise ValueError(f"模型别名 '{name}' 未找到，请确认注册！")
    return MODEL_REGISTRY[actual_tag]

def get_model_client(name: str = "qwen3") -> OllamaChatCompletionClient:
    cfg = get_model_config(name)
    return OllamaChatCompletionClient(
        model=_alias_to_tag[name],  # 实际 Ollama 的模型名称
        host=OLLAMA_HOST,
        model_info=cfg["model_info"],
        default_params=cfg["default_params"],
    )
