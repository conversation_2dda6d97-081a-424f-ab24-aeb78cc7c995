#!/usr/bin/env python3
# hybrid_ingest_search.py
# 适用环境：Python 3.9+ / MongoDB Community 8.x / Ollama 本地服务

import argparse, glob, json, pathlib, pprint, time
from typing import List, Dict, Any

import requests
import numpy as np
from pymongo import MongoClient, ASCENDING, TEXT

# ──────────────────────────── 配  置 ────────────────────────────
DOC_DIR    = "/ABSOLUTE/PATH/TO/full.md"          # 可指向单一 .md 或目录
MONGO_URI  = "mongodb://localhost:27017"
DB_NAME    = "LKS32AT08x_UM_v1_37_Autogen"
EMB_MODEL  = "nomic-embed-text"                   # 先执行 `ollama pull ...`
OLLAMA_URL = "http://localhost:11434"             # Ollama 服务地址
# ────────────────────────────────────────────────────────────────

db = MongoClient(MONGO_URI)[DB_NAME]

# ---------- 嵌入向量 ----------
def embed(text: str) -> List[float]:
    """调用 Ollama embeddings API，返回 768维向量"""
    r = requests.post(f"{OLLAMA_URL}/api/embeddings",
                      json={"model": EMB_MODEL, "prompt": text})
    r.raise_for_status()
    return r.json()["embedding"]

# ---------- 文档导入 ----------
def load_and_insert_md(path: str):
    paths = [path] if path.endswith(".md") else \
            glob.glob(f"{path}/**/*.md", recursive=True)
    for p in paths:
        chap_id = pathlib.Path(p).stem
        content = pathlib.Path(p).read_text(encoding="utf-8")
        db.chapters.insert_one({
            "chapter_id": chap_id,
            "title":      chap_id,
            "content":    content,
            "embedding":  embed(content)
        })
    print(f"[MD] 导入 {len(paths)} 篇 Markdown")

def load_and_insert_json(path: str):
    n = 0
    for js in glob.glob(f"{path}/**/*register*.json", recursive=True):
        regs = json.load(open(js, encoding="utf-8"))
        for r in regs:
            r["embedding"] = embed(r.get("description", ""))
        if regs:
            db.registers.insert_many(regs)
            n += len(regs)
    print(f"[JSON] 导入 {n} 条寄存器记录")

# ---------- 索引（仅 text & BTree） ----------
def create_indexes():
    db.chapters.create_index([("title", TEXT), ("content", TEXT)],
                             name="text_idx", default_language="none")
    db.chapters.create_index([("chapter_id", ASCENDING)], name="chap_idx")
    print("[INDEX] text_idx & chap_idx 创建完毕")

# ---------- Hybrid 查询 ----------
def _cosine(mat: np.ndarray, vec: np.ndarray):
    mat = mat / np.linalg.norm(mat, axis=1, keepdims=True)
    vec = vec / np.linalg.norm(vec)
    return np.dot(mat, vec)                  # shape (N,)

def hybrid_search(chap_regex: str, keyword: str, topk: int = 5):
    # Mongo 端目录 + text 过滤
    pipeline = [
        {"$match": {"chapter_id": {"$regex": chap_regex},
                    "$text": {"$search": keyword}}},
        {"$project": {"chapter_id":1,"title":1,"content":1,
                      "embedding":1,"ts": {"$meta":"textScore"}}}
    ]
    docs = list(db.chapters.aggregate(pipeline))
    if not docs:
        return []

    # 客户端向量相似度
    mat = np.vstack([d["embedding"] for d in docs]).astype("float32")
    v = np.asarray(embed(keyword), dtype="float32")
    vscore = _cosine(mat, v)

    ts = np.asarray([d["ts"] for d in docs], dtype="float32")
    rrf = 1/(1+ts) + 1/(60+vscore)           # 简化 RRF
    order = np.argsort(rrf)[:topk]           # 越小越相关

    return [{k:v for k,v in docs[i].items()
             if k not in ("embedding","ts")} for i in order]

# ---------- CLI ----------
def main():
    p = argparse.ArgumentParser(description="Hybrid RAG – MongoDB CE")
    sub = p.add_subparsers(dest="cmd", required=True)

    ing = sub.add_parser("ingest", help="导入并建索引")
    ing.add_argument("--dir", default=DOC_DIR)

    q = sub.add_parser("query", help="混合检索")
    q.add_argument("--chapter", default="^14")
    q.add_argument("--kw", required=True)
    q.add_argument("--k", type=int, default=5)

    a = p.parse_args()
    t0 = time.time()

    if a.cmd == "ingest":
        load_and_insert_md(a.dir)
        load_and_insert_json(a.dir)
        create_indexes()
        print(f"✓ ingest 完成，用时 {time.time()-t0:.1f}s")
    else:
        hits = hybrid_search(a.chapter, a.kw, a.k)
        pprint.pp(hits, depth=2, compact=True)

if __name__ == "__main__":
    main()
