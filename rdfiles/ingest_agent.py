# ingest_agent.py – AutoGen 0.5.5  + MCP  + Ollama
# --------------------------------------------------
# 作用：
#   1. 通过 MCP‑filesystem 列出并读取本地 Markdown / JSON 片段；
#   2. 通过 MCP‑mongo‑server 把片段及其向量写入 MongoDB；
# 使用方法：
#   $ conda activate autogen           # Python ≥3.11
#   $ python ingest_agent.py           # 首次运行将自动启动 2 个 MCP 进程
# 依赖：
#   pip install autogen‑core==0.5.5 autogen-agentchat==0.5.5 autogen-ext==0.5.5 \
#               ollama pymongo mcp tiktoken requests numpy "anyio<4.10"
#   npm ≥ 9 ；不需全局安装 mcp‑servers，脚本用 npx 调用

import asyncio, os, textwrap, pathlib, json, time
from autogen_agentchat.agents import AssistantAgent, UserProxyAgent  # 0.5.5 路径
from autogen_ext.tools.mcp import StdioServerParams, mcp_server_tools
from autogen_ext.models.ollama import OllamaChatCompletionClient
from autogen_core.models import ModelInfo, ModelFamily 
import datetime as _dt
# ---------- 可按需修改 ----------
DOC_ROOT  = pathlib.Path(__file__).parent / "doc"          # 只允许访问此目录
_db_suffix = _dt.datetime.now().strftime("%Y%m%d_%H%M%S")
MONGO_URI = f"mongodb://localhost:27017/chip_manual_{_db_suffix}" # 目标数据库
#MONGO_URI = "mongodb://localhost:27017/chip_manual"        
EMBED_TAG = "nomic-embed-text"                              # Ollama 向量模型
CHAT_TAG  = "qwen3:14b-fp16"                                       # Ollama LLM
OLLAMA_URL = "http://localhost:11434"                       # Ollama 本地服务
# --------------------------------

# MCP server params（用 npx 直接拉取，不依赖 pnpm 全局 bin）
FS_SERVER    = StdioServerParams(
    command="npx",
    args=["--yes", "mcp-filesystem", str(DOC_ROOT)],
)
MONGO_SERVER = StdioServerParams(
    command="npx",
    args=["--yes", "mcp-mongo-server", MONGO_URI, "--log-level", "debug"],
)

# --- SYSTEM_PROMPT 更严格 ---
SYSTEM_PROMPT = """
你是文档导入代理，必须完整执行以下流程：
① 调用 list_dir(path=".", recursive=true) 获取文件清单；
② 对 *.md  文件：连续调用 read_file_chunk(path, cursor) 拼接全文，
   之后 insertOne(collection="chapters", document={chapter_id, title, content});
③ 对 *register*.json 文件：read_file_chunk 读取后直接
   insertMany(collection="registers", documents=<json数组>);
④ 每写入 1 篇/批都要打印 “INSERT OK”。
全部步骤完成后回复 **唯一** 的 “DONE”。如有任何错误回复 “ERROR: <原因>” 并停止。
若步骤未走完严禁回复 DONE。
""".strip()

async def build_agent() -> AssistantAgent:
    # 取得两套 MCP 工具
    fs_tools    = await mcp_server_tools(FS_SERVER)
    mongo_tools = await mcp_server_tools(MONGO_SERVER)

    # Ollama LLM + embedding
    ollama_client = OllamaChatCompletionClient(
        model="qwen3:14b-fp16",                                # 你的本地 tag
        client_host=f"{OLLAMA_URL}/v1",
        api_key="ollama",
        model_info=ModelInfo(                                  # ← 手动补全
        vision=False,
        function_calling=True,
        structured_output=True,
        json_output=True,
        family=ModelFamily.UNKNOWN,
    ),
    default_params={"temperature": 0},
    )

    agent = AssistantAgent(
        name="DocImporter",
        model_client=ollama_client,
        tools=fs_tools + mongo_tools,
        system_message=SYSTEM_PROMPT,
        reflect_on_tool_use=True,
    )
    return agent


# ---------------- main -----------------
async def main():
    agent = await build_agent()

    # 直接驱动助手，无需 UserProxyAgent
    await agent.run(task="开始导入文档")      # ⬅ 只保留这一行

    print("✅ 导入流程触发完毕")

if __name__ == "__main__":
    import asyncio
    asyncio.run(main())


# async def main():
#     agent = await build_agent()
#     user  = UserProxyAgent(name="User")

#     t0 = time.time()
#     await user.run(task="开始导入文档")
#     print(f"\n✅ 导入流程已触发，用时 {time.time()-t0:.1f}s")

# if __name__ == "__main__":
#     asyncio.run(main())