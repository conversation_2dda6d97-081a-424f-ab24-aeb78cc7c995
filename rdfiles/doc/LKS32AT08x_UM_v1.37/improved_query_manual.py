#!/usr/bin/env python3
import sys
import re
import json
import argparse
import logging
from pymongo import MongoClient
from bson.json_util import dumps
from tabulate import tabulate

# 设置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler("query_log.txt"),
        logging.StreamHandler()
    ]
)

# 连接到MongoDB
try:
    client = MongoClient('localhost', 27017)
    db = client['LKS32AT08x_UM_v1_37']
except Exception as e:
    logging.error(f"连接MongoDB失败: {e}")
    print(f"错误: 连接数据库失败 - {e}")
    sys.exit(1)

def print_help():
    """打印帮助信息"""
    print("LKS32AT08x_UM_v1.37 芯片手册查询工具")
    print("用法:")
    print("  python improved_query_manual.py [选项] [查询内容]")
    print("\n选项:")
    print("  --help                显示此帮助信息")
    print("  --list-chapters       列出所有章节")
    print("  --list-tables         列出所有表格")
    print("  --list-registers      列出所有寄存器")
    print("  --chapter [ID]        查询指定章节")
    print("  --table [ID]          查询指定表格")
    print("  --register [ID]       查询指定寄存器")
    print("  --search [关键词]     全文搜索关键词")
    print("  --search-register [关键词] 搜索寄存器名称")
    print("  --search-table [关键词]    搜索表格标题")
    print("  --register-fuzzy [关键词]  模糊搜索寄存器")
    print("  --export [文件名]     导出查询结果到JSON文件")
    print("  --stats               显示数据库统计信息")
    print("  --page [页码]         分页显示结果 (默认: 1)")
    print("  --page-size [大小]    每页显示的结果数量 (默认: 20)")
    print("\n示例:")
    print("  python improved_query_manual.py --list-chapters")
    print("  python improved_query_manual.py --chapter 5.1")
    print("  python improved_query_manual.py --search 时钟")
    print("  python improved_query_manual.py --register-fuzzy MCPWM")

def list_chapters(page=1, page_size=20):
    """列出所有章节"""
    try:
        total = db.chapters.count_documents({})
        skip = (page - 1) * page_size
        chapters = db.chapters.find({}, {"chapter_id": 1, "title": 1}).sort("chapter_id", 1).skip(skip).limit(page_size)

        print(f"章节列表 (第 {page} 页，共 {(total + page_size - 1) // page_size} 页):")

        data = []
        for chapter in chapters:
            data.append([chapter['chapter_id'], chapter['title']])

        if data:
            print(tabulate(data, headers=["章节ID", "标题"], tablefmt="grid"))
            print(f"\n显示 {len(data)} 条记录，共 {total} 条")
        else:
            print("没有找到章节")
    except Exception as e:
        logging.error(f"列出章节失败: {e}")
        print(f"错误: 列出章节失败 - {e}")

def list_tables(page=1, page_size=20):
    """列出所有表格"""
    try:
        total = db.tables.count_documents({})
        skip = (page - 1) * page_size
        tables = db.tables.find({}, {"table_id": 1, "title": 1, "chapter_id": 1}).sort("table_id", 1).skip(skip).limit(page_size)

        print(f"表格列表 (第 {page} 页，共 {(total + page_size - 1) // page_size} 页):")

        data = []
        for table in tables:
            chapter_id = table.get('chapter_id', '')
            data.append([table['table_id'], table['title'], chapter_id])

        if data:
            print(tabulate(data, headers=["表格ID", "标题", "所属章节"], tablefmt="grid"))
            print(f"\n显示 {len(data)} 条记录，共 {total} 条")
        else:
            print("没有找到表格")
    except Exception as e:
        logging.error(f"列出表格失败: {e}")
        print(f"错误: 列出表格失败 - {e}")

def list_registers(page=1, page_size=20):
    """列出所有寄存器"""
    try:
        total = db.registers.count_documents({})
        skip = (page - 1) * page_size
        registers = db.registers.find({}, {"register_id": 1, "name": 1, "address": 1, "chapter_id": 1}).sort("register_id", 1).skip(skip).limit(page_size)

        print(f"寄存器列表 (第 {page} 页，共 {(total + page_size - 1) // page_size} 页):")

        data = []
        for register in registers:
            address = register.get('address', '')
            chapter_id = register.get('chapter_id', '')
            data.append([register['register_id'], f"{register['name']}寄存器", address, chapter_id])

        if data:
            print(tabulate(data, headers=["寄存器ID", "名称", "地址", "所属章节"], tablefmt="grid"))
            print(f"\n显示 {len(data)} 条记录，共 {total} 条")
        else:
            print("没有找到寄存器")
    except Exception as e:
        logging.error(f"列出寄存器失败: {e}")
        print(f"错误: 列出寄存器失败 - {e}")

def get_chapter(chapter_id):
    """获取指定章节内容"""
    try:
        chapter = db.chapters.find_one({"chapter_id": chapter_id})
        if chapter:
            print(f"章节 {chapter['chapter_id']} - {chapter['title']}")
            print("\n".join(chapter['content']))

            # 显示相关表格和寄存器
            if 'related_tables' in chapter and chapter['related_tables']:
                print("\n相关表格:")
                for table_id in chapter['related_tables']:
                    table = db.tables.find_one({"table_id": table_id})
                    if table:
                        print(f"  {table['table_id']} - {table['title']}")

            if 'related_registers' in chapter and chapter['related_registers']:
                print("\n相关寄存器:")
                for register_id in chapter['related_registers']:
                    register = db.registers.find_one({"register_id": register_id})
                    if register:
                        print(f"  {register['register_id']} - {register['name']}寄存器")
        else:
            print(f"未找到章节 {chapter_id}")
    except Exception as e:
        logging.error(f"获取章节 {chapter_id} 失败: {e}")
        print(f"错误: 获取章节失败 - {e}")

def get_table(table_id):
    """获取指定表格内容"""
    try:
        table = db.tables.find_one({"table_id": table_id})
        if table:
            print(f"表格 {table['table_id']} - {table['title']}")

            # 获取所属章节信息
            chapter_id = table.get('chapter_id', '')
            if chapter_id:
                chapter = db.chapters.find_one({"chapter_id": chapter_id})
                if chapter:
                    print(f"所属章节: {chapter_id} - {chapter['title']}")

            # 打印表格内容
            headers = table['headers']
            rows = table['rows']

            if headers and rows:
                print(tabulate(rows, headers=headers, tablefmt="grid"))
            elif headers:
                print(f"表头: {', '.join(headers)}")
                print("表格内容为空")
            else:
                print("表格为空")
        else:
            print(f"未找到表格 {table_id}")
    except Exception as e:
        logging.error(f"获取表格 {table_id} 失败: {e}")
        print(f"错误: 获取表格失败 - {e}")

def get_register(register_id):
    """获取指定寄存器内容"""
    try:
        register = db.registers.find_one({"register_id": register_id})
        if register:
            print(f"寄存器 {register['register_id']} - {register['name']}寄存器")

            # 获取所属章节信息
            chapter_id = register.get('chapter_id', '')
            if chapter_id:
                chapter = db.chapters.find_one({"chapter_id": chapter_id})
                if chapter:
                    print(f"所属章节: {chapter_id} - {chapter['title']}")

            # 显示地址
            address = register.get('address', '')
            if address:
                print(f"地址: {address}")

            # 显示位定义
            bits = register.get('bits', [])
            if bits:
                print("\n位定义:")
                bit_data = []
                for bit in bits:
                    if bit['high'] == bit['low']:
                        bit_range = f"[{bit['high']}]"
                    else:
                        bit_range = f"[{bit['high']}:{bit['low']}]"
                    bit_data.append([bit_range, bit['name'], bit['description']])

                print(tabulate(bit_data, headers=["位", "名称", "描述"], tablefmt="grid"))

            # 显示描述
            print("\n描述:")
            description = register.get('description', '')
            if description:
                if isinstance(description, list):
                    print("\n".join(description))
                else:
                    print(description)
            else:
                print("无描述")
        else:
            print(f"未找到寄存器 {register_id}")

            # 尝试模糊匹配
            similar_registers = db.registers.find({"register_id": {"$regex": f".*{register_id}.*"}})
            similar_list = list(similar_registers)
            if similar_list:
                print("\n您可能想查找以下寄存器:")
                for reg in similar_list:
                    print(f"  {reg['register_id']} - {reg['name']}寄存器")
    except Exception as e:
        logging.error(f"获取寄存器 {register_id} 失败: {e}")
        print(f"错误: 获取寄存器失败 - {e}")

def search_content(keyword, page=1, page_size=20):
    """全文搜索关键词"""
    try:
        # 搜索章节
        chapter_query = {"$or": [
            {"title": {"$regex": keyword, "$options": "i"}},
            {"content": {"$regex": keyword, "$options": "i"}}
        ]}
        chapter_total = db.chapters.count_documents(chapter_query)
        chapter_skip = (page - 1) * page_size if page > 0 else 0
        chapters = db.chapters.find(chapter_query).skip(chapter_skip).limit(page_size)

        # 搜索表格
        table_query = {"$or": [
            {"title": {"$regex": keyword, "$options": "i"}},
            {"headers": {"$regex": keyword, "$options": "i"}},
            {"rows": {"$regex": keyword, "$options": "i"}}
        ]}
        table_total = db.tables.count_documents(table_query)
        table_skip = (page - 1) * page_size if page > 0 else 0
        tables = db.tables.find(table_query).skip(table_skip).limit(page_size)

        # 搜索寄存器
        register_query = {"$or": [
            {"name": {"$regex": keyword, "$options": "i"}},
            {"description": {"$regex": keyword, "$options": "i"}}
        ]}
        register_total = db.registers.count_documents(register_query)
        register_skip = (page - 1) * page_size if page > 0 else 0
        registers = db.registers.find(register_query).skip(register_skip).limit(page_size)

        print(f"搜索结果 - 关键词: '{keyword}'")

        print(f"\n章节匹配 (共 {chapter_total} 条):")
        chapter_data = []
        for chapter in chapters:
            chapter_data.append([chapter['chapter_id'], chapter['title']])

        if chapter_data:
            print(tabulate(chapter_data, headers=["章节ID", "标题"], tablefmt="grid"))
        else:
            print("没有找到匹配的章节")

        print(f"\n表格匹配 (共 {table_total} 条):")
        table_data = []
        for table in tables:
            table_data.append([table['table_id'], table['title']])

        if table_data:
            print(tabulate(table_data, headers=["表格ID", "标题"], tablefmt="grid"))
        else:
            print("没有找到匹配的表格")

        print(f"\n寄存器匹配 (共 {register_total} 条):")
        register_data = []
        for register in registers:
            register_data.append([register['register_id'], f"{register['name']}寄存器"])

        if register_data:
            print(tabulate(register_data, headers=["寄存器ID", "名称"], tablefmt="grid"))
        else:
            print("没有找到匹配的寄存器")
    except Exception as e:
        logging.error(f"搜索关键词 {keyword} 失败: {e}")
        print(f"错误: 搜索失败 - {e}")

def search_register(keyword, page=1, page_size=20):
    """搜索寄存器名称"""
    try:
        query = {"name": {"$regex": keyword, "$options": "i"}}
        total = db.registers.count_documents(query)
        skip = (page - 1) * page_size
        registers = db.registers.find(query).skip(skip).limit(page_size)

        print(f"寄存器搜索结果 - 关键词: '{keyword}' (第 {page} 页，共 {(total + page_size - 1) // page_size} 页):")

        data = []
        for register in registers:
            address = register.get('address', '')
            chapter_id = register.get('chapter_id', '')
            data.append([register['register_id'], f"{register['name']}寄存器", address, chapter_id])

        if data:
            print(tabulate(data, headers=["寄存器ID", "名称", "地址", "所属章节"], tablefmt="grid"))
            print(f"\n显示 {len(data)} 条记录，共 {total} 条")
        else:
            print("没有找到匹配的寄存器")
    except Exception as e:
        logging.error(f"搜索寄存器 {keyword} 失败: {e}")
        print(f"错误: 搜索寄存器失败 - {e}")

def search_table(keyword, page=1, page_size=20):
    """搜索表格标题"""
    try:
        query = {"title": {"$regex": keyword, "$options": "i"}}
        total = db.tables.count_documents(query)
        skip = (page - 1) * page_size
        tables = db.tables.find(query).skip(skip).limit(page_size)

        print(f"表格搜索结果 - 关键词: '{keyword}' (第 {page} 页，共 {(total + page_size - 1) // page_size} 页):")

        data = []
        for table in tables:
            chapter_id = table.get('chapter_id', '')
            data.append([table['table_id'], table['title'], chapter_id])

        if data:
            print(tabulate(data, headers=["表格ID", "标题", "所属章节"], tablefmt="grid"))
            print(f"\n显示 {len(data)} 条记录，共 {total} 条")
        else:
            print("没有找到匹配的表格")
    except Exception as e:
        logging.error(f"搜索表格 {keyword} 失败: {e}")
        print(f"错误: 搜索表格失败 - {e}")

def register_fuzzy_search(keyword, page=1, page_size=20):
    """模糊搜索寄存器"""
    try:
        query = {"$or": [
            {"register_id": {"$regex": f".*{keyword}.*", "$options": "i"}},
            {"name": {"$regex": f".*{keyword}.*", "$options": "i"}}
        ]}
        total = db.registers.count_documents(query)
        skip = (page - 1) * page_size
        registers = db.registers.find(query).skip(skip).limit(page_size)

        print(f"寄存器模糊搜索结果 - 关键词: '{keyword}' (第 {page} 页，共 {(total + page_size - 1) // page_size} 页):")

        data = []
        for register in registers:
            address = register.get('address', '')
            chapter_id = register.get('chapter_id', '')
            data.append([register['register_id'], f"{register['name']}寄存器", address, chapter_id])

        if data:
            print(tabulate(data, headers=["寄存器ID", "名称", "地址", "所属章节"], tablefmt="grid"))
            print(f"\n显示 {len(data)} 条记录，共 {total} 条")
        else:
            print("没有找到匹配的寄存器")
    except Exception as e:
        logging.error(f"模糊搜索寄存器 {keyword} 失败: {e}")
        print(f"错误: 模糊搜索寄存器失败 - {e}")

def export_to_json(filename, data):
    """导出数据到JSON文件"""
    try:
        with open(filename, 'w', encoding='utf-8') as f:
            f.write(dumps(data, indent=2, ensure_ascii=False))
        print(f"数据已导出到 {filename}")
    except Exception as e:
        logging.error(f"导出数据到 {filename} 失败: {e}")
        print(f"错误: 导出数据失败 - {e}")

def show_stats():
    """显示数据库统计信息"""
    try:
        chapters_count = db.chapters.count_documents({})
        sections_count = db.sections.count_documents({})
        tables_count = db.tables.count_documents({})
        registers_count = db.registers.count_documents({})

        print("数据库统计信息:")
        print(f"  章节数量: {chapters_count}")
        print(f"  小节数量: {sections_count}")
        print(f"  表格数量: {tables_count}")
        print(f"  寄存器数量: {registers_count}")

        # 显示最近更新时间
        metadata = db.metadata.find_one({})
        if metadata and 'last_updated' in metadata:
            print(f"  最近更新时间: {metadata['last_updated']}")
    except Exception as e:
        logging.error(f"显示统计信息失败: {e}")
        print(f"错误: 显示统计信息失败 - {e}")

def main():
    parser = argparse.ArgumentParser(description='LKS32AT08x_UM_v1.37 芯片手册查询工具', add_help=False)
    parser.add_argument('--show-help', action='store_true', help='显示帮助信息')
    parser.add_argument('--list-chapters', action='store_true', help='列出所有章节')
    parser.add_argument('--list-tables', action='store_true', help='列出所有表格')
    parser.add_argument('--list-registers', action='store_true', help='列出所有寄存器')
    parser.add_argument('--chapter', type=str, help='查询指定章节')
    parser.add_argument('--table', type=str, help='查询指定表格')
    parser.add_argument('--register', type=str, help='查询指定寄存器')
    parser.add_argument('--search', type=str, help='全文搜索关键词')
    parser.add_argument('--search-register', type=str, help='搜索寄存器名称')
    parser.add_argument('--search-table', type=str, help='搜索表格标题')
    parser.add_argument('--register-fuzzy', type=str, help='模糊搜索寄存器')
    parser.add_argument('--export', type=str, help='导出查询结果到JSON文件')
    parser.add_argument('--stats', action='store_true', help='显示数据库统计信息')
    parser.add_argument('--page', type=int, default=1, help='分页显示结果 (默认: 1)')
    parser.add_argument('--page-size', type=int, default=20, help='每页显示的结果数量 (默认: 20)')
    parser.add_argument('-h', '--help', action='help', help='显示此帮助信息并退出')

    args = parser.parse_args()

    if len(sys.argv) == 1 or args.show_help:
        print_help()
        return

    if args.list_chapters:
        list_chapters(args.page, args.page_size)
    elif args.list_tables:
        list_tables(args.page, args.page_size)
    elif args.list_registers:
        list_registers(args.page, args.page_size)
    elif args.chapter:
        get_chapter(args.chapter)
    elif args.table:
        get_table(args.table)
    elif args.register:
        get_register(args.register)
    elif args.search:
        search_content(args.search, args.page, args.page_size)
    elif args.search_register:
        search_register(args.search_register, args.page, args.page_size)
    elif args.search_table:
        search_table(args.search_table, args.page, args.page_size)
    elif args.register_fuzzy:
        register_fuzzy_search(args.register_fuzzy, args.page, args.page_size)
    elif args.stats:
        show_stats()
    else:
        print("无效的命令或参数不足")
        print_help()

if __name__ == "__main__":
    main()
