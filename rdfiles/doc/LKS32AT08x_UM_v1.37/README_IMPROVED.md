# LKS32AT08x_UM_v1.37 芯片手册数据库 (改进版)

本数据库包含LKS32AT08x系列芯片的用户手册信息，以MongoDB数据库形式存储，便于查询和检索。此版本是对原始版本的改进，解决了数据截断和查询问题。

## 改进内容

1. **数据导入优化**：
   - 改进表格解析算法，解决数据截断问题
   - 增加章节与表格、寄存器的关联
   - 添加数据验证和日志记录
   - 优化寄存器位字段解析

2. **查询工具增强**：
   - 添加模糊搜索功能
   - 支持分页显示结果
   - 使用表格格式化输出
   - 增加数据导出功能
   - 添加错误处理和日志记录

3. **性能优化**：
   - 添加数据库索引
   - 优化查询性能
   - 减少重复查询

## 安装与设置

### 前提条件

- Python 3.6+
- MongoDB 4.0+

### 安装步骤

1. 运行设置脚本安装依赖：

```bash
python setup.py
```

2. 导入数据到MongoDB：

```bash
python improved_import_to_mongodb.py
```

## 数据库结构

数据库名称：`LKS32AT08x_UM_v1_37`

### 集合结构

1. **metadata** - 存储文档元数据
   - `title`: 文档标题
   - `version`: 文档版本
   - `manufacturer`: 制造商
   - `copyright`: 版权信息
   - `last_updated`: 最后更新时间

2. **chapters** - 存储章节信息
   - `chapter_id`: 章节ID (如 "5.1")
   - `title`: 章节标题
   - `content`: 章节内容
   - `related_tables`: 相关表格ID列表
   - `related_registers`: 相关寄存器ID列表

3. **sections** - 存储小节信息
   - `section_id`: 小节ID (如 "5.1.1")
   - `chapter_id`: 所属章节ID
   - `title`: 小节标题
   - `content`: 小节内容

4. **tables** - 存储表格信息
   - `table_id`: 表格ID (如 "5-1")
   - `title`: 表格标题
   - `chapter_id`: 所属章节ID
   - `headers`: 表头行
   - `rows`: 表格数据行

5. **figures** - 存储图片信息
   - `figure_id`: 图片ID (如 "5-1")
   - `title`: 图片标题
   - `image_path`: 图片路径
   - `chapter_id`: 所属章节ID

6. **registers** - 存储寄存器信息
   - `register_id`: 寄存器ID (如 "5.2.13")
   - `name`: 寄存器名称
   - `description`: 寄存器描述
   - `chapter_id`: 所属章节ID
   - `address`: 寄存器地址
   - `bits`: 寄存器位定义数组
     - `high`: 位高位
     - `low`: 位低位
     - `name`: 位名称
     - `description`: 位描述

## 查询示例

### 使用改进的命令行工具查询

```bash
# 显示帮助信息
python improved_query_manual.py --help

# 列出所有章节
python improved_query_manual.py --list-chapters

# 列出所有表格
python improved_query_manual.py --list-tables

# 列出所有寄存器
python improved_query_manual.py --list-registers

# 查询指定章节
python improved_query_manual.py --chapter 5.1

# 查询指定表格
python improved_query_manual.py --table 5-1

# 查询指定寄存器
python improved_query_manual.py --register 5.2.13

# 全文搜索关键词
python improved_query_manual.py --search 时钟

# 搜索寄存器名称
python improved_query_manual.py --search-register ADC

# 搜索表格标题
python improved_query_manual.py --search-table 时钟

# 模糊搜索寄存器
python improved_query_manual.py --register-fuzzy MCPWM

# 分页显示结果
python improved_query_manual.py --list-registers --page 2 --page-size 10

# 显示数据库统计信息
python improved_query_manual.py --stats

# 导出查询结果到JSON文件
python improved_query_manual.py --register MCPWM_TH --export mcpwm_th.json
```

### 使用MongoDB查询

您也可以直接使用MongoDB命令进行查询：

```javascript
// 连接到数据库
use LKS32AT08x_UM_v1_37

// 查询元数据
db.metadata.find()

// 查询章节
db.chapters.find({chapter_id: "5.1"})

// 查询表格
db.tables.find({table_id: "5-1"})

// 查询寄存器
db.registers.find({register_id: "5.2.13"})

// 全文搜索
db.chapters.find({$or: [
  {title: {$regex: "时钟", $options: "i"}},
  {content: {$regex: "时钟", $options: "i"}}
]})

// 搜索寄存器
db.registers.find({name: {$regex: "ADC", $options: "i"}})

// 搜索表格
db.tables.find({title: {$regex: "时钟", $options: "i"}})

// 模糊搜索寄存器
db.registers.find({$or: [
  {register_id: {$regex: ".*MCPWM.*", $options: "i"}},
  {name: {$regex: ".*MCPWM.*", $options: "i"}}
]})
```

## 常用查询提示词

以下是一些常用的查询提示词，可以帮助您快速找到所需信息：

1. **时钟相关**：
   - 时钟源
   - 时钟频率
   - PLL
   - HSI
   - LSI
   - HSE

2. **寄存器相关**：
   - ADC寄存器
   - GPIO寄存器
   - 时钟控制寄存器
   - 复位寄存器
   - 中断寄存器
   - MCPWM寄存器

3. **外设相关**：
   - ADC
   - DAC
   - GPIO
   - UART
   - SPI
   - I2C
   - CAN
   - MCPWM
   - TIMER
   - HALL

4. **功能相关**：
   - 休眠
   - 唤醒
   - 复位
   - 中断
   - DMA
   - 电源管理
   - 时钟管理
   - PWM配置

## 日志和错误处理

- 导入脚本会生成`import_log.txt`日志文件，记录导入过程
- 查询工具会生成`query_log.txt`日志文件，记录查询操作
- 错误信息会同时显示在控制台和记录到日志文件中

## 数据库维护

如需更新数据库，可以修改`improved_import_to_mongodb.py`脚本并重新运行：

```bash
python improved_import_to_mongodb.py
```

## 注意事项

1. 确保MongoDB服务已启动
2. 数据库名称中不能包含点号，因此使用下划线替代
3. 查询时注意区分章节ID和表格ID的格式（章节使用点号，表格使用连字符）
4. 大型查询可能需要使用分页功能以提高性能
