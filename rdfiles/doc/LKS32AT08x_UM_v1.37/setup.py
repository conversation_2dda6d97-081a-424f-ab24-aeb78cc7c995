#!/usr/bin/env python3
import subprocess
import sys
import os

def install_dependencies():
    """安装所需的依赖包"""
    print("正在安装依赖包...")
    
    dependencies = [
        "pymongo",
        "tabulate",
        "argparse"
    ]
    
    for dep in dependencies:
        print(f"安装 {dep}...")
        try:
            subprocess.check_call([sys.executable, "-m", "pip", "install", dep])
            print(f"{dep} 安装成功")
        except subprocess.CalledProcessError as e:
            print(f"安装 {dep} 失败: {e}")
            return False
    
    print("所有依赖包安装完成")
    return True

def check_mongodb():
    """检查MongoDB是否已安装并运行"""
    print("检查MongoDB状态...")
    
    try:
        # 尝试连接MongoDB
        result = subprocess.run(["mongosh", "--eval", "db.version()"], 
                               stdout=subprocess.PIPE, 
                               stderr=subprocess.PIPE,
                               text=True)
        
        if result.returncode == 0:
            print("MongoDB已安装并运行")
            return True
        else:
            print("MongoDB可能未运行，请启动MongoDB服务")
            return False
    except FileNotFoundError:
        print("未找到MongoDB客户端工具，请确保MongoDB已安装")
        return False

def main():
    """主函数"""
    print("LKS32AT08x_UM_v1.37 芯片手册数据库设置")
    print("======================================")
    
    # 安装依赖
    if not install_dependencies():
        print("依赖安装失败，请手动安装所需依赖")
        return
    
    # 检查MongoDB
    if not check_mongodb():
        print("MongoDB检查失败，请确保MongoDB已安装并运行")
        return
    
    # 提示用户导入数据
    print("\n设置完成！")
    print("您现在可以运行以下命令导入数据:")
    print("  python improved_import_to_mongodb.py")
    print("\n导入完成后，可以使用以下命令查询数据:")
    print("  python improved_query_manual.py --help")

if __name__ == "__main__":
    main()
