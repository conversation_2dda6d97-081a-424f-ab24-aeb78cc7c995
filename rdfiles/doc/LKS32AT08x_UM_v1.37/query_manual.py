#!/usr/bin/env python3
import sys
import re
from pymongo import MongoClient

# 连接到MongoDB
client = MongoClient('localhost', 27017)
db = client['LKS32AT08x_UM_v1_37']

def print_help():
    """打印帮助信息"""
    print("LKS32AT08x_UM_v1.37 芯片手册查询工具")
    print("用法:")
    print("  python query_manual.py [选项] [查询内容]")
    print("\n选项:")
    print("  --help                显示此帮助信息")
    print("  --list-chapters       列出所有章节")
    print("  --list-tables         列出所有表格")
    print("  --list-registers      列出所有寄存器")
    print("  --chapter [ID]        查询指定章节")
    print("  --table [ID]          查询指定表格")
    print("  --register [ID]       查询指定寄存器")
    print("  --search [关键词]     全文搜索关键词")
    print("  --search-register [关键词] 搜索寄存器名称")
    print("  --search-table [关键词]    搜索表格标题")
    print("\n示例:")
    print("  python query_manual.py --list-chapters")
    print("  python query_manual.py --chapter 5.1")
    print("  python query_manual.py --search 时钟")
    print("  python query_manual.py --search-register ADC")

def list_chapters():
    """列出所有章节"""
    chapters = db.chapters.find({}, {"chapter_id": 1, "title": 1}).sort("chapter_id", 1)
    print("章节列表:")
    for chapter in chapters:
        print(f"{chapter['chapter_id']} - {chapter['title']}")

def list_tables():
    """列出所有表格"""
    tables = db.tables.find({}, {"table_id": 1, "title": 1}).sort("table_id", 1)
    print("表格列表:")
    for table in tables:
        print(f"{table['table_id']} - {table['title']}")

def list_registers():
    """列出所有寄存器"""
    registers = db.registers.find({}, {"register_id": 1, "name": 1}).sort("register_id", 1)
    print("寄存器列表:")
    for register in registers:
        print(f"{register['register_id']} - {register['name']}寄存器")

def get_chapter(chapter_id):
    """获取指定章节内容"""
    chapter = db.chapters.find_one({"chapter_id": chapter_id})
    if chapter:
        print(f"章节 {chapter['chapter_id']} - {chapter['title']}")
        print("\n".join(chapter['content']))
    else:
        print(f"未找到章节 {chapter_id}")

def get_table(table_id):
    """获取指定表格内容"""
    table = db.tables.find_one({"table_id": table_id})
    if table:
        print(f"表格 {table['table_id']} - {table['title']}")
        
        # 打印表头
        header_row = " | ".join(table['headers'])
        print(header_row)
        print("-" * len(header_row))
        
        # 打印表格内容
        for row in table['rows']:
            print(" | ".join(row))
    else:
        print(f"未找到表格 {table_id}")

def get_register(register_id):
    """获取指定寄存器内容"""
    register = db.registers.find_one({"register_id": register_id})
    if register:
        print(f"寄存器 {register['register_id']} - {register['name']}寄存器")
        print(f"地址: {register['address']}")
        print(register['description'])
    else:
        print(f"未找到寄存器 {register_id}")

def search_content(keyword):
    """全文搜索关键词"""
    # 搜索章节
    chapters = db.chapters.find({"$or": [
        {"title": {"$regex": keyword, "$options": "i"}},
        {"content": {"$regex": keyword, "$options": "i"}}
    ]})
    
    # 搜索表格
    tables = db.tables.find({"$or": [
        {"title": {"$regex": keyword, "$options": "i"}},
        {"headers": {"$regex": keyword, "$options": "i"}},
        {"rows": {"$regex": keyword, "$options": "i"}}
    ]})
    
    # 搜索寄存器
    registers = db.registers.find({"$or": [
        {"name": {"$regex": keyword, "$options": "i"}},
        {"description": {"$regex": keyword, "$options": "i"}}
    ]})
    
    print(f"搜索结果 - 关键词: '{keyword}'")
    
    print("\n章节匹配:")
    for chapter in chapters:
        print(f"{chapter['chapter_id']} - {chapter['title']}")
    
    print("\n表格匹配:")
    for table in tables:
        print(f"{table['table_id']} - {table['title']}")
    
    print("\n寄存器匹配:")
    for register in registers:
        print(f"{register['register_id']} - {register['name']}寄存器")

def search_register(keyword):
    """搜索寄存器名称"""
    registers = db.registers.find({
        "name": {"$regex": keyword, "$options": "i"}
    })
    
    print(f"寄存器搜索结果 - 关键词: '{keyword}'")
    for register in registers:
        print(f"{register['register_id']} - {register['name']}寄存器")

def search_table(keyword):
    """搜索表格标题"""
    tables = db.tables.find({
        "title": {"$regex": keyword, "$options": "i"}
    })
    
    print(f"表格搜索结果 - 关键词: '{keyword}'")
    for table in tables:
        print(f"{table['table_id']} - {table['title']}")

def main():
    """主函数"""
    if len(sys.argv) < 2 or sys.argv[1] == "--help":
        print_help()
        return
    
    command = sys.argv[1]
    
    if command == "--list-chapters":
        list_chapters()
    elif command == "--list-tables":
        list_tables()
    elif command == "--list-registers":
        list_registers()
    elif command == "--chapter" and len(sys.argv) > 2:
        get_chapter(sys.argv[2])
    elif command == "--table" and len(sys.argv) > 2:
        get_table(sys.argv[2])
    elif command == "--register" and len(sys.argv) > 2:
        get_register(sys.argv[2])
    elif command == "--search" and len(sys.argv) > 2:
        search_content(sys.argv[2])
    elif command == "--search-register" and len(sys.argv) > 2:
        search_register(sys.argv[2])
    elif command == "--search-table" and len(sys.argv) > 2:
        search_table(sys.argv[2])
    else:
        print("无效的命令或参数不足")
        print_help()

if __name__ == "__main__":
    main()
