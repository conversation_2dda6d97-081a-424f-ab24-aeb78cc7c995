#!/usr/bin/env python3
import re
import json
import os
from pymongo import MongoClient

# 连接到MongoDB
client = MongoClient('localhost', 27017)
db = client['LKS32AT08x_UM_v1_37']

# 清空集合
db.chapters.delete_many({})
db.sections.delete_many({})
db.tables.delete_many({})
db.figures.delete_many({})
db.registers.delete_many({})
db.metadata.delete_many({})

# 读取Markdown文件
with open('full.md', 'r', encoding='utf-8') as f:
    content = f.read()

# 读取JSON文件
with open('139d2e31-21fa-4ecb-88e1-5509214a70a7_content_list.json', 'r', encoding='utf-8') as f:
    json_content = json.load(f)

# 添加元数据
metadata = {
    "title": "LKS32AT08x User Manual",
    "version": "v1.37",
    "manufacturer": "南京凌鸥创芯电子有限公司",
    "copyright": "2019，版权归凌鸥创芯所有机密文件，未经许可不得扩散"
}
db.metadata.insert_one(metadata)

# 解析章节
chapter_pattern = r'# (\d+(?:\.\d+)*)\s+(.*?)\s*\n'
chapters = re.findall(chapter_pattern, content)

for chapter_num, chapter_title in chapters:
    chapter_doc = {
        "chapter_id": chapter_num,
        "title": chapter_title,
        "content": []
    }
    
    # 查找章节内容
    chapter_start = content.find(f'# {chapter_num} {chapter_title}')
    next_chapter = re.search(r'# (\d+(?:\.\d+)*)\s+(.*?)\s*\n', content[chapter_start + 1:])
    
    if next_chapter:
        chapter_end = chapter_start + 1 + next_chapter.start()
        chapter_content = content[chapter_start:chapter_end]
    else:
        chapter_content = content[chapter_start:]
    
    # 存储章节内容
    chapter_doc["content"] = chapter_content.split('\n')
    
    # 插入章节
    chapter_id = db.chapters.insert_one(chapter_doc).inserted_id
    
    # 解析小节
    section_pattern = r'# (\d+\.\d+(?:\.\d+)*)\s+(.*?)\s*\n'
    sections = re.findall(section_pattern, chapter_content)
    
    for section_num, section_title in sections:
        if not section_num.startswith(chapter_num + '.'):
            continue
            
        section_doc = {
            "chapter_id": chapter_num,
            "section_id": section_num,
            "title": section_title,
            "content": []
        }
        
        # 查找小节内容
        section_start = chapter_content.find(f'# {section_num} {section_title}')
        next_section = re.search(r'# (\d+\.\d+(?:\.\d+)*)\s+(.*?)\s*\n', chapter_content[section_start + 1:])
        
        if next_section:
            section_end = section_start + 1 + next_section.start()
            section_content = chapter_content[section_start:section_end]
        else:
            section_content = chapter_content[section_start:]
        
        # 存储小节内容
        section_doc["content"] = section_content.split('\n')
        
        # 插入小节
        db.sections.insert_one(section_doc)

# 解析表格
table_pattern = r'表\s+(\d+(?:-\d+)?)\s+(.*?)\s*\n\s*\n\s*<html><body><table>(.*?)</table></body></html>'
tables = re.findall(table_pattern, content, re.DOTALL)

for table_num, table_title, table_content in tables:
    # 解析表格内容
    rows = re.findall(r'<tr>(.*?)</tr>', table_content, re.DOTALL)
    parsed_rows = []
    
    for row in rows:
        cells = re.findall(r'<td>(.*?)</td>', row, re.DOTALL)
        parsed_rows.append(cells)
    
    table_doc = {
        "table_id": table_num,
        "title": table_title,
        "headers": parsed_rows[0] if parsed_rows else [],
        "rows": parsed_rows[1:] if len(parsed_rows) > 1 else []
    }
    
    # 插入表格
    db.tables.insert_one(table_doc)

# 解析图片
figure_pattern = r'图\s+(\d+(?:-\d+)?)\s+(.*?)\s*\n'
figures = re.findall(figure_pattern, content)

for figure_num, figure_title in figures:
    figure_doc = {
        "figure_id": figure_num,
        "title": figure_title,
        "image_path": f"images/{figure_num}.jpg"  # 假设图片路径
    }
    
    # 插入图片
    db.figures.insert_one(figure_doc)

# 解析寄存器
register_pattern = r'(\d+\.\d+\.\d+(?:\.\d+)*)\s+(.*?)寄存器\s*\n'
registers = re.findall(register_pattern, content)

for register_id, register_name in registers:
    # 查找寄存器描述
    register_start = content.find(f'{register_id} {register_name}寄存器')
    next_register = re.search(r'(\d+\.\d+\.\d+(?:\.\d+)*)\s+(.*?)寄存器\s*\n', content[register_start + 1:])
    
    if next_register:
        register_end = register_start + 1 + next_register.start()
        register_content = content[register_start:register_end]
    else:
        register_content = content[register_start:register_start + 1000]  # 限制长度
    
    register_doc = {
        "register_id": register_id,
        "name": register_name,
        "description": register_content,
        "address": "",  # 需要进一步解析
        "bits": []  # 需要进一步解析
    }
    
    # 尝试解析地址
    address_match = re.search(r'地址[：:]\s*0x([0-9A-Fa-f]+)', register_content)
    if address_match:
        register_doc["address"] = "0x" + address_match.group(1)
    
    # 插入寄存器
    db.registers.insert_one(register_doc)

print("导入完成！")
