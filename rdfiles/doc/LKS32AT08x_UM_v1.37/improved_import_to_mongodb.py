#!/usr/bin/env python3
import re
import json
import os
import logging
from pymongo import MongoClient, ASCENDING, TEXT

# 设置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler("import_log.txt"),
        logging.StreamHandler()
    ]
)

def main():
    # 连接到MongoDB
    try:
        client = MongoClient('localhost', 27017)
        db = client['LKS32AT08x_UM_v1_37']
        logging.info("成功连接到MongoDB")
    except Exception as e:
        logging.error(f"连接MongoDB失败: {e}")
        return

    # 清空集合
    collections = ['chapters', 'sections', 'tables', 'figures', 'registers', 'metadata']
    for collection in collections:
        try:
            db[collection].delete_many({})
            logging.info(f"已清空集合: {collection}")
        except Exception as e:
            logging.error(f"清空集合 {collection} 失败: {e}")

    # 读取Markdown文件
    try:
        with open('full.md', 'r', encoding='utf-8') as f:
            content = f.read()
        logging.info(f"成功读取Markdown文件，大小: {len(content)} 字节")
    except Exception as e:
        logging.error(f"读取Markdown文件失败: {e}")
        return

    # 添加元数据
    metadata = {
        "title": "LKS32AT08x User Manual",
        "version": "v1.37",
        "manufacturer": "南京凌鸥创芯电子有限公司",
        "copyright": "2019，版权归凌鸥创芯所有机密文件，未经许可不得扩散"
    }
    db.metadata.insert_one(metadata)
    logging.info("已添加元数据")

    # 解析章节
    chapter_pattern = r'# (\d+(?:\.\d+)*)\s+(.*?)\s*\n'
    chapters = re.findall(chapter_pattern, content)
    logging.info(f"找到 {len(chapters)} 个章节")

    # 预处理表格
    all_tables = []
    table_pattern = r'表\s+(\d+(?:-\d+)?)\s+(.*?)\s*\n\s*\n\s*(?:<html><body><table>|<table>)(.*?)(?:</table></body></html>|</table>)'
    tables = re.findall(table_pattern, content, re.DOTALL)
    
    for table_num, table_title, table_content in tables:
        # 解析表格内容
        rows = re.findall(r'<tr>(.*?)</tr>', table_content, re.DOTALL)
        parsed_rows = []
        
        for row in rows:
            cells = re.findall(r'<td>(.*?)</td>', row, re.DOTALL)
            parsed_rows.append(cells)
        
        table_doc = {
            "table_id": table_num,
            "title": table_title,
            "headers": parsed_rows[0] if parsed_rows else [],
            "rows": parsed_rows[1:] if len(parsed_rows) > 1 else [],
            "chapter_id": ""  # 将在章节处理时填充
        }
        
        all_tables.append(table_doc)
    
    logging.info(f"预处理了 {len(all_tables)} 个表格")

    # 处理章节
    for chapter_num, chapter_title in chapters:
        logging.info(f"处理章节: {chapter_num} - {chapter_title}")
        
        # 查找章节内容
        chapter_start = content.find(f'# {chapter_num} {chapter_title}')
        next_chapter = re.search(r'# (\d+(?:\.\d+)*)\s+(.*?)\s*\n', content[chapter_start + 1:])
        
        if next_chapter:
            chapter_end = chapter_start + 1 + next_chapter.start()
            chapter_content = content[chapter_start:chapter_end]
        else:
            chapter_content = content[chapter_start:]
        
        # 关联表格
        related_tables = []
        for table in all_tables:
            if f"表 {table['table_id']}" in chapter_content or f"表{table['table_id']}" in chapter_content:
                table["chapter_id"] = chapter_num
                related_tables.append(table['table_id'])
        
        # 查找章节中的寄存器
        register_pattern = r'(\d+\.\d+\.\d+(?:\.\d+)*)\s+(.*?)寄存器\s*\n'
        registers = re.findall(register_pattern, chapter_content)
        
        related_registers = []
        for register_id, register_name in registers:
            related_registers.append(register_id)
            logging.info(f"  找到寄存器: {register_id} - {register_name}")
            
            # 查找寄存器描述
            register_start = chapter_content.find(f'{register_id} {register_name}寄存器')
            next_register = re.search(r'(\d+\.\d+\.\d+(?:\.\d+)*)\s+(.*?)寄存器\s*\n', chapter_content[register_start + 1:])
            
            if next_register:
                register_end = register_start + 1 + next_register.start()
                register_content = chapter_content[register_start:register_end]
            else:
                register_content = chapter_content[register_start:register_start + 3000]  # 增加长度限制
            
            register_doc = {
                "register_id": register_id,
                "name": register_name,
                "description": register_content,
                "chapter_id": chapter_num,
                "address": "",
                "bits": []
            }
            
            # 尝试解析地址
            address_match = re.search(r'地址[：:]\s*0x([0-9A-Fa-f]+)', register_content)
            if address_match:
                register_doc["address"] = "0x" + address_match.group(1)
            
            # 尝试解析位定义
            bits_pattern = r'<tr><td>\[(\d+)(?::(\d+))?\]</td><td>(.*?)</td><td>(.*?)</td>'
            bits = re.findall(bits_pattern, register_content, re.DOTALL)
            
            for bit_match in bits:
                bit_high = bit_match[0]
                bit_low = bit_match[1] if len(bit_match) > 1 and bit_match[1] else bit_high
                bit_name = bit_match[2] if len(bit_match) > 2 else ""
                bit_desc = bit_match[3] if len(bit_match) > 3 else ""
                
                bit_doc = {
                    "high": int(bit_high),
                    "low": int(bit_low) if bit_low else int(bit_high),
                    "name": bit_name,
                    "description": bit_desc
                }
                register_doc["bits"].append(bit_doc)
            
            # 插入寄存器
            try:
                db.registers.insert_one(register_doc)
            except Exception as e:
                logging.error(f"插入寄存器 {register_id} 失败: {e}")
        
        # 存储章节内容
        chapter_doc = {
            "chapter_id": chapter_num,
            "title": chapter_title,
            "content": chapter_content.split('\n'),
            "related_tables": related_tables,
            "related_registers": related_registers
        }
        
        # 插入章节
        try:
            chapter_id = db.chapters.insert_one(chapter_doc).inserted_id
            logging.info(f"  插入章节: {chapter_num}")
        except Exception as e:
            logging.error(f"插入章节 {chapter_num} 失败: {e}")
        
        # 解析小节
        section_pattern = r'# (\d+\.\d+(?:\.\d+)*)\s+(.*?)\s*\n'
        sections = re.findall(section_pattern, chapter_content)
        
        for section_num, section_title in sections:
            if not section_num.startswith(chapter_num + '.'):
                continue
                
            logging.info(f"  处理小节: {section_num} - {section_title}")
            
            section_doc = {
                "chapter_id": chapter_num,
                "section_id": section_num,
                "title": section_title,
                "content": []
            }
            
            # 查找小节内容
            section_start = chapter_content.find(f'# {section_num} {section_title}')
            next_section = re.search(r'# (\d+\.\d+(?:\.\d+)*)\s+(.*?)\s*\n', chapter_content[section_start + 1:])
            
            if next_section:
                section_end = section_start + 1 + next_section.start()
                section_content = chapter_content[section_start:section_end]
            else:
                section_content = chapter_content[section_start:]
            
            # 存储小节内容
            section_doc["content"] = section_content.split('\n')
            
            # 插入小节
            try:
                db.sections.insert_one(section_doc)
                logging.info(f"    插入小节: {section_num}")
            except Exception as e:
                logging.error(f"插入小节 {section_num} 失败: {e}")

    # 插入所有表格
    for table in all_tables:
        try:
            db.tables.insert_one(table)
            logging.info(f"插入表格: {table['table_id']}")
        except Exception as e:
            logging.error(f"插入表格 {table['table_id']} 失败: {e}")

    # 创建索引
    try:
        db.chapters.create_index([("chapter_id", ASCENDING)], unique=True)
        db.sections.create_index([("section_id", ASCENDING)], unique=True)
        db.tables.create_index([("table_id", ASCENDING)], unique=True)
        db.registers.create_index([("register_id", ASCENDING)], unique=True)
        
        # 创建文本索引
        db.chapters.create_index([("title", TEXT), ("content", TEXT)])
        db.registers.create_index([("name", TEXT), ("description", TEXT)])
        db.tables.create_index([("title", TEXT)])
        
        logging.info("创建索引完成")
    except Exception as e:
        logging.error(f"创建索引失败: {e}")

    # 验证导入结果
    validate_import(db)

    logging.info("导入完成！")

def validate_import(db):
    """验证导入结果"""
    chapters_count = db.chapters.count_documents({})
    sections_count = db.sections.count_documents({})
    tables_count = db.tables.count_documents({})
    registers_count = db.registers.count_documents({})
    
    logging.info(f"验证结果:")
    logging.info(f"  章节数量: {chapters_count}")
    logging.info(f"  小节数量: {sections_count}")
    logging.info(f"  表格数量: {tables_count}")
    logging.info(f"  寄存器数量: {registers_count}")
    
    # 检查是否有空章节
    empty_chapters = db.chapters.count_documents({"content": {"$size": 0}})
    if empty_chapters > 0:
        logging.warning(f"发现 {empty_chapters} 个空章节")
    
    # 检查是否有空表格
    empty_tables = db.tables.count_documents({"rows": {"$size": 0}})
    if empty_tables > 0:
        logging.warning(f"发现 {empty_tables} 个空表格")

if __name__ == "__main__":
    main()
