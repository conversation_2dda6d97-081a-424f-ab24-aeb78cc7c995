import asyncio
import json
import textwrap
from autogen_agentchat.agents import Assistant<PERSON><PERSON>, UserProxyAgent
from autogen_agentchat.team import Team
from autogen_ext.tools.mcp import StdioServerParams, mcp_server_tools
from autogen_core.models import ModelInfo, ModelFamily
from autogen_ext.models.ollama import OllamaChatCompletionClient

MONGO_URI = "mongodb://localhost:27017/LKS32AT08x_UM_v1_37"
OLLAMA_TAG = "qwen3:14b-fp16"

async def setup_agents():
    """设置查询助手和用户代理"""
    # 加载 mongodb 工具
    mongo_tools = await mcp_server_tools(
        StdioServerParams(
            command="npx",
            args=["-y", "mcp-mongo-server", MONGO_URI, "--log-level", "debug"],
            read_timeout_seconds=90,
        )
    )
    
    # 配置大模型
    model = OllamaChatCompletionClient(
        model=OLLAMA_TAG,
        host="http://localhost:11434",
        model_info=ModelInfo(
            vision=False,
            function_calling=True,
            structured_output=True,
            json_output=True,
            family=ModelFamily.UNKNOWN,
        ),
        default_params={"temperature": 0, "output_format": "json", "max_tokens": 8192},
    )
    
    # 查询助手代理
    assistant = AssistantAgent(
        name="manual_query_assistant",
        model_client=model,
        tools=mongo_tools,
        system_message=textwrap.dedent("""
            你是 LKS32AT08x_UM_v1.37 芯片手册数据库的查询助手，使用 find() 工具查询 MongoDB 数据库（LKS32AT08x_UM_v1_37）。
            数据库包含以下集合：
            - metadata: {title, version, manufacturer, copyright}
            - chapters: {chapter_id, title, content}
            - tables: {table_id, title, headers, rows}
            - figures: {figure_id, title, image_path}
            - registers: {register_id, name, description, address, bits}

            任务：解析用户提问，生成 MongoDB 查询，返回 JSON 格式结果。
            支持的查询类型：
            1. 列出所有记录：
               - 提问：列出所有章节/表格/寄存器
               - 查询：find({'collection': 'chapters/tables/registers', 'query': {}})
            2. 查询特定 ID：
               - 提问：查询章节 5.1 / 表格 5-1 / 寄存器 5.2.13
               - 查询：find({'collection': 'chapters/tables/registers', 'query': {'chapter_id/table_id/register_id': '5.1/5-1/5.2.13'}})
            3. 全文搜索：
               - 提问：搜索关键词“时钟”
               - 查询：find({'collection': 'chapters', 'query': {'content': {'$regex': '时钟', '$options': 'i'}}})
            4. 寄存器名称搜索：
               - 提问：搜索寄存器 ADC
               - 查询：find({'collection': 'registers', 'query': {'name': {'$regex': 'ADC', '$options': 'i'}}})
            5. 表格标题搜索：
               - 提问：搜索表格标题“时钟”
               - 查询：find({'collection': 'tables', 'query': {'title': {'$regex': '时钟', '$options': 'i'}}})
            6. 元数据查询：
               - 提问：查询手册版本
               - 查询：find({'collection': 'metadata', 'query': {}})

            规则：
            - 解析提问，确定集合和查询条件。
            - 使用 find() 工具，格式：find({'collection': '<集合名>', 'query': {...}})
            - 返回 JSON 数组，包含查询结果。
            - 如果无结果，返回 []。
            - 如果查询失败，返回 {"error": "查询失败：<原因>"}
            - 严格返回 JSON 格式，禁止输出 <think>, <tool_call> 或任何非 JSON 内容。
            - 仅返回查询结果，不解释过程。

            示例：
            提问：列出所有章节
            输出：[{"chapter_id": "1.1", "title": "引言", "content": "..."}, ...]

            提问：查询章节 5.1
            输出：[{"chapter_id": "5.1", "title": "时钟", "content": "..."}]

            提问：搜索关键词“时钟”
            输出：[{"chapter_id": "5.1", "title": "时钟", "content": "..."}, ...]
        """).strip(),
        reflect_on_tool_use=True,
    )
    
    # 用户代理
    user_proxy = UserProxyAgent(name="user_proxy")
    
    # 创建团队
    team = Team(name="query_team", agents=[user_proxy, assistant])
    
    return team, assistant

async def main():
    team, assistant = await setup_agents()
    
    print("欢迎查询 LKS32AT08x_UM_v1.37 芯片手册数据库！")
    print("示例提问：")
    print("- 列出所有章节")
    print("- 查询章节 5.1")
    print("- 搜索关键词“时钟”")
    print("- 搜索寄存器 ADC")
    print("输入 'exit' 退出")
    
    while True:
        task = input("\n请输入查询：")
        if task.lower() == "exit":
            break
        
        # 直接调用 AssistantAgent 的 run 方法
        res = await assistant.run(task=task)
        
        # 提取助手回复
        try:
            result = json.loads(res.messages[-1].content)
            print("\n🟢 查询结果：\n", json.dumps(result, indent=2, ensure_ascii=False))
        except json.JSONDecodeError:
            print("\n🔴 输出非JSON：", res.messages[-1].content)

if __name__ == "__main__":
    asyncio.run(main())