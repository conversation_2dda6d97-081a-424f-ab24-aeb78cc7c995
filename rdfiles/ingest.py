# ingest.py  —— 运行环境：主 AutoGen venv
import os, json, glob, pathlib, requests
from pymongo import MongoClient
from autogen_ext.models.ollama import OllamaChatCompletionClient

# 0. 参数
DOC_DIR   = "/Users/<USER>/Public/AutoGen/rdfiles/doc/LKS32AT08x_UM_v1.37/full.md"              # 你已放好的 md/json
MONGO_URI = "mongodb://localhost:27017"
DB_NAME   = "LKS32AT08x_UM_v1_37_Autogen"
EMB_MODEL = "nomic-embed-text"                # Ollama embedding 模型&#8203;:contentReference[oaicite:6]{index=6}

ollama = OllamaChatCompletionClient(model=EMB_MODEL,
                                    client_host="http://localhost:11434/v1",
                                    api_key="ollama")
db = MongoClient(MONGO_URI)[DB_NAME]

def embed(text):                              # 生成 768 维向量&#8203;:contentReference[oaicite:7]{index=7}
    return ollama.embed(text)["embedding"]

def load_and_insert_md():
    for md in glob.glob(f"{DOC_DIR}/**/*.md", recursive=True):
        chap_id = pathlib.Path(md).stem        # 假设文件名=章节号
        content = open(md).read()
        doc = {"chapter_id": chap_id,
               "title": chap_id,
               "content": content,
               "embedding": embed(content)}
        db.chapters.insert_one(doc)            # 单章写入

def load_and_insert_json():
    for js in glob.glob(f"{DOC_DIR}/**/*register*.json", recursive=True):
        data = json.load(open(js))
        for reg in data:
            reg["embedding"] = embed(reg["description"])
        db.registers.insert_many(data)         # 批量写入&#8203;:contentReference[oaicite:8]{index=8}

if __name__ == "__main__":
    load_and_insert_md()
    load_and_insert_json()
