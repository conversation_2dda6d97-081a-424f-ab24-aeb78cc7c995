import asyncio
import json
import textwrap
from autogen_agentchat.agents import Assistant<PERSON>gent
from autogen_ext.tools.mcp import StdioServerParams, mcp_server_tools
from autogen_core.models import ModelInfo, ModelFamily
from autogen_ext.models.ollama import OllamaChatCompletionClient

OLLAMA_TAG = "qwen3:14b-fp16"

async def main():
    # 加载 sequentialthinking MCP 服务器
    tools = await mcp_server_tools(
        StdioServerParams(
            command="npx",
            args=["-y", "@modelcontextprotocol/server-sequential-thinking", "--log-level", "debug"],
            read_timeout_seconds=90,
        )
    )

    # 配置大模型
    model = OllamaChatCompletionClient(
        model=OLLAMA_TAG,
        host="http://localhost:11434",
        model_info=ModelInfo(
            vision=False,
            function_calling=True,
            structured_output=True,
            json_output=True,
            family=ModelFamily.UNKNOWN,
        ),
        default_params={"temperature": 0, "output_format": "json"},
    )

    # 创建代理
    assistant = AssistantAgent(
        name="sequential_thinking_helper",
        model_client=model,
        tools=tools,
        system_message=textwrap.dedent("""
            你有 sequentialthinking 工具，可分解问题为思考步骤。
            当用户询问如何分析问题时，必须调用 sequentialthinking 工具，传递：
            - thought: 当前思考步骤描述。
            - thoughtNumber: 步骤编号（从1开始）。
            - totalThoughts: 预计总步骤数（默认3）。
            - nextThoughtNeeded: 是否需要更多步骤（true/false）。
            获取返回后，提取所有 thought 字段，组成数组回复。
            严格返回 JSON 数组，包含思考步骤，例如 ["步骤1", "步骤2", "步骤3"]。
            示例：
            任务：如何分析问题？
            1. 调用 sequentialthinking({"thought": "识别问题核心", "thoughtNumber": 1, "totalThoughts": 3, "nextThoughtNeeded": true})
            2. 调用 sequentialthinking({"thought": "分解为子问题", "thoughtNumber": 2, "totalThoughts": 3, "nextThoughtNeeded": true})
            3. 调用 sequentialthinking({"thought": "总结解决方案", "thoughtNumber": 3, "totalThoughts": 3, "nextThoughtNeeded": false})
            输出：["识别问题核心", "分解为子问题", "总结解决方案"]
        """).strip(),
        reflect_on_tool_use=True,
    )

    # 测试任务
    res = await assistant.run(task="no thinking,分析一个复杂问题有哪些步骤？（只给数组）")
    print("\n🟢 LLM 回复：\n", res.messages[-1].content)

if __name__ == "__main__":
    asyncio.run(main())