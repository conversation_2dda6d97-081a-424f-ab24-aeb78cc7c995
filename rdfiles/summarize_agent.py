import asyncio
import textwrap
from autogen_agentchat.agents import Assistant<PERSON><PERSON>
from autogen_ext.tools.mcp import StdioServerParams, mcp_server_tools
from autogen_core.models import ModelInfo, ModelFamily
from autogen_ext.models.ollama import OllamaChatCompletionClient

FILESYSTEM_PATH = "/Users/<USER>/Public/AutoGen/rdfiles/doc/LKS32AT08x_UM_v1.37"
OLLAMA_TAG = "mistral-small:24b"
TARGET_FILE = "full.md"

async def chunk_markdown(content: str, max_length: int = 2000) -> list:
    """将Markdown内容分割成块，每块不超过max_length字符"""
    chunks = []
    current_chunk = ""
    lines = content.split("\n")
    for line in lines:
        if len(current_chunk) + len(line) + 1 <= max_length:
            current_chunk += line + "\n"
        else:
            if current_chunk:
                chunks.append(current_chunk.strip())
            current_chunk = line + "\n"
    if current_chunk:
        chunks.append(current_chunk.strip())
    return chunks

async def setup_structure_analyzer():
    """设置结构分析代理，逐块分析手册内容"""
    filesystem_tools = await mcp_server_tools(
        StdioServerParams(
            command="npx",
            args=["-y", "@modelcontextprotocol/server-filesystem", FILESYSTEM_PATH],
            read_timeout_seconds=90,
        )
    )
    model = OllamaChatCompletionClient(
        model=OLLAMA_TAG,
        host="http://localhost:11434",
        model_info=ModelInfo(
            vision=False,
            function_calling=True,
            structured_output=True,
            json_output=True,
            family=ModelFamily.UNKNOWN,
        ),
        default_params={"temperature": 0},
    )
    assistant = AssistantAgent(
        name="structure_analyzer",
        model_client=model,
        tools=filesystem_tools,
        system_message=textwrap.dedent("""
            你有 readFile() 工具，可读取Markdown文件。
            任务：逐块分析芯片手册内容，提取结构化信息，输出JSON格式。
            每块处理时，输出“正在阅读块 <块序号> 中”。
            最终返回：
            [
              {
                "file_name": "<文件名>",
                "section": "<章节标题，例如 '1. 引言'>",
                "subsection": "<子章节标题，例如 '1.1 芯片概述'，若无则为空>",
                "keywords": ["<关键词1>", "<关键词2>", ...],
                "content": "<该章节或子章节的总结，限制200字>",
                "order": <章节顺序，整数，例如 1, 2, 3...>
              },
              ...
            ]
            步骤：
            1. 使用 readFile({'path': '%s/%s'}) 读取Markdown文件。
            2. 将内容分块（每块约2000字符）。
            3. 对每块：
               - 输出“正在阅读块 <块序号> 中”。
               - 提取章节和子章节信息：
                 - file_name：文件名。
                 - section：顶级章节标题（# 级别）。
                 - subsection：子章节标题（## 级别）。
                 - keywords：提取3-5个与芯片功能、外设、寄存器相关的关键词。
                 - content：总结该章节/子章节的核心内容，突出功能、寄存器或配置，限制200字。
                 - order：基于章节出现顺序（跨块连续编号）。
            4. 合并所有分块的结果，按order排序，返回JSON列表。
            严格基于文件内容提取信息，不生成额外内容。
            如果未找到章节结构，返回 {"error": "未找到章节结构"}。
        """ % (FILESYSTEM_PATH, TARGET_FILE)).strip(),
        reflect_on_tool_use=True,
    )
    return assistant

async def main():
    analyzer = await setup_structure_analyzer()
    res = await analyzer.run(
        task=f"逐块分析 {FILESYSTEM_PATH}/{TARGET_FILE} 的内容，输出JSON格式的数据库记录"
    )
    print("\n🟢 结构分析代理回复：\n", res.messages[-1].content)

if __name__ == "__main__":
    asyncio.run(main())