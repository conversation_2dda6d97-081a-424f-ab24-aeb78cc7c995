import asyncio
import json
import ast
import textwrap
from autogen_agentchat.agents import Assistant<PERSON><PERSON>
from autogen_ext.tools.mcp import StdioServerParams, mcp_server_tools
from autogen_core.models import ModelInfo, ModelFamily
from autogen_ext.models.ollama import OllamaChatCompletionClient

FILESYSTEM_PATH = "/Users/<USER>/Public/AutoGen/rdfiles/doc"
OLLAMA_TAG = "qwen3:14b-fp16"

async def main():
    # Configure the Filesystem MCP server
    tools = await mcp_server_tools(
        StdioServerParams(
            command="npx",
            args=[
                "-y",
                "@modelcontextprotocol/server-filesystem",
                FILESYSTEM_PATH
            ],
            read_timeout_seconds=90,
        )
    )

    # Initialize the Ollama model client
    model = OllamaChatCompletionClient(
        model=OLLAMA_TAG,
        host="http://localhost:11434",
        model_info=ModelInfo(
            vision=False,
            function_calling=True,
            structured_output=True,
            json_output=True,
            family=ModelFamily.UNKNOWN,
        ),
        default_params={"temperature": 0},
    )

    # Create the AssistantAgent
    assistant = AssistantAgent(
        name="filesystem_helper",
        model_client=model,
        tools=tools,
        system_message=textwrap.dedent("""
            你有 listFiles() 工具，可列出指定目录中的文件。
            当用户询问目录中的文件时，必须调用该工具（arguments={'path': '%s'}）。
            获取到原始返回后，只需要把 "name" 字段组成数组回复。
        """ % FILESYSTEM_PATH).strip(),
        reflect_on_tool_use=True,
    )

    # Run the task to list files
    res = await assistant.run(
        task="no thinking,列出 %s 目录有哪些文件？（只给数组）" % FILESYSTEM_PATH
    )
    print("\n🟢 LLM 回复：\n", res.messages[-1].content)

if __name__ == "__main__":
    asyncio.run(main())